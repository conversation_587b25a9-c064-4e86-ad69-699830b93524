#!/bin/bash

# QOS Market API 启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查配置文件
check_config() {
    if [ ! -f "configs/config.yaml" ]; then
        print_warning "配置文件不存在，从示例文件创建..."
        cp configs/config.example.yaml configs/config.yaml
        print_warning "请编辑 configs/config.yaml 并设置您的 QOS.HK API 密钥"
        return 1
    fi
    
    # 检查API密钥是否已设置
    if grep -q "your_api_key_here" configs/config.yaml; then
        print_warning "请在 configs/config.yaml 中设置您的 QOS.HK API 密钥"
        return 1
    fi
    
    return 0
}

# 检查依赖
check_dependencies() {
    print_info "检查依赖..."
    
    if ! command -v go &> /dev/null; then
        print_error "Go 未安装，请先安装 Go 1.19 或更高版本"
        exit 1
    fi
    
    go_version=$(go version | awk '{print $3}' | sed 's/go//')
    print_info "Go 版本: $go_version"
}

# 构建应用
build_app() {
    print_info "构建应用..."
    make build
    if [ $? -eq 0 ]; then
        print_success "构建成功"
    else
        print_error "构建失败"
        exit 1
    fi
}

# 创建必要的目录
create_dirs() {
    print_info "创建必要的目录..."
    mkdir -p logs
    mkdir -p bin
}

# 启动应用
start_app() {
    print_info "启动 QOS Market API..."
    print_info "HTTP API: http://localhost:8080"
    print_info "WebSocket: ws://localhost:8080/ws"
    print_info "监控接口: http://localhost:9090"
    print_info "健康检查: http://localhost:8080/health"
    echo
    print_info "按 Ctrl+C 停止服务"
    echo
    
    ./bin/qos-market-api -config=configs/config.yaml
}

# 主函数
main() {
    echo "========================================"
    echo "    QOS Market API 启动脚本"
    echo "========================================"
    echo
    
    # 检查依赖
    check_dependencies
    
    # 创建目录
    create_dirs
    
    # 检查配置
    if ! check_config; then
        print_error "配置检查失败，请修复配置后重试"
        exit 1
    fi
    
    # 构建应用
    build_app
    
    # 启动应用
    start_app
}

# 如果直接运行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
