#!/bin/bash

# K线数据修复功能优化测试脚本

BASE_URL="http://localhost:8080"
API_URL="${BASE_URL}/api/v1"

echo "=== K线数据修复功能优化测试 ==="
echo

# 检查服务是否运行
echo "1. 检查服务状态..."
curl -s "${BASE_URL}/health" | jq '.' || {
    echo "❌ 服务未运行，请先启动服务"
    exit 1
}
echo "✅ 服务运行正常"
echo

# 测试获取支持的品种列表
echo "2. 测试获取支持的品种列表..."
SYMBOLS_RESPONSE=$(curl -s "${API_URL}/symbols")
echo "支持的品种列表:"
echo "$SYMBOLS_RESPONSE" | jq '.data[]'
echo "✅ 品种列表获取成功"
echo

# 测试添加修复数据（所有K线类型）
echo "3. 测试添加修复数据（所有K线类型）..."
CORRECTION_DATA='{
  "code": "US:AAPL",
  "kline_type": 0,
  "timestamp": **********,
  "field": "cl",
  "corr_value": "150.25",
  "reason": "测试所有K线类型修复功能"
}'

CORRECTION_RESPONSE=$(curl -s -X POST "${API_URL}/corrections" \
  -H "Content-Type: application/json" \
  -d "${CORRECTION_DATA}")

echo "修复数据添加结果:"
echo "$CORRECTION_RESPONSE" | jq '.'

if echo "$CORRECTION_RESPONSE" | jq -e '.data.kline_type == 0' > /dev/null; then
    echo "✅ 所有K线类型修复功能正常"
else
    echo "❌ 所有K线类型修复功能异常"
fi
echo

# 测试获取修复数据列表
echo "4. 测试获取修复数据列表..."
CORRECTIONS_LIST=$(curl -s "${API_URL}/corrections")
echo "修复数据列表:"
echo "$CORRECTIONS_LIST" | jq '.data[] | {id, code, kline_type, field, corr_value, reason}'
echo "✅ 修复数据列表获取成功"
echo

# 测试修复统计信息
echo "5. 测试修复统计信息..."
STATS_RESPONSE=$(curl -s "${API_URL}/corrections/stats")
echo "修复统计信息:"
echo "$STATS_RESPONSE" | jq '.data'
echo "✅ 修复统计信息获取成功"
echo

# 测试Web界面可访问性
echo "6. 测试Web界面可访问性..."
CORRECTIONS_PAGE=$(curl -s -o /dev/null -w "%{http_code}" "${BASE_URL}/corrections")
KLINE_TEST_PAGE=$(curl -s -o /dev/null -w "%{http_code}" "${BASE_URL}/kline-test")

if [ "$CORRECTIONS_PAGE" = "200" ]; then
    echo "✅ 修复管理页面可访问"
else
    echo "❌ 修复管理页面不可访问 (HTTP $CORRECTIONS_PAGE)"
fi

if [ "$KLINE_TEST_PAGE" = "200" ]; then
    echo "✅ K线测试页面可访问"
else
    echo "❌ K线测试页面不可访问 (HTTP $KLINE_TEST_PAGE)"
fi
echo

echo "=== 测试完成 ==="
echo "请手动验证以下功能："
echo "1. 访问 ${BASE_URL}/corrections 查看修复管理页面的品种下拉选择和日期选择器"
echo "2. 访问 ${BASE_URL}/kline-test 查看K线测试页面的品种下拉选择"
echo "3. 在修复管理页面添加修复数据，验证默认选择'所有K线类型'"
echo "4. 验证修复数据在列表中正确显示为'所有K线类型'"
