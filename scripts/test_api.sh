#!/bin/bash

# QOS Market API 测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 服务器地址
API_BASE="http://localhost:8080"
MONITOR_BASE="http://localhost:9090"

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务是否运行
check_service() {
    print_info "检查服务状态..."
    
    if curl -s "$API_BASE/health" > /dev/null; then
        print_success "API 服务运行正常"
    else
        print_error "API 服务未运行，请先启动服务"
        print_info "运行命令: ./scripts/start.sh"
        exit 1
    fi
    
    if curl -s "$MONITOR_BASE/health" > /dev/null; then
        print_success "监控服务运行正常"
    else
        print_warning "监控服务未运行"
    fi
}

# 测试健康检查
test_health() {
    print_info "测试健康检查接口..."
    
    response=$(curl -s "$API_BASE/health")
    echo "响应: $response"
    
    if echo "$response" | grep -q '"status":"ok"'; then
        print_success "健康检查通过"
    else
        print_error "健康检查失败"
    fi
    echo
}

# 测试监控接口
test_monitoring() {
    print_info "测试监控接口..."
    
    print_info "获取指标数据..."
    curl -s "$MONITOR_BASE/metrics" | head -20
    echo
    
    print_info "获取监控健康状态..."
    curl -s "$MONITOR_BASE/health" | jq '.' 2>/dev/null || curl -s "$MONITOR_BASE/health"
    echo
}

# 测试API接口（模拟数据）
test_api_endpoints() {
    print_info "测试API接口..."
    
    # 注意：这些测试可能会失败，因为需要有效的QOS.HK API密钥
    print_warning "以下测试需要有效的QOS.HK API密钥，可能会返回错误"
    
    print_info "测试获取快照数据..."
    curl -s "$API_BASE/api/v1/snapshot?codes=US:AAPL,HK:700" | jq '.' 2>/dev/null || \
    curl -s "$API_BASE/api/v1/snapshot?codes=US:AAPL,HK:700"
    echo
    
    print_info "测试获取K线数据..."
    curl -s "$API_BASE/api/v1/kline?codes=US:AAPL&kline_type=1001&count=5" | jq '.' 2>/dev/null || \
    curl -s "$API_BASE/api/v1/kline?codes=US:AAPL&kline_type=1001&count=5"
    echo
    
    print_info "测试获取盘口深度..."
    curl -s "$API_BASE/api/v1/depth?codes=US:AAPL" | jq '.' 2>/dev/null || \
    curl -s "$API_BASE/api/v1/depth?codes=US:AAPL"
    echo
    
    print_info "测试获取交易品种信息..."
    curl -s "$API_BASE/api/v1/instrument?codes=US:AAPL" | jq '.' 2>/dev/null || \
    curl -s "$API_BASE/api/v1/instrument?codes=US:AAPL"
    echo
}

# 测试WebSocket连接
test_websocket() {
    print_info "测试WebSocket连接..."
    
    if command -v wscat &> /dev/null; then
        print_info "使用 wscat 测试WebSocket连接..."
        echo "连接到 ws://localhost:8080/ws"
        echo "发送心跳消息: {\"type\":\"PING\"}"
        echo "按 Ctrl+C 退出"
        echo
        wscat -c ws://localhost:8080/ws
    else
        print_warning "wscat 未安装，跳过WebSocket测试"
        print_info "安装 wscat: npm install -g wscat"
    fi
}

# 性能测试
test_performance() {
    print_info "简单性能测试..."
    
    if command -v ab &> /dev/null; then
        print_info "使用 Apache Bench 进行性能测试..."
        ab -n 100 -c 10 "$API_BASE/health"
    elif command -v wrk &> /dev/null; then
        print_info "使用 wrk 进行性能测试..."
        wrk -t4 -c10 -d10s "$API_BASE/health"
    else
        print_warning "未安装性能测试工具 (ab 或 wrk)"
        print_info "手动测试并发请求..."
        for i in {1..10}; do
            curl -s "$API_BASE/health" > /dev/null &
        done
        wait
        print_success "并发请求测试完成"
    fi
}

# 显示使用说明
show_usage() {
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help        显示帮助信息"
    echo "  -a, --all         运行所有测试"
    echo "  -s, --status      检查服务状态"
    echo "  -H, --health      测试健康检查"
    echo "  -m, --monitor     测试监控接口"
    echo "  -A, --api         测试API接口"
    echo "  -w, --websocket   测试WebSocket"
    echo "  -p, --performance 性能测试"
    echo
}

# 主函数
main() {
    echo "========================================"
    echo "    QOS Market API 测试脚本"
    echo "========================================"
    echo
    
    case "${1:-all}" in
        -h|--help)
            show_usage
            exit 0
            ;;
        -s|--status)
            check_service
            ;;
        -H|--health)
            check_service
            test_health
            ;;
        -m|--monitor)
            check_service
            test_monitoring
            ;;
        -A|--api)
            check_service
            test_api_endpoints
            ;;
        -w|--websocket)
            check_service
            test_websocket
            ;;
        -p|--performance)
            check_service
            test_performance
            ;;
        -a|--all|*)
            check_service
            test_health
            test_monitoring
            test_api_endpoints
            test_performance
            ;;
    esac
    
    print_success "测试完成！"
}

# 如果直接运行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
