#!/bin/bash

# K线数据修复功能演示脚本

BASE_URL="http://localhost:8080"
API_URL="${BASE_URL}/api/v1"

echo "=== K线数据修复功能演示 ==="
echo

# 检查服务是否运行
echo "1. 检查服务状态..."
curl -s "${BASE_URL}/health" | jq '.' || {
    echo "❌ 服务未运行，请先启动服务"
    exit 1
}
echo "✅ 服务运行正常"
echo

# 添加修复数据
echo "2. 添加修复数据..."
CORRECTION_DATA='{
  "code": "US:AAPL",
  "kline_type": 1001,
  "timestamp": **********,
  "field": "cl",
  "corr_value": "150.25",
  "reason": "修复错误的收盘价数据"
}'

CORRECTION_ID=$(curl -s -X POST "${API_URL}/corrections" \
  -H "Content-Type: application/json" \
  -d "${CORRECTION_DATA}" | jq -r '.data.id')

if [ "$CORRECTION_ID" != "null" ] && [ "$CORRECTION_ID" != "" ]; then
    echo "✅ 修复数据添加成功，ID: $CORRECTION_ID"
else
    echo "❌ 修复数据添加失败"
    exit 1
fi
echo

# 查看修复数据列表
echo "3. 查看修复数据列表..."
curl -s "${API_URL}/corrections" | jq '.data'
echo

# 查看修复统计
echo "4. 查看修复统计..."
curl -s "${API_URL}/corrections/stats" | jq '.data'
echo

# 查看监控指标中的修复信息
echo "5. 查看监控指标..."
curl -s "http://localhost:9090/metrics" | jq '.corrections' 2>/dev/null || echo "监控服务可能未启动"
echo

# 测试修复数据切换
echo "6. 测试禁用修复数据..."
curl -s -X POST "${API_URL}/corrections/${CORRECTION_ID}/toggle" \
  -H "Content-Type: application/json" \
  -d '{"is_active": false}' | jq '.'
echo

echo "7. 重新启用修复数据..."
curl -s -X POST "${API_URL}/corrections/${CORRECTION_ID}/toggle" \
  -H "Content-Type: application/json" \
  -d '{"is_active": true}' | jq '.'
echo

# 清理测试数据
echo "8. 清理测试数据..."
curl -s -X DELETE "${API_URL}/corrections/${CORRECTION_ID}" | jq '.'
echo

echo "=== 演示完成 ==="
echo
echo "💡 提示："
echo "- 访问 ${BASE_URL}/corrections 使用Web管理界面"
echo "- 访问 ${BASE_URL}/health 查看服务状态"
echo "- 访问 http://localhost:9090/metrics 查看监控指标"
echo "- 查看 docs/CORRECTION.md 了解详细使用说明"
