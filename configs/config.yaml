# QOS Market API 配置文件

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: 30s
  write_timeout: 30s
  idle_timeout: 60s

# QOS.HK API配置
qos:
  api_key: "8d9e98929a7276d67cb275595bd29c38"  # 请替换为您的API密钥
  http_base_url: "https://qos.hk"
  websocket_url: "wss://api.qos.hk/ws"
  request_timeout: 30s
  max_retries: 3
  retry_delay: 5s

# 缓存配置
cache:
  # 内存缓存配置
  memory:
    max_size: 10000  # 最大缓存条目数
    ttl: 300s       # 默认过期时间(5分钟)
    cleanup_interval: 60s  # 清理间隔
  
  # 不同数据类型的缓存TTL
  ttl:
    snapshot: 5s      # 实时行情快照
    kline_1m: 120s     # 1分钟K线
    kline_5m: 300s    # 5分钟K线
    kline_15m: 900s   # 15分钟K线
    kline_30m: 1800s  # 30分钟K线
    kline_1h: 3600s   # 1小时K线
    kline_2h: 7200s   # 2小时K线
    kline_4h: 14400s  # 4小时K线
    kline_1d: 86400s  # 日K线
    kline_1w: 604800s # 周K线
    kline_1M: 2592000s # 月K线
    kline_1y: 31536000s # 年K线
    depth: 2s         # 盘口深度
    trades: 10s       # 成交明细

# 日志配置
logging:
  level: "info"  # debug, info, warn, error
  format: "json" # json, text
  output: "both" # stdout, file, both
  file:
    path: "logs/qos-market-api.log"
    max_size: 100  # MB
    max_backups: 5
    max_age: 30    # days
    compress: true

# 监控配置
monitoring:
  enabled: true
  metrics_port: 9090
  health_check_interval: 30s

# 交易品种配置
symbols:
  # 美股
  us_stocks:
    - "NVDA"    # 英伟达
    - "INTC"    # 英特尔
    - "BABA"    # 阿里巴巴
    - "NFLX"    # 奈飞
    - "AMGN"    # 安进
    - "AXP"     # 美国运通
    - "BABACAT" # 阿里巴巴 CAT
    - "CRM"     # Salesforce
    - "TSM"     # 台积电
    - "QCOM"    # 高通
    - "AVGO"    # 博通
    - "TXN"     # 德州仪器

    
  # 港股
  hk_stocks:
    - "00001"  # 长和
    - "00002"  # 中电控股
    - "00003"  # 电能实业
    - "00005"  # 汇丰控股
    - "00006"  # 恒生银行
    - "00011"  # 恒基地产
    - "00016"  # 新鸿基地产
    - "00027"  # 华夏控股
    - "00066"  # 中银香港
    - "00101"  # 中国光大国际
    - "00175"  # 中信证券
    - "00241"  # 中国铁建
    - "00267"  # 中国交通建设
    - "00285"  # 中国银行
    - "00288"  # 中国人寿
    - "00291"  # 中国太平洋保险
    - "00316"  # 中国海洋石油
    - "03750"  # 美的集团

    
  # # A股
  # a_stocks:
  #   sh:  # 上海
  #     - "600519"  # 贵州茅台
  #     - "600036"  # 招商银行
  #   sz:  # 深圳
  #     - "000001"  # 平安银行
  #     - "002594"  # 比亚迪
    
  # 数字货币
  crypto:
    - "BTCUSDT"  # 比特币
    - "ETHUSDT"  # 以太坊
    - "BNBUSDT"  # 币安币
    - "SOLUSDT"  # Solana
    - "XRPUSDT"  # 瑞波币
    - "DOGEUSDT" # 狗狗币
    - "ADAUSDT"  # 卡尔达诺
    - "TRXUSDT"  # 波场币
    - "SUIUSDT"  # Sui
    - "LINKUSDT" # Chainlink
    - "AVAXUSDT" # Avalanche
    - "1000SHIBUSDT" # Shiba Inu
    - "BCHUSDT"  # 比特币现金
    - "DOTUSDT"  # Polkadot
    - "LTCUSDT"  # 莱特币
    - "XLMUSDT"  # 恒星币
    - "HBARUSDT" # Hedera Hashgraph
    - "UNIUSDT"  # Uniswap
    - "AAVEUSDT" # Aave
    - "NEARUSDT" # Near Protocol
    - "APTUSDT"  # Aptos
    - "ETCUSDT"  # Ethereum Classic
    - "ICPUSDT"  # Internet Computer
    - "XVSUSDT"  # Venus
  
  # # 外汇
  # forex:
  #   - "USDJPY"
  #   - "EURUSD"
  #   - "GBPUSD"
  
  # # 商品
  # commodities:
  #   - "XAUUSD"  # 黄金
  #   - "XAGUSD"  # 白银

# WebSocket配置
websocket:
  max_connections: 1000
  read_buffer_size: 1024
  write_buffer_size: 1024
  ping_period: 54s
  pong_wait: 60s
  write_wait: 10s
  max_message_size: 512

# 限流配置
rate_limit:
  enabled: true
  requests_per_minute: 60
  burst: 10

# 重试和恢复配置
recovery:
  max_reconnect_attempts: 10
  reconnect_delay: 5s
  health_check_interval: 30s
  restart_on_panic: true

# K线修复配置
correction:
  enabled: true
  database_path: "data/corrections.db"
  refresh_interval: 60s  # 缓存刷新间隔

# K线数据配置
kline:
  # 最近K线配置
  recent:
    enabled: true
    update_interval: 2s  # 后台更新频率，可配置
    max_periods: 2000     # 保留的最大周期数
    supported_types:      # 支持的K线类型
      - "1m"              # 1分钟
      - "5m"              # 5分钟
      - "15m"             # 15分钟
      - "30m"             # 30分钟
      - "1h"              # 1小时
      # - "1d"              # 日线
      # - "1w"              # 周线
      # - "1M"              # 月线

    # 延迟优化配置
    initial_load_count: 1000      # 初始化时加载的K线条数
    initial_load_batch_size: 10    # 初始化时批处理大小（每批处理的品种数）
    refresh_count: 2             # 实时刷新时获取的K线条数
    integrity_check_interval: 5m  # 完整性检查间隔，检查是否有缓存以及是否过期
    max_data_age: 30m             # 数据最大存活时间，超过则重新加载，完整性检查时检测，缓存更新时刷新

  # 历史K线配置
  history:
    enabled: true
    cache_ttl: 24h        # 缓存有效期，默认24小时
