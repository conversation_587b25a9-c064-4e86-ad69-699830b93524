# QOS Market API 测试配置文件

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: 30s
  write_timeout: 30s
  idle_timeout: 60s

# QOS API配置
qos:
  api_key: "8d9e98929a7276d67cb275595bd29c38"
  http_base_url: "https://qos.hk"
  websocket_url: "wss://api.qos.hk/ws"
  request_timeout: 30s
  max_retries: 3
  retry_delay: 5s

# 缓存配置
cache:
  memory:
    max_size: 10000
    ttl: 5m
    cleanup_interval: 1m

# 日志配置
logging:
  level: "info"
  format: "json"
  output: "stdout"

# 监控配置
monitoring:
  enabled: true
  metrics_port: 9090
  health_check_interval: 30s

# 支持的交易品种
symbols:
  us_stocks: ["AAPL", "TSLA", "MSFT"]
  hk_stocks: ["700", "9988", "1810"]
  crypto: ["BTCUSDT", "ETHUSDT", "BNBUSDT"]

# WebSocket配置
websocket:
  enabled: true
  max_connections: 1000
  read_buffer_size: 1024
  write_buffer_size: 1024
  ping_period: 54s
  pong_wait: 60s
  write_wait: 10s

# 限流配置
rate_limit:
  enabled: true
  requests_per_minute: 1000
  burst: 100

# 恢复配置
recovery:
  max_reconnect_attempts: 10
  reconnect_delay: 5s
  health_check_interval: 30s
  restart_on_panic: true

# K线修复配置
correction:
  enabled: true
  database_path: "data/corrections.db"
  refresh_interval: 60s

# K线数据配置（测试：只支持少数几种类型）
kline:
  # 最近K线配置
  recent:
    enabled: true
    update_interval: 30s
    max_periods: 50
    supported_types:      # 只支持3种类型进行测试
      - "1m"              # 1分钟
      - "1h"              # 1小时
      - "1d"              # 日线

    # 延迟优化配置（测试用较小的值）
    initial_load_count: 100       # 初始化时加载的K线条数
    initial_load_batch_size: 3    # 初始化时批处理大小（测试用较小值）
    refresh_count: 5              # 实时刷新时获取的K线条数
    integrity_check_interval: 2m  # 完整性检查间隔
    max_data_age: 10m             # 数据最大存活时间，超过则重新加载
  
  # 历史K线配置
  history:
    enabled: true
    cache_ttl: 12h        # 测试用较短的缓存时间
