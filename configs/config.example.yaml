# QOS Market API 配置文件示例

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: 30s
  write_timeout: 30s
  idle_timeout: 60s

# QOS.HK API配置
qos:
  api_key: "your_api_key_here"  # 请替换为您的API密钥
  http_base_url: "https://qos.hk"
  websocket_url: "wss://api.qos.hk/ws"
  request_timeout: 30s
  max_retries: 3
  retry_delay: 5s

# 缓存配置
cache:
  # 内存缓存配置
  memory:
    max_size: 1000  # 最大缓存条目数
    ttl: 300s       # 默认过期时间(5分钟)
    cleanup_interval: 60s  # 清理间隔
  
  # 不同数据类型的缓存TTL
  ttl:
    snapshot: 5s      # 实时行情快照
    kline_1m: 60s     # 1分钟K线
    kline_5m: 300s    # 5分钟K线
    kline_1h: 3600s   # 1小时K线
    kline_1d: 86400s  # 日K线
    depth: 2s         # 盘口深度
    trades: 10s       # 成交明细

# 日志配置
logging:
  level: "info"  # debug, info, warn, error
  format: "json" # json, text
  output: "stdout" # stdout, file, both
  file:
    path: "logs/qos-market-api.log"
    max_size: 100  # MB
    max_backups: 5
    max_age: 30    # days
    compress: true

# 监控配置
monitoring:
  enabled: true
  metrics_port: 9090
  health_check_interval: 30s

# 交易品种配置
symbols:
  # 美股
  us_stocks:
    - "AAPL"
    - "TSLA"
    - "MSFT"
    - "GOOGL"
    - "AMZN"
  
  # 港股
  hk_stocks:
    - "700"   # 腾讯
    - "9988"  # 阿里巴巴
    - "1810"  # 小米
    - "3690"  # 美团
  
  # A股
  a_stocks:
    sh:  # 上海
      - "600519"  # 贵州茅台
      - "600036"  # 招商银行
    sz:  # 深圳
      - "000001"  # 平安银行
      - "002594"  # 比亚迪
  
  # 数字货币
  crypto:
    - "BTCUSDT"
    - "ETHUSDT"
    - "BNBUSDT"
  
  # 外汇
  forex:
    - "USDJPY"
    - "EURUSD"
    - "GBPUSD"
  
  # 商品
  commodities:
    - "XAUUSD"  # 黄金
    - "XAGUSD"  # 白银

# WebSocket配置
websocket:
  max_connections: 1000
  read_buffer_size: 1024
  write_buffer_size: 1024
  ping_period: 54s
  pong_wait: 60s
  write_wait: 10s
  max_message_size: 512

# 限流配置
rate_limit:
  enabled: true
  requests_per_minute: 60
  burst: 10

# 重试和恢复配置
recovery:
  max_reconnect_attempts: 10
  reconnect_delay: 5s
  health_check_interval: 30s
  restart_on_panic: true

# K线修复配置
correction:
  enabled: true
  database_path: "data/corrections.db"
  refresh_interval: 60s  # 缓存刷新间隔

# K线数据配置
kline:
  # 最近K线配置
  recent:
    enabled: true
    update_interval: 30s  # 后台更新频率，可配置
    max_periods: 100      # 保留的最大周期数
    supported_types:      # 支持的K线类型
      - "1m"              # 1分钟
      - "5m"              # 5分钟
      - "15m"             # 15分钟
      - "30m"             # 30分钟
      - "1h"              # 1小时
      - "1d"              # 日线

    # 延迟优化配置
    initial_load_count: 1000      # 初始化时加载的K线条数
    initial_load_batch_size: 5    # 初始化时批处理大小（每批处理的品种数）
    refresh_count: 10             # 实时刷新时获取的K线条数
    integrity_check_interval: 5m  # 完整性检查间隔
    max_data_age: 30m             # 数据最大存活时间，超过则重新加载

  # 历史K线配置
  history:
    enabled: true
    cache_ttl: 24h        # 缓存有效期，默认24小时
