# K线数据初始化日志功能

## 功能概述

K线数据初始化过程现在提供了详细的日志输出，包括品种信息、K线类型、数量统计和时间跟踪，帮助开发者和运维人员监控和调试初始化过程。

## 日志级别

- **INFO**: 主要进度和结果信息
- **DEBUG**: 详细的执行过程和数据信息
- **WARN**: 警告和重试信息
- **ERROR**: 错误和失败信息

## 日志内容详解

### 1. 初始化开始

```
=== Starting initial kline data load ===
Initial load configuration:
  - Total symbols: 10 ([US:AAPL US:GOOGL US:MSFT US:AMZN US:TSLA HK:00001 HK:00002 HK:00003 CF:BTCUSDT CF:ETHUSDT])
  - K-line types: 3 ([1m 5m 1h])
  - Periods per symbol: 10
  - Batch size: 2
  - Expected total data points: 300
```

**包含信息**：
- 总品种数和具体品种列表
- 支持的K线类型和数量
- 每个品种加载的周期数
- 批处理大小配置
- 预期的总数据点数

### 2. 并行加载启动

```
Starting parallel load for 3 K-line types...
Loading initial 1m kline data for 10 symbols in batches of 2
Loading initial 5m kline data for 10 symbols in batches of 2
Loading initial 1h kline data for 10 symbols in batches of 2
```

**包含信息**：
- 并行处理的K线类型数量
- 每种K线类型的处理详情

### 3. 批次请求详情

```
Requesting initial 1m kline data for 2 symbols: [US:AAPL US:GOOGL]
QOS API call successful for 1m kline batch (duration: 51.109ms)
```

**包含信息**：
- 请求的K线类型
- 批次中的品种数量和具体品种
- API调用耗时

### 4. 数据处理详情

```
Loaded US:AAPL 1m klines: 10 periods from 2025-07-14 22:56:22 to 2025-07-14 23:05:22
Loaded US:GOOGL 1m klines: 10 periods from 2025-07-14 22:56:22 to 2025-07-14 23:05:22
```

**包含信息**：
- 品种代码
- K线类型
- 加载的周期数
- 数据的时间范围（起始时间到结束时间）

### 5. 批次完成统计

```
Batch completed for 1m kline: 2 success, 0 errors, 20 data points (request: 51.421334ms, process: 162.917µs, total: 51.584251ms)
```

**包含信息**：
- K线类型
- 成功和错误数量
- 总数据点数
- 时间分解：请求时间、处理时间、总时间

### 6. K线类型完成总结

```
✓ 1m klines loaded: 10 success, 0 errors (duration: 51.929125ms)
✓ 5m klines loaded: 10 success, 0 errors (duration: 51.856541ms)
✓ 1h klines loaded: 10 success, 0 errors (duration: 51.920958ms)
```

**包含信息**：
- K线类型
- 总成功和错误数量
- 该类型的总耗时

### 7. 最终完成总结

```
=== Initial kline data load completed ===
  - Success: 30
  - Errors: 0
  - Total duration: 52.020083ms
  - Average per symbol: 5.202008ms
  - Success rate: 100.0%
```

**包含信息**：
- 总成功和错误数量
- 整个初始化过程的总耗时
- 平均每个品种的处理时间
- 成功率百分比

## 错误和重试日志

### API调用失败重试

```
Failed to get initial kline data batch for 1m (attempt 1/3, duration: 30.5s): timeout, retrying...
Failed to get initial kline data batch for 1m (attempt 2/3, duration: 30.5s): timeout, retrying...
```

### 批次过大自动分割

```
Batch size too large for 1m kline, reducing batch size and retrying (attempt 1/3)
Splitting batch for 1m kline: 10 symbols -> 5 + 5 symbols
Recursive batch completed for 1m kline: 10 success, 0 errors
```

### 数据缺失警告

```
No kline data returned for symbol US:INVALID (1m)
QOS API returned empty kline data list for initial load batch of 5 requests (1m, duration: 45.2ms)
```

## 性能监控指标

### 时间指标
- **API请求时间**: 单次QOS API调用耗时
- **数据处理时间**: 数据解析和缓存耗时
- **批次总时间**: 单个批次的完整处理时间
- **类型总时间**: 单个K线类型的完整加载时间
- **整体总时间**: 所有K线类型的完整初始化时间

### 数据指标
- **数据点数**: 实际加载的K线数据点数量
- **成功率**: 成功加载的品种比例
- **批次效率**: 每个批次的平均处理时间
- **并行效率**: 并行处理相比串行处理的性能提升

## 日志配置

### 启用详细日志

```yaml
logging:
  level: "debug"  # 启用DEBUG级别查看详细信息
  format: "text"  # 或 "json"
  output: "stdout" # 或 "file"
```

### 生产环境建议

```yaml
logging:
  level: "info"   # 生产环境使用INFO级别
  format: "json"  # JSON格式便于日志分析
  output: "file"  # 输出到文件
```

## 故障排查

### 常见问题和对应日志

1. **初始化超时**
   - 查看 "Total duration" 是否过长
   - 检查 "API call duration" 是否有异常

2. **数据缺失**
   - 查看 "No kline data returned" 警告
   - 检查品种代码是否正确

3. **批次失败**
   - 查看 "Batch size too large" 警告
   - 检查是否触发递归分割

4. **性能问题**
   - 比较 "request" 和 "process" 时间
   - 查看并行处理效果

## 监控建议

1. **关键指标监控**
   - 初始化总时间
   - 成功率
   - 错误数量

2. **告警设置**
   - 初始化时间超过阈值
   - 成功率低于95%
   - 连续错误超过阈值

3. **性能分析**
   - 定期分析平均处理时间
   - 监控API调用延迟趋势
   - 评估批次大小优化效果
