# QOS 行情数据 API 接口文档
## 基础信息

- **基础URL**: `http://localhost:8080`
- **监控URL**: `http://localhost:9090`
- **数据格式**: JSON
- **字符编码**: UTF-8

## HTTP API 接口

### 1. 健康检查

检查服务运行状态。

**请求**
```
GET /health
```

**响应示例**
```json
{
  "status": "ok",
  "timestamp": **********,
  "version": "1.0.0",
  "services": {
    "qos_websocket": true,
    "cache": 150
  },
  "supported_symbols": ["US:AAPL", "HK:700", "CF:BTCUSDT"]
}
```

### 2. 获取实时行情快照

获取指定交易品种的实时行情快照数据。

**请求**
```
GET /api/v1/snapshot?codes=US:AAPL,HK:700,CF:BTCUSDT
```

**参数说明**
- `codes`: 交易品种代码列表，用逗号分隔

**响应示例**
```json
{
  "msg": "OK",
  "data": [
    {
      "c": "US:AAPL",
      "lp": "150.25",
      "yp": "149.80",
      "o": "150.00",
      "h": "151.20",
      "l": "149.50",
      "ts": **********,
      "v": "1000000",
      "t": "150250000",
      "s": 0
    }
  ]
}
```

### 3. 获取K线数据（智能路由）

获取指定交易品种的K线数据。系统会根据请求参数智能选择最优数据源（最近K线缓存或历史K线API）。

**请求**
```
GET /api/v1/kline?codes=US:AAPL,HK:700&kline_type=1001&count=100&adjust=0&end_time=**********
```

**参数说明**
- `codes`: 交易品种代码列表，用逗号分隔（必需）
- `kline_type`: K线类型，默认1001（日线）
- `count`: 获取数量，默认100
- `adjust`: 复权类型（0=不复权, 1=前复权），默认0
- `end_time`: 结束时间戳（可选），指定时使用历史数据接口

**智能路由规则**
- 指定`end_time`时：使用历史K线接口
- 请求数量超过配置的最大周期数时：使用历史K线接口
- 长周期K线（周线、月线、年线）：优先使用历史K线接口
- 其他情况：使用最近K线缓存（响应更快）

**响应示例**
```json
{
  "msg": "OK",
  "data": [
    {
      "c": "US:AAPL",
      "k": [
        {
          "c": "US:AAPL",
          "o": "150.00",
          "cl": "150.25",
          "h": "151.20",
          "l": "149.50",
          "v": "1000000",
          "ts": **********,
          "kt": 1001
        }
      ]
    }
  ]
}
```

### 4. 获取历史K线数据

专门的历史K线数据接口，强制使用历史数据源。

**请求**
```
GET /api/v1/history?codes=US:AAPL&kline_type=1001&count=200&end_time=**********
```

**参数说明**
- `codes`: 交易品种代码列表，用逗号分隔（必需）
- `kline_type`: K线类型，默认1001（日线）
- `count`: 获取数量，默认100
- `adjust`: 复权类型（0=不复权, 1=前复权），默认0
- `end_time`: 结束时间戳（可选）

**响应格式**
与`/api/v1/kline`接口相同。

### 5. 获取盘口深度

获取指定交易品种的盘口深度数据。支持智能缓存，优先返回缓存数据。

**请求**
```
GET /api/v1/depth?codes=US:AAPL,HK:700
```

**参数说明**
- `codes`: 交易品种代码列表，用逗号分隔（必需）

**响应示例**
```json
{
  "msg": "OK",
  "data": [
    {
      "c": "US:AAPL",
      "b": [
        {"p": "150.20", "v": "1000"},
        {"p": "150.15", "v": "2000"}
      ],
      "a": [
        {"p": "150.25", "v": "1500"},
        {"p": "150.30", "v": "2500"}
      ],
      "ts": **********
    }
  ]
}
```

### 6. 获取交易品种信息

获取指定交易品种的基础信息。

**请求**
```
GET /api/v1/instrument?codes=US:AAPL,HK:700
```

**参数说明**
- `codes`: 交易品种代码列表，用逗号分隔（必需）

**响应示例**
```json
{
  "msg": "OK",
  "data": [
    {
      "c": "US:AAPL",
      "e": "NASD",
      "tc": "USD",
      "nc": "苹果",
      "ne": "Apple Inc.",
      "ls": 1,
      "ts": 15022073000,
      "os": 14998202926,
      "ep": "6.24",
      "na": "4.44",
      "dy": "1"
    }
  ]
}
```

### 7. 获取支持的交易品种列表

获取系统支持的所有交易品种代码列表。

**请求**
```
GET /api/v1/symbols
```

**参数说明**
无需参数

**响应示例**
```json
{
  "msg": "OK",
  "data": [
    "US:AAPL",
    "US:TSLA",
    "HK:700",
    "HK:9988",
    "CF:BTCUSDT",
    "CF:ETHUSDT"
  ]
}
```

### 8. 获取最近K线服务状态

获取最近K线服务的运行状态和统计信息。

**请求**
```
GET /api/v1/recent-kline-status
```

**参数说明**
无需参数

**响应示例**
```json
{
  "msg": "OK",
  "data": {
    "enabled": true,
    "supported_types": ["1", "5", "15", "30", "60", "1001"],
    "cache_stats": {
      "total_cached": 150,
      "hit_rate": 85.5
    },
    "update_frequency": "30s"
  }
}
```

## 交易品种代码格式

### 市场前缀

- `US:` - 美股
- `HK:` - 港股
- `SH:` - 上海A股
- `SZ:` - 深圳A股
- `CF:` - 数字货币
- `FX:` - 外汇
- `IX:` - 指数
- `CM:` - 商品

### 示例代码

- `US:AAPL` - 苹果公司
- `HK:700` - 腾讯控股
- `SH:600519` - 贵州茅台
- `CF:BTCUSDT` - 比特币/USDT
- `FX:EURUSD` - 欧元/美元
- `CM:XAUUSD` - 黄金/美元

## K线类型

| 类型值 | 说明   |
| ------ | ------ |
| 1      | 1分钟  |
| 5      | 5分钟  |
| 15     | 15分钟 |
| 30     | 30分钟 |
| 60     | 1小时  |
| 120    | 2小时  |
| 240    | 4小时  |
| 1001   | 日线   |
| 1007   | 周线   |
| 1030   | 月线   |
| 2001   | 年线   |

## 错误码

| 状态码 | 说明           |
| ------ | -------------- |
| 200    | 成功           |
| 400    | 请求参数错误   |
| 500    | 服务器内部错误 |
| 503    | 服务不可用     |


## K线数据修复功能

QOS 行情数据 API 支持对错误的K线数据进行修复，提供了完整的修复数据管理功能。

### 修复数据管理API

#### 1. 添加修复数据

**请求**
```
POST /api/v1/corrections
Content-Type: application/json

{
  "code": "US:AAPL",
  "kline_type": 1001,
  "timestamp": **********,
  "field": "cl",
  "corr_value": "150.25",
  "reason": "修复错误的收盘价"
}
```

**参数说明**
- `code`: 股票代码
- `kline_type`: K线类型（参考K线类型表）
- `timestamp`: 时间戳（秒）
- `field`: 修复字段（o=开盘价, cl=收盘价, h=最高价, l=最低价, v=成交量）
- `corr_value`: 修复后的值
- `reason`: 修复原因（可选）

**响应示例**
```json
{
  "msg": "Correction added successfully",
  "data": {
    "id": 1,
    "code": "US:AAPL",
    "kline_type": 1001,
    "timestamp": **********,
    "field": "cl",
    "corr_value": "150.25",
    "reason": "修复错误的收盘价",
    "created_at": **********,
    "updated_at": **********,
    "is_active": true
  }
}
```

#### 2. 获取修复数据列表

**请求**
```
GET /api/v1/corrections?code=US:AAPL&kline_type=1001&limit=50&offset=0
```

**参数说明**
- `code`: 股票代码（可选）
- `kline_type`: K线类型（可选）
- `limit`: 返回数量限制（默认50，最大1000）
- `offset`: 偏移量（默认0）

#### 3. 获取单个修复数据

**请求**
```
GET /api/v1/corrections/{id}
```

#### 4. 更新修复数据

**请求**
```
PUT /api/v1/corrections/{id}
Content-Type: application/json

{
  "corr_value": "150.30",
  "reason": "更新修复值"
}
```

#### 5. 删除修复数据

**请求**
```
DELETE /api/v1/corrections/{id}
```

#### 6. 切换修复数据状态

**请求**
```
POST /api/v1/corrections/{id}/toggle
Content-Type: application/json

{
  "is_active": false
}
```

#### 7. 获取修复统计信息

**请求**
```
GET /api/v1/corrections/stats
```

**响应示例**
```json
{
  "msg": "OK",
  "data": {
    "total_corrections": 10,
    "by_code": {
      "US:AAPL": 5,
      "HK:700": 3,
      "CF:BTCUSDT": 2
    },
    "by_type": {
      "1001": 8,
      "60": 2
    },
    "by_field": {
      "cl": 6,
      "o": 2,
      "h": 1,
      "v": 1
    },
    "refresh_interval": "1m0s"
  }
}
```

### Web管理界面

访问 `http://localhost:8080/corrections` 可以使用Web界面管理修复数据，支持：

- 添加新的修复数据
- 查看修复数据列表
- 编辑和删除修复数据
- 启用/禁用修复数据
- 查看修复统计信息

### 修复数据应用

修复数据会自动应用到K线数据API的返回结果中：

1. **实时应用**: 所有通过 `/api/v1/kline` 接口返回的K线数据都会自动应用修复
2. **缓存更新**: 修复数据变更后会自动刷新内存缓存
3. **低延时**: 修复检查和应用过程经过优化，确保低延时
4. **高效查询**: 使用内存缓存提供毫秒级的修复数据查询

### 配置说明

在配置文件中启用修复功能：

```yaml
# K线修复配置
correction:
  enabled: true                    # 是否启用修复功能
  database_path: "data/corrections.db"  # 数据库文件路径
  refresh_interval: 60s            # 缓存刷新间隔
```

### 监控指标

修复功能的监控指标可通过 `/metrics` 接口获取：

```json
{
  "corrections": {
    "enabled": true,
    "total_corrections": 10,
    "active_corrections": 8,
    "cache_size": 8,
    "last_refresh": "2024-01-01T12:00:00Z",
    "by_code": {...},
    "by_type": {...},
    "by_field": {...}
  }
}
```

## 


