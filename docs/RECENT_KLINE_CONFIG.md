# 最近K线支持类型配置功能

## 功能概述

最近K线服务现在支持通过配置文件自定义支持的K线类型，允许用户根据实际需求选择需要缓存的K线周期，优化资源使用和性能。

## 配置方式

### 配置文件设置

在 `configs/config.yaml` 中的 `kline.recent.supported_types` 字段配置支持的K线类型：

```yaml
kline:
  recent:
    enabled: true
    update_interval: 30s
    max_periods: 100
    supported_types:      # 支持的K线类型
      - "1m"              # 1分钟
      - "5m"              # 5分钟
      - "15m"             # 15分钟
      - "30m"             # 30分钟
      - "1h"              # 1小时
      - "2h"              # 2小时
      - "4h"              # 4小时
      - "1d"              # 日线
      - "1w"              # 周线
      - "1M"              # 月线
      - "1y"              # 年线
```

### 支持的K线类型

| 配置值 | 说明 | K线类型常量 |
|--------|------|-------------|
| `1m`   | 1分钟 | `types.Kline1Min` |
| `5m`   | 5分钟 | `types.Kline5Min` |
| `15m`  | 15分钟 | `types.Kline15Min` |
| `30m`  | 30分钟 | `types.Kline30Min` |
| `1h`   | 1小时 | `types.Kline1Hour` |
| `2h`   | 2小时 | `types.Kline2Hour` |
| `4h`   | 4小时 | `types.Kline4Hour` |
| `1d`   | 日线 | `types.KlineDay` |
| `1w`   | 周线 | `types.KlineWeek` |
| `1M`   | 月线 | `types.KlineMonth` |
| `1y`   | 年线 | `types.KlineYear` |

## 默认配置

如果 `supported_types` 配置为空或未设置，系统将使用默认的K线类型：

```yaml
supported_types:
  - "1m"    # 1分钟
  - "5m"    # 5分钟
  - "15m"   # 15分钟
  - "30m"   # 30分钟
  - "1h"    # 1小时
  - "1d"    # 日线
```

这些是最常用的短周期K线类型，适合最近K线缓存的使用场景。

## 配置验证

### 无效类型处理

- 如果配置中包含无效的K线类型字符串，系统会记录警告日志并忽略该类型
- 如果所有配置的类型都无效，系统会回退到默认配置

### 日志示例

```
level=warning msg="Unknown kline type in config: invalid_type"
level=warning msg="No valid kline types found in config, using defaults"
```

## API接口

### 查看当前配置

可以通过以下API端点查看当前的K线类型配置：

```bash
curl "http://localhost:8080/api/v1/recent-kline-status"
```

响应示例：

```json
{
  "msg": "OK",
  "data": {
    "is_running": true,
    "last_update_time": "2025-07-13T17:50:35Z",
    "update_count": 1,
    "error_count": 0,
    "update_interval": "30s",
    "max_periods": 100,
    "supported_types": ["1m", "1h", "1d"],
    "configured_types": ["1m", "1h", "1d"]
  }
}
```

字段说明：
- `supported_types`: 实际生效的K线类型列表
- `configured_types`: 配置文件中指定的K线类型列表

## 使用场景

### 1. 资源优化

只缓存业务需要的K线类型，减少内存使用和API调用：

```yaml
supported_types:
  - "1h"    # 只缓存小时线
  - "1d"    # 和日线
```

### 2. 高频交易

专注于短周期K线：

```yaml
supported_types:
  - "1m"    # 1分钟
  - "5m"    # 5分钟
  - "15m"   # 15分钟
```

### 3. 长期分析

关注长周期K线：

```yaml
supported_types:
  - "1d"    # 日线
  - "1w"    # 周线
  - "1M"    # 月线
```

## 性能影响

### 后台更新

- 配置的K线类型越少，后台更新越快
- 减少对QOS API的调用次数
- 降低网络带宽使用

### 内存使用

- 每种K线类型会为每个交易品种缓存最多 `max_periods` 个数据点
- 减少支持的类型可以显著降低内存使用

### 示例计算

假设：
- 9个交易品种
- 每种K线类型缓存100个数据点
- 每个数据点约200字节

内存使用对比：
- 6种类型：9 × 6 × 100 × 200 = 1.08MB
- 3种类型：9 × 3 × 100 × 200 = 0.54MB

## 监控和调试

### 日志监控

启动时会显示配置的K线类型数量：

```
level=info msg="Recent kline update completed: 3 success, 0 errors, duration: 807ms"
```

其中 "3 success" 表示成功更新了3种K线类型。

### 状态检查

通过API端点可以实时查看：
- 当前支持的K线类型
- 更新统计信息
- 错误计数

## 注意事项

1. **配置更改需要重启服务**：修改配置后需要重启服务器才能生效
2. **类型字符串大小写敏感**：必须使用正确的大小写（如 `1M` 而不是 `1m` 表示月线）
3. **长周期类型的考虑**：周线、月线、年线更适合使用历史K线接口而不是最近K线缓存
4. **API权限**：某些K线类型可能需要更高的API权限，无权限时会在日志中显示401错误

## 最佳实践

1. **根据业务需求选择**：只配置业务实际需要的K线类型
2. **考虑更新频率**：高频更新的类型（如1分钟）更适合最近K线缓存
3. **监控资源使用**：定期检查内存和网络使用情况
4. **测试配置**：在生产环境应用前先在测试环境验证配置

## 故障排除

### 问题：所有K线类型都返回401错误

**原因**：API密钥权限不足

**解决方案**：
1. 检查API密钥是否有效
2. 联系QOS服务提供商升级权限
3. 临时移除无权限的K线类型

### 问题：配置的类型没有生效

**原因**：配置格式错误或服务未重启

**解决方案**：
1. 检查YAML格式是否正确
2. 验证类型字符串是否在支持列表中
3. 重启服务器
4. 查看启动日志确认配置加载

### 问题：内存使用过高

**原因**：配置了过多的K线类型或max_periods过大

**解决方案**：
1. 减少supported_types中的类型数量
2. 降低max_periods值
3. 监控实际内存使用情况
