# QOS Market API 部署指南

## 概述

QOS Market API 是一个高性能的金融市场数据服务，提供实时行情、K线数据、盘口深度等功能。本指南将帮助您在不同环境中部署和配置该服务。

## 系统特性

- 🔄 **多数据源支持**: 实时行情、K线数据、盘口深度、交易品种信息
- 💾 **智能缓存**: 多层缓存策略，提升响应速度
- 🔧 **K线修复**: 支持错误数据修复和跨周期传播
- 📊 **性能监控**: 详细的延迟跟踪和统计指标
- 🌐 **WebSocket推送**: 实时数据推送服务
- 📝 **请求日志**: 完整的用户请求日志记录
- 🏥 **健康检查**: 自动故障检测和恢复

## 部署前准备

### 1. 系统要求

**最低配置**
- **操作系统**: Linux, macOS, Windows
- **Go版本**: 1.19 或更高
- **内存**: 最少 1GB，推荐 2GB+
- **磁盘**: 最少 500MB 可用空间（包含日志和数据库）
- **网络**: 需要访问 qos.hk 的 HTTPS 和 WebSocket 接口

**推荐配置（生产环境）**
- **CPU**: 2核心或更多
- **内存**: 4GB 或更多
- **磁盘**: SSD，2GB+ 可用空间
- **网络**: 稳定的互联网连接，低延迟

### 2. 获取 QOS.HK API 密钥

1. 访问 [QOS.HK 官网](https://qos.hk)
2. 注册账户并获取 API 密钥
3. 记录您的 API 密钥，稍后需要配置

### 3. 端口规划

默认端口配置：
- **8080**: HTTP API 服务端口
- **9090**: 监控指标端口
- **WebSocket**: 使用 8080 端口的 `/ws` 路径

## 部署方式

### 方式一：快速开发部署

适用于开发和测试环境，快速启动服务。

```bash
# 1. 克隆或下载项目
git clone <repository-url>
cd qos-market-api

# 2. 初始化项目（自动安装依赖和创建配置）
make init

# 3. 配置 API 密钥
vim configs/config.yaml
# 将 qos.api_key 字段设置为您的真实密钥

# 4. 构建并运行
make run

# 5. 验证服务
curl http://localhost:8080/health
curl http://localhost:9090/metrics
```

**特点**
- 快速启动，适合开发调试
- 使用默认配置，功能完整
- 支持热重载和实时日志

### 方式二：Docker 容器部署

推荐用于生产环境，提供隔离和可移植性。

```bash
# 1. 准备配置文件
cp configs/config.example.yaml configs/config.yaml
vim configs/config.yaml  # 设置 API 密钥和生产配置

# 2. 创建数据目录
mkdir -p data logs

# 3. 使用 Docker Compose 启动
docker-compose up -d

# 4. 查看服务状态
docker-compose ps

# 5. 查看日志
docker-compose logs -f qos-market-api

# 6. 停止服务
docker-compose down
```

**Docker Compose 配置示例**
```yaml
version: '3.8'
services:
  qos-market-api:
    build: .
    ports:
      - "8080:8080"
      - "9090:9090"
    volumes:
      - ./configs:/app/configs:ro
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - CONFIG_PATH=/app/configs/config.yaml
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

### 方式三：系统服务部署（生产推荐）

适用于生产环境，提供系统级服务管理和自动重启。

#### 创建系统用户和目录

```bash
# 创建专用用户
sudo useradd -r -s /bin/false qos-api

# 创建应用目录
sudo mkdir -p /opt/qos-market-api/{bin,configs,data,logs,web}
sudo chown -R qos-api:qos-api /opt/qos-market-api
```

#### 部署应用文件

```bash
# 1. 构建生产版本
make build-linux

# 2. 复制应用文件
sudo cp bin/qos-market-api-linux /opt/qos-market-api/bin/qos-market-api
sudo cp -r configs/* /opt/qos-market-api/configs/
sudo cp -r web/* /opt/qos-market-api/web/

# 3. 设置权限
sudo chown -R qos-api:qos-api /opt/qos-market-api
sudo chmod +x /opt/qos-market-api/bin/qos-market-api

# 4. 配置生产环境参数
sudo vim /opt/qos-market-api/configs/config.yaml
```

#### 生产环境配置示例

```yaml
# /opt/qos-market-api/configs/config.yaml
server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: 30s
  write_timeout: 30s
  idle_timeout: 120s

qos:
  api_key: "your_production_api_key"
  request_timeout: 30s
  max_retries: 3

cache:
  memory:
    max_size: 2000
    ttl: 300s
  recent_kline:
    ttl: 30s
  history_kline:
    ttl: 86400s

logging:
  level: "info"
  format: "json"
  output: "file"
  file:
    path: "/opt/qos-market-api/logs/qos-market-api.log"
    max_size: 100
    max_backups: 10
    max_age: 30
    compress: true

monitoring:
  enabled: true
  metrics_port: 9090

correction:
  enabled: true
  database_path: "/opt/qos-market-api/data/corrections.db"
  refresh_interval: 60s

recovery:
  restart_on_panic: true
  health_check_interval: 30s
```

#### 创建 systemd 服务

```bash
sudo tee /etc/systemd/system/qos-market-api.service > /dev/null <<EOF
[Unit]
Description=QOS Market API Service
Documentation=https://github.com/your-org/qos-market-api
After=network.target network-online.target
Wants=network-online.target

[Service]
Type=simple
User=qos-api
Group=qos-api
WorkingDirectory=/opt/qos-market-api
ExecStart=/opt/qos-market-api/bin/qos-market-api -config=/opt/qos-market-api/configs/config.yaml
ExecReload=/bin/kill -HUP \$MAINPID

# 重启策略
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# 资源限制
LimitNOFILE=65536
LimitNPROC=32768

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ReadWritePaths=/opt/qos-market-api/data /opt/qos-market-api/logs

# 日志设置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=qos-market-api

[Install]
WantedBy=multi-user.target
EOF

# 重载 systemd 配置
sudo systemctl daemon-reload

# 启用开机自启
sudo systemctl enable qos-market-api

# 启动服务
sudo systemctl start qos-market-api

# 查看服务状态
sudo systemctl status qos-market-api

# 查看日志
sudo journalctl -u qos-market-api -f
```

#### 服务管理命令

```bash
# 启动服务
sudo systemctl start qos-market-api

# 停止服务
sudo systemctl stop qos-market-api

# 重启服务
sudo systemctl restart qos-market-api

# 重载配置（无需重启）
sudo systemctl reload qos-market-api

# 查看服务状态
sudo systemctl status qos-market-api

# 查看实时日志
sudo journalctl -u qos-market-api -f

# 查看最近日志
sudo journalctl -u qos-market-api --since "1 hour ago"
```

## 详细配置说明

### 完整配置文件结构

```yaml
# 服务器配置
server:
  host: "0.0.0.0"           # 监听地址，0.0.0.0 表示所有接口
  port: 8080                # HTTP API 端口
  read_timeout: 30s         # 读取超时
  write_timeout: 30s        # 写入超时
  idle_timeout: 120s        # 空闲连接超时

# QOS.HK API 配置
qos:
  api_key: "your_api_key"   # QOS.HK API 密钥（必需）
  base_url: "https://qos.hk" # API 基础URL
  websocket_url: "wss://qos.hk/ws" # WebSocket URL
  request_timeout: 30s      # 请求超时时间
  max_retries: 3           # 最大重试次数
  retry_delay: 1s          # 重试延迟

# 缓存配置
cache:
  memory:
    max_size: 2000         # 内存缓存最大条目数
    ttl: 300s             # 默认缓存时间
  snapshot:
    ttl: 2s               # 快照数据缓存时间
  depth:
    ttl: 1s               # 盘口深度缓存时间
  recent_kline:
    ttl: 30s              # 最近K线缓存时间
  history_kline:
    ttl: 86400s           # 历史K线缓存时间（24小时）

# K线优化配置
kline:
  recent:
    enabled: true         # 启用最近K线服务
    supported_types: ["1", "5", "15", "30", "60", "1001"] # 支持的K线类型
    update_frequency: 30s # 更新频率
    max_periods: 1000    # 最大周期数
  history:
    cache_expiration: 86400s # 历史数据缓存过期时间

# WebSocket 配置
websocket:
  enabled: true           # 启用WebSocket服务
  max_connections: 1000   # 最大连接数
  ping_interval: 30s      # 心跳间隔
  pong_timeout: 10s       # 心跳超时

# 日志配置
logging:
  level: "info"           # 日志级别: debug, info, warn, error
  format: "json"          # 日志格式: text, json
  output: "file"          # 输出方式: stdout, file, both
  file:
    path: "logs/qos-market-api.log"
    max_size: 100         # 单个日志文件最大大小(MB)
    max_backups: 10       # 保留的日志文件数量
    max_age: 30           # 日志文件保留天数
    compress: true        # 是否压缩旧日志文件

# 监控配置
monitoring:
  enabled: true           # 启用监控
  metrics_port: 9090      # 监控指标端口
  health_check_interval: 30s # 健康检查间隔

# K线修复配置
correction:
  enabled: true           # 启用修复功能
  database_path: "data/corrections.db" # 数据库文件路径
  refresh_interval: 60s   # 缓存刷新间隔

# 恢复和容错配置
recovery:
  restart_on_panic: true  # 发生panic时自动重启
  health_check_interval: 30s # 健康检查间隔
  max_restart_attempts: 5 # 最大重启尝试次数

# 支持的交易品种配置
symbols:
  us_stocks: ["AAPL", "TSLA", "MSFT", "GOOGL"] # 美股
  hk_stocks: ["700", "9988", "1810"]           # 港股
  crypto: ["BTCUSDT", "ETHUSDT"]               # 数字货币
```

### 环境特定配置

#### 开发环境配置

```yaml
# configs/config.dev.yaml
logging:
  level: "debug"
  format: "text"
  output: "stdout"

cache:
  memory:
    max_size: 500
    ttl: 60s

monitoring:
  enabled: true
  metrics_port: 9090

correction:
  enabled: true
  database_path: "data/corrections_dev.db"
```

#### 生产环境配置

```yaml
# configs/config.prod.yaml
server:
  read_timeout: 30s
  write_timeout: 30s
  idle_timeout: 300s

logging:
  level: "info"
  format: "json"
  output: "file"
  file:
    path: "/opt/qos-market-api/logs/qos-market-api.log"
    max_size: 200
    max_backups: 20
    max_age: 60
    compress: true

cache:
  memory:
    max_size: 5000
    ttl: 300s
  recent_kline:
    ttl: 30s
  history_kline:
    ttl: 86400s

recovery:
  restart_on_panic: true
  health_check_interval: 30s
  max_restart_attempts: 10
```

## 监控和维护

### 健康检查和状态监控

#### 基础健康检查

```bash
# 应用健康检查
curl http://localhost:8080/health

# 监控端点健康检查
curl http://localhost:9090/health

# 获取完整指标数据
curl http://localhost:9090/metrics

# 检查特定功能状态
curl http://localhost:8080/api/v1/recent-kline-status
curl http://localhost:8080/api/v1/corrections/stats
```

#### 详细状态检查

```bash
# 检查服务连接状态
curl -s http://localhost:8080/health | jq '.services'

# 检查缓存状态
curl -s http://localhost:9090/metrics | grep cache

# 检查延迟统计
curl -s http://localhost:9090/metrics | grep latency

# 检查错误统计
curl -s http://localhost:9090/metrics | grep error
```

### 日志监控和分析

#### 日志查看命令

```bash
# 查看实时日志
tail -f /opt/qos-market-api/logs/qos-market-api.log

# 查看最近的错误日志
grep ERROR /opt/qos-market-api/logs/qos-market-api.log | tail -20

# 使用 systemd 查看服务日志
sudo journalctl -u qos-market-api -f

# 查看特定时间段的日志
sudo journalctl -u qos-market-api --since "2024-01-15 10:00:00" --until "2024-01-15 11:00:00"

# 查看用户请求日志
grep "User request" /opt/qos-market-api/logs/qos-market-api.log | tail -10
```

#### 日志分析脚本

```bash
#!/bin/bash
# log_analysis.sh - 日志分析脚本

LOG_FILE="/opt/qos-market-api/logs/qos-market-api.log"

echo "=== QOS Market API 日志分析报告 ==="
echo "时间范围: $(date)"
echo

echo "1. 错误统计:"
grep ERROR "$LOG_FILE" | wc -l
echo

echo "2. 最近10个错误:"
grep ERROR "$LOG_FILE" | tail -10
echo

echo "3. API请求统计:"
grep "User request" "$LOG_FILE" | grep SUCCESS | wc -l
echo "成功请求数"
grep "User request" "$LOG_FILE" | grep ERROR | wc -l
echo "失败请求数"
echo

echo "4. 热门API端点:"
grep "User request" "$LOG_FILE" | grep -o 'endpoint=[^ ]*' | sort | uniq -c | sort -nr | head -5
echo

echo "5. 平均响应时间:"
grep "User request" "$LOG_FILE" | grep -o 'latency_ms=[0-9]*' | cut -d= -f2 | awk '{sum+=$1; count++} END {if(count>0) print sum/count "ms"}'
```

### 性能监控

#### Prometheus 集成

创建 Prometheus 配置文件：

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'qos-market-api'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 10s
    metrics_path: /metrics
```

#### Grafana 仪表板

推荐监控指标：

1. **系统指标**
   - CPU 使用率
   - 内存使用率
   - 协程数量
   - 服务运行时间

2. **业务指标**
   - API 请求量和成功率
   - 响应时间分布
   - 缓存命中率
   - QOS API 调用统计

3. **错误监控**
   - 错误率趋势
   - 错误类型分布
   - 连接失败次数

#### 告警规则示例

```yaml
# alerting_rules.yml
groups:
  - name: qos-market-api
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "QOS Market API high error rate"

      - alert: HighLatency
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "QOS Market API high latency"

      - alert: ServiceDown
        expr: up{job="qos-market-api"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "QOS Market API service is down"
```

### 自动化监控脚本

```bash
#!/bin/bash
# health_monitor.sh - 健康监控脚本

API_URL="http://localhost:8080"
METRICS_URL="http://localhost:9090"
ALERT_EMAIL="<EMAIL>"

# 检查API健康状态
check_api_health() {
    local response=$(curl -s -w "%{http_code}" "$API_URL/health" -o /dev/null)
    if [ "$response" != "200" ]; then
        echo "ALERT: API health check failed (HTTP $response)"
        # 发送告警邮件
        echo "QOS Market API health check failed" | mail -s "API Alert" "$ALERT_EMAIL"
        return 1
    fi
    return 0
}

# 检查关键指标
check_metrics() {
    local error_rate=$(curl -s "$METRICS_URL/metrics" | grep 'http_requests_total.*5' | awk '{sum+=$2} END {print sum+0}')
    local total_requests=$(curl -s "$METRICS_URL/metrics" | grep 'http_requests_total' | awk '{sum+=$2} END {print sum+0}')

    if [ "$total_requests" -gt 0 ]; then
        local error_percentage=$(echo "scale=2; $error_rate * 100 / $total_requests" | bc)
        if (( $(echo "$error_percentage > 5" | bc -l) )); then
            echo "ALERT: High error rate: $error_percentage%"
            return 1
        fi
    fi
    return 0
}

# 主监控循环
main() {
    echo "$(date): Starting health monitoring..."

    while true; do
        if check_api_health && check_metrics; then
            echo "$(date): All checks passed"
        else
            echo "$(date): Health check failed"
        fi

        sleep 60  # 每分钟检查一次
    done
}

main "$@"
```

## 负载均衡和高可用

### Nginx 反向代理配置

#### 基础配置

```nginx
# /etc/nginx/sites-available/qos-market-api
upstream qos_api_backend {
    # 负载均衡策略
    least_conn;  # 最少连接数

    # API 服务实例
    server 12*******:8080 weight=1 max_fails=3 fail_timeout=30s;
    server 12*******:8081 weight=1 max_fails=3 fail_timeout=30s backup;

    # 健康检查
    keepalive 32;
}

upstream qos_metrics_backend {
    server 12*******:9090;
    server 12*******:9091 backup;
}

# HTTP 服务配置
server {
    listen 80;
    server_name api.yourcompany.com;

    # 重定向到 HTTPS
    return 301 https://$server_name$request_uri;
}

# HTTPS 服务配置
server {
    listen 443 ssl http2;
    server_name api.yourcompany.com;

    # SSL 证书配置
    ssl_certificate /etc/ssl/certs/api.yourcompany.com.crt;
    ssl_certificate_key /etc/ssl/private/api.yourcompany.com.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

    # 日志配置
    access_log /var/log/nginx/qos-api-access.log;
    error_log /var/log/nginx/qos-api-error.log;

    # 限流配置
    limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
    limit_req zone=api_limit burst=20 nodelay;

    # API 接口代理
    location /api/ {
        proxy_pass http://qos_api_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;

        # 缓存设置（针对静态数据）
        location ~* /api/v1/(symbols|instrument) {
            proxy_pass http://qos_api_backend;
            proxy_cache api_cache;
            proxy_cache_valid 200 5m;
            proxy_cache_key "$scheme$request_method$host$request_uri";
            add_header X-Cache-Status $upstream_cache_status;
        }
    }

    # WebSocket 代理
    location /ws {
        proxy_pass http://qos_api_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # WebSocket 特定设置
        proxy_read_timeout 86400s;
        proxy_send_timeout 86400s;
    }

    # 监控接口代理（限制访问）
    location /metrics {
        allow 10.0.0.0/8;
        allow **********/12;
        allow ***********/16;
        deny all;

        proxy_pass http://qos_metrics_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # Web 界面
    location / {
        proxy_pass http://qos_api_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 健康检查端点
    location /health {
        proxy_pass http://qos_api_backend;
        access_log off;
    }
}

# 缓存配置
proxy_cache_path /var/cache/nginx/api_cache levels=1:2 keys_zone=api_cache:10m max_size=100m inactive=60m use_temp_path=off;
```

### 多实例部署

#### 配置多个服务实例

```bash
# 实例1配置 (端口8080)
sudo cp /opt/qos-market-api/configs/config.yaml /opt/qos-market-api/configs/config-8080.yaml
sudo sed -i 's/port: 8080/port: 8080/' /opt/qos-market-api/configs/config-8080.yaml
sudo sed -i 's/metrics_port: 9090/metrics_port: 9090/' /opt/qos-market-api/configs/config-8080.yaml

# 实例2配置 (端口8081)
sudo cp /opt/qos-market-api/configs/config.yaml /opt/qos-market-api/configs/config-8081.yaml
sudo sed -i 's/port: 8080/port: 8081/' /opt/qos-market-api/configs/config-8081.yaml
sudo sed -i 's/metrics_port: 9090/metrics_port: 9091/' /opt/qos-market-api/configs/config-8081.yaml

# 创建第二个服务实例
sudo cp /etc/systemd/system/qos-market-api.service /etc/systemd/system/qos-market-api-2.service
sudo sed -i 's/config.yaml/config-8081.yaml/' /etc/systemd/system/qos-market-api-2.service
sudo sed -i 's/Description=QOS Market API Service/Description=QOS Market API Service Instance 2/' /etc/systemd/system/qos-market-api-2.service

# 启动多个实例
sudo systemctl daemon-reload
sudo systemctl enable qos-market-api-2
sudo systemctl start qos-market-api-2
```

## 安全配置

### 网络安全

#### 防火墙配置

```bash
# 使用 ufw 配置防火墙
sudo ufw enable

# 允许 SSH 访问
sudo ufw allow ssh

# 允许 HTTP/HTTPS 访问
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# 限制 API 端口访问（仅内网）
sudo ufw allow from 10.0.0.0/8 to any port 8080
sudo ufw allow from **********/12 to any port 8080
sudo ufw allow from ***********/16 to any port 8080

# 限制监控端口访问
sudo ufw allow from 10.0.0.0/8 to any port 9090

# 查看防火墙状态
sudo ufw status verbose
```

#### SSL/TLS 配置

```bash
# 使用 Let's Encrypt 获取免费证书
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d api.yourcompany.com

# 自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

### 应用安全

#### 用户权限配置

```bash
# 创建专用用户组
sudo groupadd qos-api-group

# 设置文件权限
sudo chown -R qos-api:qos-api-group /opt/qos-market-api
sudo chmod -R 750 /opt/qos-market-api
sudo chmod 640 /opt/qos-market-api/configs/config.yaml

# 保护敏感文件
sudo chmod 600 /opt/qos-market-api/configs/config.yaml
sudo chattr +i /opt/qos-market-api/configs/config.yaml  # 防止意外修改
```

#### API 密钥安全

```bash
# 使用环境变量存储敏感信息
export QOS_API_KEY="your_secret_api_key"

# 在配置文件中引用环境变量
# config.yaml
qos:
  api_key: "${QOS_API_KEY}"

# 或使用专门的密钥管理工具
# 例如 HashiCorp Vault, AWS Secrets Manager 等
```

#### 依赖安全

```bash
# 定期检查依赖漏洞
go list -json -m all | nancy sleuth

# 更新依赖
go get -u ./...
go mod tidy

# 使用 Go 安全检查工具
go install golang.org/x/vuln/cmd/govulncheck@latest
govulncheck ./...
```

### 数据安全

#### 数据库安全

```bash
# 设置数据库文件权限
sudo chmod 600 /opt/qos-market-api/data/corrections.db
sudo chown qos-api:qos-api /opt/qos-market-api/data/corrections.db

# 定期备份数据库
#!/bin/bash
# backup_db.sh
BACKUP_DIR="/opt/qos-market-api/backups"
DB_FILE="/opt/qos-market-api/data/corrections.db"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

mkdir -p "$BACKUP_DIR"
cp "$DB_FILE" "$BACKUP_DIR/corrections_${TIMESTAMP}.db"

# 保留最近30天的备份
find "$BACKUP_DIR" -name "corrections_*.db" -mtime +30 -delete
```

#### 日志安全

```bash
# 设置日志文件权限
sudo chmod 640 /opt/qos-market-api/logs/*.log
sudo chown qos-api:adm /opt/qos-market-api/logs/*.log

# 配置日志轮转
sudo tee /etc/logrotate.d/qos-market-api > /dev/null <<EOF
/opt/qos-market-api/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 640 qos-api adm
    postrotate
        systemctl reload qos-market-api
    endscript
}
EOF
```

### 访问控制

#### API 访问限制

```nginx
# 在 Nginx 配置中添加访问控制
location /api/v1/corrections {
    # 限制管理接口访问
    allow ***********/24;
    deny all;

    proxy_pass http://qos_api_backend;
}

# 添加基本认证
location /corrections {
    auth_basic "QOS API Admin";
    auth_basic_user_file /etc/nginx/.htpasswd;

    proxy_pass http://qos_api_backend;
}
```

#### 创建认证文件

```bash
# 安装 htpasswd 工具
sudo apt install apache2-utils

# 创建用户认证文件
sudo htpasswd -c /etc/nginx/.htpasswd admin
sudo htpasswd /etc/nginx/.htpasswd operator

# 设置权限
sudo chmod 644 /etc/nginx/.htpasswd
sudo chown root:www-data /etc/nginx/.htpasswd
```

### 安全监控

#### 入侵检测

```bash
# 安装 fail2ban
sudo apt install fail2ban

# 配置 fail2ban 规则
sudo tee /etc/fail2ban/jail.local > /dev/null <<EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
logpath = /var/log/nginx/error.log
maxretry = 3

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
logpath = /var/log/nginx/error.log
maxretry = 10
EOF

sudo systemctl restart fail2ban
```

#### 安全审计

```bash
#!/bin/bash
# security_audit.sh - 安全审计脚本

echo "=== QOS Market API 安全审计报告 ==="
echo "时间: $(date)"
echo

# 检查文件权限
echo "1. 文件权限检查:"
ls -la /opt/qos-market-api/configs/
ls -la /opt/qos-market-api/data/
echo

# 检查运行用户
echo "2. 进程用户检查:"
ps aux | grep qos-market-api | grep -v grep
echo

# 检查网络连接
echo "3. 网络连接检查:"
netstat -tlnp | grep -E ':(8080|9090)'
echo

# 检查最近的登录
echo "4. 最近登录检查:"
last -n 10
echo

# 检查系统日志中的异常
echo "5. 安全日志检查:"
grep -i "authentication failure\|invalid user\|failed password" /var/log/auth.log | tail -5
```

## 故障排除

### 常见问题诊断

#### 1. 服务启动失败

**症状**: 服务无法启动或立即退出

**诊断步骤**:
```bash
# 检查配置文件语法
/opt/qos-market-api/bin/qos-market-api -config=/opt/qos-market-api/configs/config.yaml -validate

# 检查端口占用
sudo netstat -tlnp | grep -E ':(8080|9090)'
sudo lsof -i :8080

# 检查文件权限
ls -la /opt/qos-market-api/
ls -la /opt/qos-market-api/configs/config.yaml

# 查看详细错误信息
sudo journalctl -u qos-market-api -n 50 --no-pager
```

**常见解决方案**:
- 修复配置文件语法错误
- 更改端口或停止占用端口的进程
- 修正文件权限：`sudo chown -R qos-api:qos-api /opt/qos-market-api`

#### 2. API 请求失败

**症状**: API 返回错误或超时

**诊断步骤**:
```bash
# 检查服务健康状态
curl -v http://localhost:8080/health

# 检查 QOS.HK 连接
curl -v --connect-timeout 10 https://qos.hk

# 验证 API 密钥配置
sudo grep -A5 "qos:" /opt/qos-market-api/configs/config.yaml

# 检查网络连接
ping -c 4 qos.hk
nslookup qos.hk

# 查看 API 调用日志
grep "QOS API" /opt/qos-market-api/logs/qos-market-api.log | tail -10
```

**常见解决方案**:
- 验证 API 密钥是否正确和有效
- 检查网络连接和防火墙设置
- 增加请求超时时间
- 检查 QOS.HK 服务状态

#### 3. 内存使用过高

**症状**: 系统内存不足，服务被 OOM Killer 终止

**诊断步骤**:
```bash
# 检查内存使用情况
free -h
ps aux --sort=-%mem | head -10

# 检查服务内存使用
sudo systemctl status qos-market-api
pmap -x $(pgrep qos-market-api)

# 查看 OOM 日志
dmesg | grep -i "killed process"
grep -i "out of memory" /var/log/syslog
```

**解决方案**:
```bash
# 调整缓存配置
sudo vim /opt/qos-market-api/configs/config.yaml
# 减少以下配置值：
# cache.memory.max_size: 1000  # 从 2000 减少到 1000
# kline.recent.max_periods: 500  # 从 1000 减少到 500

# 重启服务
sudo systemctl restart qos-market-api
```

#### 4. 高延迟问题

**症状**: API 响应时间过长

**诊断步骤**:
```bash
# 检查延迟指标
curl -s http://localhost:9090/metrics | grep latency

# 测试 API 响应时间
time curl -s http://localhost:8080/api/v1/snapshot?codes=US:AAPL

# 检查缓存命中率
curl -s http://localhost:9090/metrics | grep cache_hit

# 查看慢查询日志
grep "latency_ms" /opt/qos-market-api/logs/qos-market-api.log | awk '$NF > 1000' | tail -10
```

**解决方案**:
- 优化缓存配置，增加缓存时间
- 检查数据库性能（如果使用修复功能）
- 增加服务实例进行负载分担

#### 5. WebSocket 连接问题

**症状**: WebSocket 连接失败或频繁断开

**诊断步骤**:
```bash
# 测试 WebSocket 连接
wscat -c ws://localhost:8080/ws

# 检查连接数
curl -s http://localhost:9090/metrics | grep websocket

# 查看 WebSocket 日志
grep -i websocket /opt/qos-market-api/logs/qos-market-api.log | tail -20
```

### 高级故障排除

#### 性能分析

```bash
# 使用 pprof 进行性能分析
go tool pprof http://localhost:8080/debug/pprof/profile
go tool pprof http://localhost:8080/debug/pprof/heap

# 生成火焰图
go tool pprof -http=:8081 http://localhost:8080/debug/pprof/profile
```

#### 网络问题诊断

```bash
# 检查网络连接
ss -tulpn | grep -E ':(8080|9090)'

# 测试网络延迟
ping -c 10 qos.hk
traceroute qos.hk

# 检查 DNS 解析
dig qos.hk
nslookup qos.hk
```

#### 数据库问题（修复功能）

```bash
# 检查数据库文件
ls -la /opt/qos-market-api/data/corrections.db

# 验证数据库完整性
sqlite3 /opt/qos-market-api/data/corrections.db "PRAGMA integrity_check;"

# 查看数据库统计
sqlite3 /opt/qos-market-api/data/corrections.db "SELECT COUNT(*) FROM kline_corrections;"
```

### 日志分析工具

#### 日志分析脚本

```bash
#!/bin/bash
# troubleshoot.sh - 故障排除脚本

LOG_FILE="/opt/qos-market-api/logs/qos-market-api.log"
METRICS_URL="http://localhost:9090/metrics"

echo "=== QOS Market API 故障排除报告 ==="
echo "生成时间: $(date)"
echo

# 1. 服务状态检查
echo "1. 服务状态:"
systemctl is-active qos-market-api
systemctl is-enabled qos-market-api
echo

# 2. 端口检查
echo "2. 端口状态:"
netstat -tlnp | grep -E ':(8080|9090)'
echo

# 3. 最近错误
echo "3. 最近错误 (最近10条):"
grep ERROR "$LOG_FILE" | tail -10
echo

# 4. 内存使用
echo "4. 内存使用:"
free -h
ps aux | grep qos-market-api | grep -v grep
echo

# 5. 磁盘使用
echo "5. 磁盘使用:"
df -h /opt/qos-market-api
du -sh /opt/qos-market-api/logs/*
echo

# 6. 网络连接测试
echo "6. 网络连接:"
curl -s --connect-timeout 5 https://qos.hk > /dev/null && echo "QOS.HK: OK" || echo "QOS.HK: FAILED"
curl -s --connect-timeout 5 http://localhost:8080/health > /dev/null && echo "API Health: OK" || echo "API Health: FAILED"
echo

# 7. 关键指标
echo "7. 关键指标:"
if curl -s "$METRICS_URL" > /dev/null 2>&1; then
    echo "缓存命中率: $(curl -s "$METRICS_URL" | grep cache_hit_rate | head -1)"
    echo "HTTP请求总数: $(curl -s "$METRICS_URL" | grep http_requests_total | head -1)"
    echo "平均延迟: $(curl -s "$METRICS_URL" | grep latency_avg | head -1)"
else
    echo "无法获取指标数据"
fi
```

#### 实时监控脚本

```bash
#!/bin/bash
# monitor.sh - 实时监控脚本

watch -n 5 '
echo "=== QOS Market API 实时监控 ==="
echo "时间: $(date)"
echo
echo "服务状态: $(systemctl is-active qos-market-api)"
echo "内存使用: $(ps aux | grep qos-market-api | grep -v grep | awk "{print \$4\"%\"}")"
echo "连接数: $(netstat -an | grep :8080 | grep ESTABLISHED | wc -l)"
echo
echo "最近5个请求:"
tail -5 /opt/qos-market-api/logs/qos-market-api.log | grep "User request"
'
```

## 升级指南

### 升级前准备

#### 1. 版本兼容性检查

```bash
# 检查当前版本
/opt/qos-market-api/bin/qos-market-api -version

# 查看发布说明
curl -s https://api.github.com/repos/your-org/qos-market-api/releases/latest | jq '.body'

# 检查配置文件兼容性
diff configs/config.example.yaml /opt/qos-market-api/configs/config.yaml
```

#### 2. 完整备份

```bash
#!/bin/bash
# backup.sh - 升级前备份脚本

BACKUP_DIR="/opt/qos-market-api/backups/$(date +%Y%m%d_%H%M%S)"
APP_DIR="/opt/qos-market-api"

echo "创建备份目录: $BACKUP_DIR"
sudo mkdir -p "$BACKUP_DIR"

echo "备份应用程序..."
sudo cp "$APP_DIR/bin/qos-market-api" "$BACKUP_DIR/"

echo "备份配置文件..."
sudo cp -r "$APP_DIR/configs" "$BACKUP_DIR/"

echo "备份数据库..."
sudo cp -r "$APP_DIR/data" "$BACKUP_DIR/"

echo "备份日志..."
sudo cp -r "$APP_DIR/logs" "$BACKUP_DIR/"

echo "备份 systemd 服务文件..."
sudo cp /etc/systemd/system/qos-market-api.service "$BACKUP_DIR/"

echo "创建备份清单..."
sudo find "$BACKUP_DIR" -type f > "$BACKUP_DIR/backup_manifest.txt"

echo "备份完成: $BACKUP_DIR"
sudo chown -R qos-api:qos-api "$BACKUP_DIR"
```

### 升级流程

#### 标准升级（无停机时间）

```bash
#!/bin/bash
# upgrade.sh - 标准升级脚本

set -e  # 遇到错误立即退出

BACKUP_DIR="/opt/qos-market-api/backups/$(date +%Y%m%d_%H%M%S)"
APP_DIR="/opt/qos-market-api"
NEW_VERSION="$1"

if [ -z "$NEW_VERSION" ]; then
    echo "用法: $0 <version>"
    exit 1
fi

echo "=== QOS Market API 升级到版本 $NEW_VERSION ==="

# 1. 创建备份
echo "1. 创建备份..."
sudo mkdir -p "$BACKUP_DIR"
sudo cp "$APP_DIR/bin/qos-market-api" "$BACKUP_DIR/"
sudo cp -r "$APP_DIR/configs" "$BACKUP_DIR/"
sudo cp -r "$APP_DIR/data" "$BACKUP_DIR/"

# 2. 下载新版本
echo "2. 下载新版本..."
cd /tmp
wget "https://github.com/your-org/qos-market-api/releases/download/v${NEW_VERSION}/qos-market-api-linux-amd64.tar.gz"
tar -xzf "qos-market-api-linux-amd64.tar.gz"

# 3. 验证新版本
echo "3. 验证新版本..."
./qos-market-api -version

# 4. 停止服务
echo "4. 停止服务..."
sudo systemctl stop qos-market-api

# 5. 替换二进制文件
echo "5. 更新应用程序..."
sudo cp qos-market-api "$APP_DIR/bin/"
sudo chown qos-api:qos-api "$APP_DIR/bin/qos-market-api"
sudo chmod +x "$APP_DIR/bin/qos-market-api"

# 6. 更新配置（如果需要）
echo "6. 检查配置更新..."
if [ -f "config.example.yaml" ]; then
    echo "发现新的配置模板，请手动检查配置更新"
    sudo cp config.example.yaml "$APP_DIR/configs/config.example.yaml.new"
fi

# 7. 启动服务
echo "7. 启动服务..."
sudo systemctl start qos-market-api

# 8. 验证升级
echo "8. 验证升级..."
sleep 5
if sudo systemctl is-active qos-market-api > /dev/null; then
    echo "✓ 服务启动成功"
else
    echo "✗ 服务启动失败，开始回滚..."
    sudo systemctl stop qos-market-api
    sudo cp "$BACKUP_DIR/qos-market-api" "$APP_DIR/bin/"
    sudo systemctl start qos-market-api
    echo "回滚完成"
    exit 1
fi

# 9. 功能验证
echo "9. 功能验证..."
if curl -s http://localhost:8080/health > /dev/null; then
    echo "✓ API 健康检查通过"
else
    echo "✗ API 健康检查失败"
    exit 1
fi

if curl -s http://localhost:9090/metrics > /dev/null; then
    echo "✓ 监控指标正常"
else
    echo "✗ 监控指标异常"
fi

echo "=== 升级完成 ==="
echo "新版本: $(/opt/qos-market-api/bin/qos-market-api -version)"
echo "备份位置: $BACKUP_DIR"
```

#### 蓝绿部署升级

```bash
#!/bin/bash
# blue_green_upgrade.sh - 蓝绿部署升级

# 配置
BLUE_PORT=8080
GREEN_PORT=8081
NGINX_CONFIG="/etc/nginx/sites-available/qos-market-api"

# 检查当前活跃实例
CURRENT_PORT=$(curl -s http://localhost/health | jq -r '.port // 8080')

if [ "$CURRENT_PORT" = "$BLUE_PORT" ]; then
    NEW_PORT=$GREEN_PORT
    OLD_PORT=$BLUE_PORT
    NEW_SERVICE="qos-market-api-green"
    OLD_SERVICE="qos-market-api-blue"
else
    NEW_PORT=$BLUE_PORT
    OLD_PORT=$GREEN_PORT
    NEW_SERVICE="qos-market-api-blue"
    OLD_SERVICE="qos-market-api-green"
fi

echo "当前活跃端口: $OLD_PORT"
echo "新实例端口: $NEW_PORT"

# 1. 启动新实例
echo "启动新实例..."
sudo systemctl start "$NEW_SERVICE"
sleep 10

# 2. 验证新实例
echo "验证新实例..."
if curl -s "http://localhost:$NEW_PORT/health" > /dev/null; then
    echo "✓ 新实例健康检查通过"
else
    echo "✗ 新实例启动失败"
    sudo systemctl stop "$NEW_SERVICE"
    exit 1
fi

# 3. 切换流量
echo "切换 Nginx 流量..."
sudo sed -i "s/:$OLD_PORT/:$NEW_PORT/g" "$NGINX_CONFIG"
sudo nginx -t && sudo systemctl reload nginx

# 4. 验证切换
sleep 5
if curl -s http://localhost/health > /dev/null; then
    echo "✓ 流量切换成功"

    # 5. 停止旧实例
    echo "停止旧实例..."
    sudo systemctl stop "$OLD_SERVICE"
    echo "升级完成"
else
    echo "✗ 流量切换失败，回滚..."
    sudo sed -i "s/:$NEW_PORT/:$OLD_PORT/g" "$NGINX_CONFIG"
    sudo systemctl reload nginx
    sudo systemctl stop "$NEW_SERVICE"
    exit 1
fi
```

### 升级后验证

#### 完整功能测试

```bash
#!/bin/bash
# post_upgrade_test.sh - 升级后测试脚本

API_BASE="http://localhost:8080"
METRICS_BASE="http://localhost:9090"

echo "=== 升级后功能验证 ==="

# 1. 基础健康检查
echo "1. 健康检查..."
if curl -s "$API_BASE/health" | jq -e '.status == "ok"' > /dev/null; then
    echo "✓ 健康检查通过"
else
    echo "✗ 健康检查失败"
    exit 1
fi

# 2. API 功能测试
echo "2. API 功能测试..."
ENDPOINTS=(
    "/api/v1/symbols"
    "/api/v1/snapshot?codes=US:AAPL"
    "/api/v1/kline?codes=US:AAPL&kline_type=1001&count=1"
)

for endpoint in "${ENDPOINTS[@]}"; do
    if curl -s "$API_BASE$endpoint" | jq -e '.msg == "OK"' > /dev/null; then
        echo "✓ $endpoint"
    else
        echo "✗ $endpoint"
    fi
done

# 3. 监控指标检查
echo "3. 监控指标检查..."
if curl -s "$METRICS_BASE/metrics" > /dev/null; then
    echo "✓ 监控指标正常"
else
    echo "✗ 监控指标异常"
fi

# 4. 修复功能检查（如果启用）
echo "4. 修复功能检查..."
if curl -s "$API_BASE/api/v1/corrections/stats" > /dev/null; then
    echo "✓ 修复功能正常"
else
    echo "✓ 修复功能未启用或异常"
fi

# 5. WebSocket 连接测试
echo "5. WebSocket 连接测试..."
timeout 10 wscat -c "ws://localhost:8080/ws" -x '{"type":"PING"}' > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✓ WebSocket 连接正常"
else
    echo "✗ WebSocket 连接异常"
fi

echo "=== 验证完成 ==="
```

### 回滚程序

```bash
#!/bin/bash
# rollback.sh - 回滚脚本

BACKUP_DIR="$1"

if [ -z "$BACKUP_DIR" ] || [ ! -d "$BACKUP_DIR" ]; then
    echo "用法: $0 <backup_directory>"
    echo "可用备份:"
    ls -la /opt/qos-market-api/backups/
    exit 1
fi

echo "=== 开始回滚到 $BACKUP_DIR ==="

# 1. 停止服务
echo "1. 停止服务..."
sudo systemctl stop qos-market-api

# 2. 恢复二进制文件
echo "2. 恢复应用程序..."
sudo cp "$BACKUP_DIR/qos-market-api" /opt/qos-market-api/bin/

# 3. 恢复配置文件
echo "3. 恢复配置..."
sudo cp -r "$BACKUP_DIR/configs"/* /opt/qos-market-api/configs/

# 4. 恢复数据库
echo "4. 恢复数据库..."
sudo cp -r "$BACKUP_DIR/data"/* /opt/qos-market-api/data/

# 5. 恢复服务文件
echo "5. 恢复服务配置..."
sudo cp "$BACKUP_DIR/qos-market-api.service" /etc/systemd/system/
sudo systemctl daemon-reload

# 6. 启动服务
echo "6. 启动服务..."
sudo systemctl start qos-market-api

# 7. 验证回滚
echo "7. 验证回滚..."
sleep 5
if sudo systemctl is-active qos-market-api > /dev/null; then
    echo "✓ 服务启动成功"
    if curl -s http://localhost:8080/health > /dev/null; then
        echo "✓ API 功能正常"
        echo "=== 回滚完成 ==="
    else
        echo "✗ API 功能异常"
    fi
else
    echo "✗ 服务启动失败"
    exit 1
fi
```

## 相关文档

- [API 接口文档](API.md) - 完整的 API 接口说明
- [K线修复文档](CORRECTION.md) - K线数据修复功能详解
- [监控统计文档](STATS.md) - 监控指标和统计功能
- [延迟跟踪文档](LATENCY_TRACKING.md) - 延迟跟踪和性能优化
