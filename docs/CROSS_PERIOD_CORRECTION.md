# K线跨周期修复功能

## 概述

K线跨周期修复功能是对原有K线修复系统的重要优化，允许录入的修复值自动影响不同周期的K线数据。例如，当修复某天的最高价时，系统会自动计算并应用到包含该时间点的周线、月线等更大周期的K线数据中。

## 功能特性

### ✅ 已实现功能

- **智能周期影响计算**: 系统自动计算修复数据会影响哪些K线周期
- **跨周期数据传播**: 修复数据可以自动传播到相关的其他周期
- **时间戳对齐**: 精确计算不同周期的时间边界和对齐规则
- **Web界面优化**: 显示修复数据的影响范围预览
- **数据库扩展**: 支持存储影响范围和自动传播配置
- **完整测试**: 包含跨周期修复功能的测试用例

### 🔄 传播规则

#### 最高价 (h) 和最低价 (l)
- **向上传播**: 影响包含该时间点的所有更大周期
- **规则**: 
  - 最高价修复：如果修复值比目标周期当前最高价更高，则更新
  - 最低价修复：如果修复值比目标周期当前最低价更低，则更新

#### 开盘价 (o) 和收盘价 (cl)
- **条件传播**: 只在特定时间条件下传播
- **规则**:
  - 开盘价：只有当修复时间是目标周期的开始时间时才传播
  - 收盘价：只有当修复时间是目标周期的结束时间时才传播

#### 成交量 (v)
- **聚合传播**: 需要重新计算目标周期的总成交量
- **规则**: 影响包含该时间点的所有更大周期（需要重新聚合）

## 技术实现

### 核心组件

1. **PeriodMapper**: 周期映射器
   - 处理不同K线周期之间的时间关系
   - 计算时间戳对齐和周期边界
   - 确定修复数据的影响范围

2. **CorrectionPropagator**: 修复数据传播器
   - 负责将修复数据传播到相关周期
   - 实现不同字段的传播算法
   - 处理传播值的计算逻辑

3. **Enhanced Corrector**: 增强的修复器
   - 支持跨周期修复应用
   - 智能检测和应用传播的修复数据
   - 优化的修复逻辑

### 数据库扩展

```sql
-- 新增字段
ALTER TABLE kline_corrections ADD COLUMN affected_periods TEXT;
ALTER TABLE kline_corrections ADD COLUMN auto_propagate BOOLEAN NOT NULL DEFAULT 1;
```

- `affected_periods`: 存储影响的K线周期列表
- `auto_propagate`: 是否自动传播到其他周期

### API 增强

修复数据创建时自动计算影响范围：

```json
{
  "code": "US:AAPL",
  "kline_type": 1001,
  "timestamp": 1752206400,
  "field": "h",
  "corr_value": "250.00",
  "reason": "修复最高价",
  "affected_periods": [1001, 1007, 1030, 2001],
  "auto_propagate": true
}
```

## 使用示例

### 1. 通过Web界面

1. 访问 `http://localhost:8080/corrections`
2. 填写修复数据表单
3. 选择修复字段后，系统自动显示影响范围
4. 提交后系统自动计算并应用跨周期修复

### 2. 通过API

```bash
# 添加日线最高价修复
curl -X POST http://localhost:8080/api/v1/corrections \
  -H "Content-Type: application/json" \
  -d '{
    "code": "US:AAPL",
    "kline_type": 1001,
    "timestamp": 1752206400,
    "field": "h",
    "corr_value": "250.00",
    "reason": "修复异常最高价数据"
  }'

# 获取K线数据（已应用修复）
curl "http://localhost:8080/api/v1/kline?codes=US:AAPL&kline_type=1001&count=5"
```

### 3. 验证修复效果

修复前的K线数据：
```json
{
  "c": "US:AAPL",
  "o": "210.565",
  "cl": "211.16", 
  "h": "212.13",  // 原始最高价
  "l": "209.86",
  "v": "39765812",
  "ts": 1752206400,
  "kt": 1001
}
```

修复后的K线数据：
```json
{
  "c": "US:AAPL",
  "o": "210.565",
  "cl": "211.16",
  "h": "250.00",  // 修复后的最高价
  "l": "209.86", 
  "v": "39765812",
  "ts": 1752206400,
  "kt": 1001
}
```

## 周期层次关系

系统支持以下K线周期的层次关系：

```
1分钟 → 5分钟 → 15分钟 → 30分钟 → 1小时 → 2小时 → 4小时 → 日线 → 周线 → 月线 → 年线
```

修复数据会从小周期向大周期传播，确保数据一致性。

## 测试验证

### 单元测试

```bash
# 运行跨周期修复测试
go test ./internal/correction -v -run TestCrossPeriodCorrection

# 运行周期映射器测试  
go test ./internal/correction -v -run TestPeriodMapper
```

### 集成测试

1. 启动服务：`./bin/qos-market-api -config=configs/config.yaml`
2. 添加修复数据
3. 验证K线数据API返回的修复结果
4. 检查不同周期的数据一致性

## 配置说明

在 `configs/config.yaml` 中启用修复功能：

```yaml
correction:
  enabled: true
  database_path: "data/corrections.db"
  refresh_interval: 60s
```

## 注意事项

1. **数据一致性**: 跨周期修复确保了不同时间维度数据的一致性
2. **性能考虑**: 修复检查经过优化，对API响应时间影响最小
3. **向后兼容**: 现有的修复数据仍然有效，新功能不影响已有功能
4. **缓存刷新**: 修复数据变更后会自动刷新内存缓存

## 监控指标

通过 `/metrics` 接口可以获取修复功能的监控数据：

```json
{
  "corrections": {
    "enabled": true,
    "total_corrections": 2,
    "active_corrections": 2,
    "cache_size": 2,
    "cross_period_enabled": true,
    "affected_periods_count": {
      "1001": 4,  // 日线修复影响4个周期
      "1007": 3   // 周线修复影响3个周期
    }
  }
}
```

## 未来优化

1. **智能传播算法**: 更精确的传播值计算
2. **批量修复**: 支持批量添加跨周期修复数据
3. **修复历史**: 跟踪修复数据的传播历史
4. **可视化界面**: 图形化显示修复数据的影响范围和传播路径
