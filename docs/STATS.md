# QOS Market API 统计功能文档

## 概述

QOS Market API 现在支持详细的调用统计功能，可以跟踪缓存命中情况和QOS HTTP接口调用情况，并提供多个时间窗口的统计数据。

## 功能特性

### 1. 缓存统计
- **缓存命中次数**: 记录缓存成功命中的次数
- **缓存未命中次数**: 记录缓存未命中的次数  
- **缓存命中率**: 自动计算命中率百分比
- **时间窗口统计**: 支持总次数、最近24小时、1小时、1分钟的统计

### 2. QOS HTTP接口调用统计
- **HTTP调用次数**: 记录对QOS.HK API的HTTP调用次数
- **时间窗口统计**: 支持总次数、最近24小时、1小时、1分钟的统计

### 3. 时间窗口支持
所有统计数据都支持以下时间窗口：
- `total`: 总次数（自服务启动以来）
- `last_24_hours`: 最近24小时
- `last_hour`: 最近1小时  
- `last_minute`: 最近1分钟

## API接口

### Metrics端点

访问 `/metrics` 端点可以获取完整的统计数据：

```bash
curl http://localhost:9090/metrics
```

### 响应格式

```json
{
  "start_time": "2024-01-01T00:00:00Z",
  "uptime": "1h30m45s",
  "go_version": "go1.21.0",
  "num_goroutines": 15,
  "memory_stats": {
    "alloc": 2048576,
    "total_alloc": 10485760,
    "sys": 8388608,
    "num_gc": 5,
    "heap_alloc": 2048576,
    "heap_sys": 4194304,
    "heap_inuse": 3145728,
    "heap_released": 1048576
  },
  "qos_connected": true,
  "cache_size": 150,
  "websocket_clients": 3,
  "http_requests": {
    "total": 1250,
    "success": 1200,
    "failed": 50,
    "rate": 2.5
  },
  "websocket_events": {
    "total": 5000,
    "success": 4950,
    "failed": 50,
    "rate": 10.2
  },
  "cache": {
    "hits": {
      "total": 800,
      "last_24_hours": 800,
      "last_hour": 120,
      "last_minute": 5
    },
    "misses": {
      "total": 200,
      "last_24_hours": 200,
      "last_hour": 30,
      "last_minute": 1
    },
    "hit_rate": 80.0
  },
  "qos_api": {
    "http_calls": {
      "total": 200,
      "last_24_hours": 200,
      "last_hour": 30,
      "last_minute": 1
    }
  },
  "errors": {
    "total": 10,
    "last_error": "Connection timeout",
    "last_error_at": "2024-01-01T12:30:00Z",
    "errors_by_type": {
      "timeout": 5,
      "network": 3,
      "parse": 2
    }
  }
}
```

## 统计数据说明

### 缓存统计 (cache)

- **hits**: 缓存命中统计
  - `total`: 总命中次数
  - `last_24_hours`: 最近24小时命中次数
  - `last_hour`: 最近1小时命中次数
  - `last_minute`: 最近1分钟命中次数

- **misses**: 缓存未命中统计
  - `total`: 总未命中次数
  - `last_24_hours`: 最近24小时未命中次数
  - `last_hour`: 最近1小时未命中次数
  - `last_minute`: 最近1分钟未命中次数

- **hit_rate**: 缓存命中率（百分比）

### QOS API统计 (qos_api)

- **http_calls**: HTTP接口调用统计
  - `total`: 总调用次数
  - `last_24_hours`: 最近24小时调用次数
  - `last_hour`: 最近1小时调用次数
  - `last_minute`: 最近1分钟调用次数

## 配置

统计功能通过监控配置启用：

```yaml
# 监控配置
monitoring:
  enabled: true
  metrics_port: 9090
  health_check_interval: 30s
```

## 性能影响

统计功能设计为高性能：

- **内存使用**: 每个时间窗口收集器约占用几KB内存
- **CPU开销**: 记录操作约63ns/op，获取统计约2.3μs/op
- **自动清理**: 超过24小时的记录会自动清理，避免内存泄漏

## 使用示例

### 1. 监控缓存性能

```bash
# 获取缓存命中率
curl -s http://localhost:9090/metrics | jq '.cache.hit_rate'

# 获取最近1小时的缓存统计
curl -s http://localhost:9090/metrics | jq '.cache.hits.last_hour'
```

### 2. 监控API调用频率

```bash
# 获取最近1分钟的API调用次数
curl -s http://localhost:9090/metrics | jq '.qos_api.http_calls.last_minute'

# 获取总的API调用次数
curl -s http://localhost:9090/metrics | jq '.qos_api.http_calls.total'
```

### 3. 运行演示

```bash
# 运行统计功能演示
go run examples/stats_demo.go
```

## 故障排除

### 统计数据为0
- 确认监控功能已启用
- 检查是否有实际的缓存操作和API调用
- 验证统计记录器是否正确设置

### 时间窗口统计不准确
- 统计基于事件发生的时间戳
- 确保系统时间正确
- 超过24小时的数据会被自动清理

### 性能问题
- 统计功能开销很小，通常不会影响性能
- 如需禁用，可在配置中设置 `monitoring.enabled: false`

## 扩展

统计系统设计为可扩展的，可以轻松添加新的统计指标：

1. 在 `internal/stats` 包中添加新的收集器
2. 在监控器中集成新的统计数据
3. 更新metrics端点返回新的统计信息
