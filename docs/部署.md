# QOS 行情数据 API 部署指南

## 部署前准备

### 获取 QOS.HK API 密钥

### 端口规划

默认端口配置：
- **8080**: HTTP API 服务端口
- **9090**: 监控指标端口

## 部署方式

### 创建系统用户和目录（可选）
```bash
# 创建专用用户
sudo useradd -r -s /bin/false qos-api

# 创建应用目录
sudo mkdir -p /opt/qos-market-api/{bin,configs,data,logs,web}
sudo chown -R qos-api:qos-api /opt/qos-market-api
```

### 部署应用文件（已编译好）

```bash
# 1. 构建生产版本
make build-linux

# 2. 复制应用文件
sudo cp bin/qos-market-api-linux /opt/qos-market-api/bin/qos-market-api
sudo cp -r configs/* /opt/qos-market-api/configs/
sudo cp -r web/* /opt/qos-market-api/web/

# 3. 设置权限
sudo chown -R qos-api:qos-api /opt/qos-market-api
sudo chmod +x /opt/qos-market-api/bin/qos-market-api

# 4. 配置生产环境参数
sudo vim /opt/qos-market-api/configs/config.yaml

# 5. 运行服务
bin/qos-market-api-linux -config=configs/config.yaml
```

### 详细配置说明

#### 完整配置文件结构

```yaml
# 服务器配置
server:
  host: "0.0.0.0"           # 监听地址，0.0.0.0 表示所有接口
  port: 8080                # HTTP API 端口
  read_timeout: 30s         # 读取超时
  write_timeout: 30s        # 写入超时
  idle_timeout: 120s        # 空闲连接超时

# QOS.HK API 配置
qos:
  api_key: "your_api_key"   # QOS.HK API 密钥（必需）
  base_url: "https://qos.hk" # API 基础URL
  websocket_url: "wss://qos.hk/ws" # WebSocket URL
  request_timeout: 30s      # 请求超时时间
  max_retries: 3           # 最大重试次数
  retry_delay: 1s          # 重试延迟

# 缓存配置
cache:
  # 内存缓存配置
  memory:
    max_size: 2000  # 最大缓存条目数
    ttl: 300s       # 默认过期时间(5分钟)
    cleanup_interval: 60s  # 清理间隔
  
  # 不同数据类型的缓存TTL
  ttl:
    snapshot: 5s      # 实时行情快照
    kline_1m: 60s     # 1分钟K线
    kline_5m: 300s    # 5分钟K线
    kline_15m: 900s   # 15分钟K线
    kline_30m: 1800s  # 30分钟K线
    kline_1h: 3600s   # 1小时K线
    kline_2h: 7200s   # 2小时K线
    kline_4h: 14400s  # 4小时K线
    kline_1d: 86400s  # 日K线
    kline_1w: 604800s # 周K线
    kline_1M: 2592000s # 月K线
    kline_1y: 31536000s # 年K线
    depth: 2s         # 盘口深度
    trades: 10s       # 成交明细

# K线优化配置
kline:
  recent:
    enabled: true         # 启用最近K线服务
    supported_types: ["1", "5", "15", "30", "60", "1001"] # 支持的K线类型
    update_frequency: 30s # 更新频率
    max_periods: 1000    # 最大周期数
  history:
    cache_expiration: 86400s # 历史数据缓存过期时间

# WebSocket 配置
websocket:
  enabled: true           # 启用WebSocket服务
  max_connections: 1000   # 最大连接数
  ping_interval: 30s      # 心跳间隔
  pong_timeout: 10s       # 心跳超时

# 日志配置
logging:
  level: "info"           # 日志级别: debug, info, warn, error
  format: "json"          # 日志格式: text, json
  output: "file"          # 输出方式: stdout, file, both
  file:
    path: "logs/qos-market-api.log"
    max_size: 100         # 单个日志文件最大大小(MB)
    max_backups: 10       # 保留的日志文件数量
    max_age: 30           # 日志文件保留天数
    compress: true        # 是否压缩旧日志文件

# 监控配置
monitoring:
  enabled: true           # 启用监控
  metrics_port: 9090      # 监控指标端口
  health_check_interval: 30s # 健康检查间隔

# K线修复配置
correction:
  enabled: true           # 启用修复功能
  database_path: "data/corrections.db" # 数据库文件路径
  refresh_interval: 60s   # 缓存刷新间隔

# 恢复和容错配置
recovery:
  restart_on_panic: true  # 发生panic时自动重启
  health_check_interval: 30s # 健康检查间隔
  max_restart_attempts: 5 # 最大重启尝试次数

# 支持的交易品种配置
symbols:
  us_stocks: ["AAPL", "TSLA", "MSFT", "GOOGL"] # 美股
  hk_stocks: ["700", "9988", "1810"]           # 港股
  crypto: ["BTCUSDT", "ETHUSDT"]               # 数字货币
```

更详细的配置直接看config.yaml。
