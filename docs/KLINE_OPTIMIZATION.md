# QOS Market API K线数据获取优化功能

## 功能概述

成功实现了K线数据获取的功能优化，区分最近K线和历史K线进行不同的处理策略，提高了系统性能和用户体验。

## 核心特性

### 1. 智能数据源路由

系统会根据请求参数自动选择最优的数据源：

- **最近K线**：小数量请求（≤100根）且无指定结束时间
- **历史K线**：大数量请求（>100根）或指定了结束时间的请求
- **长周期K线**：周线、月线、年线优先使用历史接口

### 2. 最近K线服务

#### 特性
- 后台定期向QOS批量查询最新K线数据
- 在缓存中仅保留最新数据（可配置保留周期数）
- 用户请求时从缓存中快速获取
- 更新频率可配置（默认30秒）

#### 配置
```yaml
kline:
  recent:
    enabled: true
    update_interval: 30s  # 后台更新频率
    max_periods: 100      # 保留的最大周期数
```

#### 实现组件
- `RecentKlineService`：后台更新服务
- 支持的K线类型：1分钟、5分钟、15分钟、30分钟、1小时、日线
- 自动应用K线修复数据

### 3. 历史K线服务

#### 特性
- 用户请求时向QOS查询历史数据
- 支持长期缓存（默认24小时）
- 使用QOS的`/history`接口
- 缓存有效期可配置

#### 配置
```yaml
kline:
  history:
    enabled: true
    cache_ttl: 24h        # 缓存有效期
```

#### 实现组件
- `GetHistoryKline`：QOS客户端历史数据方法
- 智能缓存键生成（包含时间范围信息）
- 支持指定结束时间的查询

### 4. 新增API接口

#### 智能路由接口（现有）
```
GET /api/v1/kline?codes=US:AAPL&kline_type=1001&count=10
```
- 自动选择最近K线或历史K线数据源

#### 专门的历史K线接口（新增）
```
GET /api/v1/history?codes=US:AAPL&kline_type=1001&count=100&end_time=1640995200
```
- 强制使用历史K线数据源
- 支持指定结束时间参数

### 5. 增强的延迟跟踪

#### 新增延迟类型
- `recent_kline_latency`：最近K线请求延迟
- `history_kline_latency`：历史K线请求延迟
- `cache_latency`：缓存访问延迟

#### 监控指标
通过`/metrics`端点可查看：
- 各类型请求的次数统计
- 平均延迟和最大延迟
- 1分钟、1小时、24小时时间窗口统计

### 6. 缓存策略优化

#### 最近K线缓存
- 使用短TTL，定期刷新
- 缓存键格式：`recent_kline:{code}:{kline_type}`
- 仅保留最新的N个周期数据

#### 历史K线缓存
- 使用长TTL（24小时）
- 缓存键格式：`history_kline:{code}:{kline_type}:{count}[:{end_time}]`
- 按时间范围和参数缓存

## 技术实现

### 核心组件

1. **RecentKlineService** (`internal/service/recent_kline_service.go`)
   - 后台定期更新服务
   - 支持启动/停止控制
   - 提供统计信息接口

2. **QOSClient扩展** (`internal/client/qos_client.go`)
   - 新增`GetHistoryKline`方法
   - 支持QOS `/history`接口调用

3. **HTTPHandler优化** (`internal/handler/http_handler.go`)
   - 智能路由逻辑`shouldUseHistoryAPI`
   - 分离的处理方法`getRecentKline`和`getHistoryKline`
   - 新增`GetHistoryKlineAPI`接口

4. **CacheManager扩展** (`internal/cache/cache.go`)
   - 新增`SetRecentKline`和`SetHistoryKline`方法
   - 支持不同的缓存策略

5. **延迟跟踪增强** (`internal/stats/collector.go`)
   - 扩展`LatencyStatsCollection`结构
   - 新增延迟收集器和记录方法

### 配置结构

```go
type KlineConfig struct {
    Recent  RecentKlineConfig  `yaml:"recent"`
    History HistoryKlineConfig `yaml:"history"`
}

type RecentKlineConfig struct {
    Enabled        bool          `yaml:"enabled"`
    UpdateInterval time.Duration `yaml:"update_interval"`
    MaxPeriods     int           `yaml:"max_periods"`
}

type HistoryKlineConfig struct {
    Enabled  bool          `yaml:"enabled"`
    CacheTTL time.Duration `yaml:"cache_ttl"`
}
```

## 使用示例

### 1. 获取最近K线数据（快速响应）
```bash
curl "http://localhost:8080/api/v1/kline?codes=US:AAPL&kline_type=1001&count=10"
```

### 2. 获取大量历史数据
```bash
curl "http://localhost:8080/api/v1/kline?codes=US:AAPL&kline_type=1001&count=200"
```

### 3. 获取指定时间的历史数据
```bash
curl "http://localhost:8080/api/v1/kline?codes=US:AAPL&kline_type=1001&count=50&end_time=1640995200"
```

### 4. 使用专门的历史接口
```bash
curl "http://localhost:8080/api/v1/history?codes=US:AAPL&kline_type=1001&count=100"
```

### 5. 查看性能指标
```bash
curl "http://localhost:8080/metrics"
```

## 性能优势

1. **响应速度提升**：最近K线数据从缓存快速获取
2. **资源优化**：减少对QOS API的频繁调用
3. **智能路由**：根据请求特征选择最优数据源
4. **缓存效率**：不同数据类型使用不同的缓存策略
5. **监控完善**：详细的性能指标和延迟跟踪

## 运行演示

```bash
# 构建服务器
go build -o qos-server ./cmd/server

# 启动服务器
./qos-server

# 运行演示程序
go run examples/kline_optimization_demo.go
```

## 测试验证

```bash
# 运行优化功能测试
go test ./test -v -run TestKlineOptimization

# 运行所有测试
go test ./... -v
```

## 注意事项

1. 确保QOS API配置正确
2. 最近K线服务需要足够的内存来缓存数据
3. 历史K线缓存会占用较多存储空间
4. 建议根据实际使用情况调整配置参数
5. 监控缓存命中率和延迟指标以优化性能
