# K线数据修复功能使用指南

## 概述

QOS Market API 的K线数据修复功能允许您对错误的K线数据进行修正，确保返回给用户的数据准确性。该功能支持对开盘价、收盘价、最高价、最低价和成交量进行修复，并提供完整的Web管理界面和RESTful API。

## 功能特性

- ✅ **实时修复**: 修复数据实时应用到API返回结果
- ✅ **智能缓存**: 内存缓存确保毫秒级修复应用
- ✅ **跨周期修复**: 支持修复数据在不同K线周期间的传播
- ✅ **Web管理界面**: 提供直观易用的Web界面
- ✅ **RESTful API**: 完整的CRUD操作接口
- ✅ **批量管理**: 支持批量启用/禁用修复规则
- ✅ **统计监控**: 详细的修复统计和监控指标
- ✅ **数据持久化**: SQLite数据库可靠存储
- ✅ **请求日志**: 完整的修复操作日志记录

## 快速开始

### 1. 启用修复功能

在配置文件 `configs/config.yaml` 中启用修复功能：

```yaml
correction:
  enabled: true
  database_path: "data/corrections.db"
  refresh_interval: 60s
```

### 2. 启动服务

```bash
./bin/qos-market-api -config=configs/config.yaml
```

### 3. 访问Web管理界面

打开浏览器访问：`http://localhost:8080/corrections`

## 使用场景

### 场景1：修复错误的收盘价

假设发现 `US:AAPL` 在某个时间点的收盘价数据错误：

1. **通过Web界面**：
   - 访问 `http://localhost:8080/corrections`
   - 填写表单：
     - 品种代码: `US:AAPL`
     - K线类型: `日线`
     - 时间戳: `1641234567`
     - 修复字段: `收盘价(cl)`
     - 修复值: `150.25`
     - 修复原因: `修正错误的收盘价数据`
   - 点击"添加修复数据"

2. **通过API**：
```bash
curl -X POST http://localhost:8080/api/v1/corrections \
  -H "Content-Type: application/json" \
  -d '{
    "code": "US:AAPL",
    "kline_type": 1001,
    "timestamp": 1641234567,
    "field": "cl",
    "corr_value": "150.25",
    "reason": "修正错误的收盘价数据"
  }'
```

### 场景2：批量修复多个字段

如果需要修复同一K线的多个字段，需要分别添加：

```bash
# 修复开盘价
curl -X POST http://localhost:8080/api/v1/corrections \
  -H "Content-Type: application/json" \
  -d '{
    "code": "US:AAPL",
    "kline_type": 1001,
    "timestamp": 1641234567,
    "field": "o",
    "corr_value": "149.80",
    "reason": "修正开盘价"
  }'

# 修复收盘价
curl -X POST http://localhost:8080/api/v1/corrections \
  -H "Content-Type: application/json" \
  -d '{
    "code": "US:AAPL",
    "kline_type": 1001,
    "timestamp": 1641234567,
    "field": "cl",
    "corr_value": "150.25",
    "reason": "修正收盘价"
  }'
```

### 场景3：临时禁用修复数据

如果需要临时禁用某个修复数据而不删除：

```bash
curl -X POST http://localhost:8080/api/v1/corrections/1/toggle \
  -H "Content-Type: application/json" \
  -d '{"is_active": false}'
```

## API 接口详细说明

### 1. 添加修复数据

**请求**
```
POST /api/v1/corrections
Content-Type: application/json
```

**请求体**
```json
{
  "code": "US:AAPL",
  "kline_type": 1001,
  "timestamp": 1641234567,
  "field": "cl",
  "corr_value": "150.25",
  "reason": "修复错误的收盘价"
}
```

**响应**
```json
{
  "msg": "Correction added successfully",
  "data": {
    "id": 1,
    "code": "US:AAPL",
    "timestamp": 1641234567,
    "field": "cl",
    "orig_value": "",
    "corr_value": "150.25",
    "reason": "修复错误的收盘价",
    "created_at": 1641234567,
    "updated_at": 1641234567,
    "is_active": true
  }
}
```

### 2. 获取修复数据列表

**请求**
```
GET /api/v1/corrections?page=1&limit=10&code=US:AAPL
```

**参数说明**
- `page`: 页码，默认1
- `limit`: 每页数量，默认10
- `code`: 过滤品种代码（可选）

**响应**
```json
{
  "msg": "OK",
  "data": {
    "corrections": [...],
    "total": 25,
    "page": 1,
    "limit": 10
  }
}
```

### 3. 获取单个修复数据

**请求**
```
GET /api/v1/corrections/{id}
```

### 4. 更新修复数据

**请求**
```
PUT /api/v1/corrections/{id}
Content-Type: application/json
```

### 5. 删除修复数据

**请求**
```
DELETE /api/v1/corrections/{id}
```

### 6. 切换修复状态

**请求**
```
POST /api/v1/corrections/{id}/toggle
Content-Type: application/json
```

**请求体**
```json
{
  "is_active": false
}
```

### 7. 获取修复统计

**请求**
```
GET /api/v1/corrections/stats
```

**响应**
```json
{
  "msg": "OK",
  "data": {
    "total_corrections": 10,
    "active_corrections": 8,
    "by_code": {
      "US:AAPL": 5,
      "HK:700": 3
    },
    "by_field": {
      "cl": 4,
      "h": 3,
      "o": 1
    },
    "by_type": {
      "1001": 6,
      "60": 2
    }
  }
}
```

## 字段说明

### K线类型对照表

| 类型值 | 说明 |
|--------|------|
| 1 | 1分钟 |
| 5 | 5分钟 |
| 15 | 15分钟 |
| 30 | 30分钟 |
| 60 | 1小时 |
| 120 | 2小时 |
| 240 | 4小时 |
| 1001 | 日线 |
| 1007 | 周线 |
| 1030 | 月线 |
| 2001 | 年线 |

### 修复字段对照表

| 字段 | 说明 |
|------|------|
| o | 开盘价 |
| cl | 收盘价 |
| h | 最高价 |
| l | 最低价 |
| v | 成交量 |

## Web管理界面

### 访问界面

打开浏览器访问：`http://localhost:8080/corrections`

### 界面功能

**统计信息面板**
- 总修复数：显示系统中所有修复数据的数量
- 活跃修复：显示当前启用的修复数据数量
- 涉及品种：显示有修复数据的交易品种数量

**修复数据管理**
- 添加修复数据：通过表单快速添加新的修复规则
- 修复列表：查看所有修复数据，支持分页浏览
- 状态切换：一键启用/禁用修复规则
- 编辑删除：修改或删除现有修复数据

**智能功能**
- 品种代码下拉选择（基于配置文件）
- 时间选择器（自动转换为时间戳）
- 实时状态更新
- 操作结果提示

### 界面截图说明

界面采用响应式设计，支持桌面和移动设备访问，提供直观的数据管理体验。

## 监控和运维

### 系统监控

**监控指标获取**
```bash
curl http://localhost:9090/metrics
```

**修复功能指标**
```json
{
  "corrections": {
    "enabled": true,
    "total_corrections": 10,
    "active_corrections": 8,
    "cache_size": 8,
    "last_refresh": "2024-01-01T12:00:00Z",
    "by_code": {
      "US:AAPL": 5,
      "HK:700": 3
    },
    "by_field": {
      "cl": 4,
      "h": 3,
      "o": 1
    }
  }
}
```

### 日志监控

**修复操作日志**
```
INFO[2024-01-15 10:30:45] User request: corrections - SUCCESS endpoint=corrections codes=[US:AAPL] result_count=1 status=SUCCESS latency_ms=12
INFO[2024-01-15 10:31:20] Added correction: id=1 code=US:AAPL field=cl value=150.25
INFO[2024-01-15 10:31:21] Cache refreshed: loaded 8 corrections
```

**修复应用日志**
```
DEBUG[2024-01-15 10:32:15] Applied correction: code=US:AAPL timestamp=1641234567 field=cl orig=149.80 corr=150.25
```

### 性能监控

**延迟跟踪**
- 修复查询延迟：缓存查询时间
- 修复应用延迟：数据修复处理时间
- 总延迟影响：修复对API响应时间的影响

**缓存监控**
- 缓存命中率：修复数据缓存效率
- 缓存刷新频率：数据同步状态
- 内存使用：修复缓存占用情况

## 最佳实践

### 1. 数据验证

在添加修复数据前，请确保：
- **时间戳准确性**：确保时间戳精确对应需要修复的K线周期
- **数值合理性**：修复值应符合市场数据的合理范围和历史趋势
- **原因记录**：详细记录修复原因，便于后续审计和维护
- **字段正确性**：确认修复字段（o/cl/h/l/v）选择正确

### 2. 测试验证

添加修复数据后，建议按以下步骤验证：

**API验证**
```bash
# 验证修复是否生效
curl "http://localhost:8080/api/v1/kline?codes=US:AAPL&kline_type=1001&count=1"

# 检查修复统计
curl "http://localhost:8080/api/v1/corrections/stats"
```

**数据一致性检查**
- 检查修复后的K线数据是否符合预期
- 验证修复不会影响其他时间段的数据
- 确认跨周期数据的一致性

### 3. 运维管理

**定期维护**
- 定期清理过期或无效的修复数据
- 监控修复数据的应用效果
- 备份修复数据库文件

**性能优化**
- 监控修复功能对API响应时间的影响
- 适当调整缓存刷新间隔
- 定期检查数据库性能

### 4. 安全考虑

**访问控制**
- 限制修复管理界面的访问权限
- 记录所有修复操作的操作者信息
- 定期审计修复数据的变更历史

**数据备份**
- 定期备份修复数据库
- 在重要修复操作前创建数据快照
- 建立数据恢复流程

## 故障排除

### 常见问题

**1. 修复数据不生效**

症状：添加修复数据后，API返回的K线数据未发生变化

解决方案：
```bash
# 检查修复数据状态
curl "http://localhost:8080/api/v1/corrections/stats"

# 检查具体修复数据
curl "http://localhost:8080/api/v1/corrections?code=US:AAPL"

# 强制刷新缓存（重启服务）
sudo systemctl restart qos-market-api
```

可能原因：
- 修复数据处于禁用状态
- 时间戳或K线类型不匹配
- 缓存未及时刷新
- 修复值格式错误

**2. Web界面无法访问**

症状：访问 `http://localhost:8080/corrections` 返回404或无响应

解决方案：
```bash
# 检查服务状态
curl http://localhost:8080/health

# 检查修复功能是否启用
grep -A5 "correction:" configs/config.yaml

# 检查端口占用
netstat -tlnp | grep :8080
```

**3. 数据库操作失败**

症状：添加、更新或删除修复数据时报错

解决方案：
```bash
# 检查数据库文件权限
ls -la data/corrections.db

# 检查数据库完整性
sqlite3 data/corrections.db "PRAGMA integrity_check;"

# 备份并重建数据库
cp data/corrections.db data/corrections.db.backup
rm data/corrections.db
# 重启服务自动重建数据库
```

**4. 性能问题**

症状：API响应时间明显增加

解决方案：
```bash
# 检查修复数据数量
curl "http://localhost:8080/api/v1/corrections/stats"

# 监控延迟指标
curl "http://localhost:9090/metrics" | grep correction

# 优化缓存刷新间隔
# 在config.yaml中调整refresh_interval
```

### 日志分析

**查看修复相关日志**
```bash
# 实时查看修复日志
tail -f logs/qos-market-api.log | grep -i correction

# 查看错误日志
grep -i "error.*correction" logs/qos-market-api.log

# 查看性能日志
grep -i "correction.*latency" logs/qos-market-api.log
```

**日志级别说明**
- `INFO`: 正常操作记录
- `WARN`: 警告信息，需要关注
- `ERROR`: 错误信息，需要立即处理
- `DEBUG`: 详细调试信息

## 技术架构

### 系统组件

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web界面       │    │   RESTful API   │    │   K线API        │
│  corrections    │    │  /api/v1/       │    │  /api/v1/kline  │
│     .html       │    │  corrections    │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Correction     │
                    │   Handler       │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐    ┌─────────────────┐
                    │  Correction     │    │   Database      │
                    │    Cache        │    │   (SQLite)      │
                    └─────────────────┘    └─────────────────┘
                                 │                       │
                    ┌─────────────────┐    ┌─────────────────┐
                    │   Corrector     │    │   Monitoring    │
                    │   (应用修复)     │    │   (指标统计)     │
                    └─────────────────┘    └─────────────────┘
```

### 数据流程

1. **修复数据管理**：通过Web界面或API添加/管理修复数据
2. **数据持久化**：修复数据存储到SQLite数据库
3. **缓存同步**：定期从数据库加载数据到内存缓存
4. **实时应用**：K线API调用时自动应用修复数据
5. **监控统计**：记录修复操作和性能指标

### 性能优化

**缓存策略**
- 内存缓存：毫秒级修复数据查询
- 定期刷新：可配置的缓存更新间隔
- 智能加载：只加载活跃的修复数据

**查询优化**
- 索引优化：数据库表建立适当索引
- 批量查询：减少数据库访问次数
- 时间范围过滤：只查询相关时间段的修复数据

## 相关文档

- [API接口文档](API.md) - 完整的API接口说明
- [跨周期修复文档](CROSS_PERIOD_CORRECTION.md) - 跨周期修复功能详解
- [部署文档](DEPLOYMENT.md) - 系统部署和配置指南
- [监控文档](STATS.md) - 监控指标和统计功能
