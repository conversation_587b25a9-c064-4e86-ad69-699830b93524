# K线数据初始化批处理修复

## 问题描述

在K线数据初始化时遇到以下错误：
```
failed to unmarshal response: unexpected end of JSON input
```

### 根本原因

1. **请求数据量过大**：一次性请求所有品种（约50个）的K线数据（每个1000条），导致QOS API响应数据过大
2. **JSON响应截断**：服务器响应可能因为数据量大而被截断，导致JSON解析失败
3. **网络超时**：大量数据传输可能导致网络超时

## 解决方案

### 1. 分批处理机制

将品种按批次处理，避免单次请求过大：

```go
// 配置项
type RecentKlineConfig struct {
    InitialLoadBatchSize int `yaml:"initial_load_batch_size"` // 批处理大小，默认5
    // ... 其他配置
}
```

### 2. 并行批处理

不同批次并行处理，提高效率：

```go
func (s *RecentKlineService) loadInitialKlineType(codes []string, klineType types.KlineType, count int) error {
    batchSize := 5 // 可配置
    
    // 分批并行处理
    for i := 0; i < totalCodes; i += batchSize {
        batchCodes := codes[i:end]
        go func(batch []string) {
            s.loadInitialKlineTypeBatch(batch, klineType, count)
        }(batchCodes)
    }
}
```

### 3. 错误检测和重试

增强JSON解析错误检测：

```go
func (c *QOSClient) GetKline(requests []types.KlineRequest) ([]types.KlineData, error) {
    // 检查JSON有效性
    if !json.Valid(data) {
        return nil, fmt.Errorf("invalid JSON response from /kline endpoint")
    }
    
    // 检测截断错误
    if strings.Contains(err.Error(), "unexpected end of JSON input") {
        return nil, fmt.Errorf("response data truncated, try reducing batch size: %w", err)
    }
}
```

### 4. 递归批次分割

当批次仍然过大时，递归分割：

```go
func (s *RecentKlineService) loadInitialKlineTypeBatchRecursive(codes []string, klineType types.KlineType, count int) (int, int, error) {
    if len(codes) <= 1 {
        return 0, 1, fmt.Errorf("single symbol request failed")
    }
    
    // 分成两半并行处理
    mid := len(codes) / 2
    firstHalf := codes[:mid]
    secondHalf := codes[mid:]
    
    // 并行处理两个子批次
    // ...
}
```

### 5. 重试机制

实现指数退避重试：

```go
maxRetries := 3
for retry := 0; retry < maxRetries; retry++ {
    klineDataList, err = s.qosClient.GetKline(requests)
    if err == nil {
        break
    }
    
    if strings.Contains(err.Error(), "response data truncated") {
        // 尝试递归分割
        return s.loadInitialKlineTypeBatchRecursive(codes, klineType, count)
    }
    
    time.Sleep(time.Duration(retry+1) * time.Second) // 递增延迟
}
```

## 配置参数

### 新增配置项

```yaml
kline:
  recent:
    initial_load_batch_size: 5    # 初始化时批处理大小（每批处理的品种数）
```

### 推荐配置值

- **生产环境**：`initial_load_batch_size: 5`
- **测试环境**：`initial_load_batch_size: 3`
- **开发环境**：`initial_load_batch_size: 2`

## 实现效果

### 修复前
- 单次请求：50个品种 × 1000条K线 = 50,000条数据
- 错误频率：高（JSON解析失败）
- 初始化时间：失败或超时

### 修复后
- 批次请求：5个品种 × 1000条K线 = 5,000条数据/批次
- 总批次数：10批次（50个品种 ÷ 5）
- 并行处理：不同K线类型并行，不同批次并行
- 错误处理：自动重试和递归分割
- 初始化时间：稳定且可预测

## 监控和日志

### 初始化概览日志
```
=== Starting initial kline data load ===
Initial load configuration:
  - Total symbols: 10 ([US:AAPL US:GOOGL US:MSFT ...])
  - K-line types: 3 ([1m 5m 1h])
  - Periods per symbol: 10
  - Batch size: 2
  - Expected total data points: 300
```

### 并行处理日志
```
Starting parallel load for 3 K-line types...
Loading initial 1m kline data for 10 symbols in batches of 2
Loading initial 5m kline data for 10 symbols in batches of 2
Loading initial 1h kline data for 10 symbols in batches of 2
```

### 详细批处理日志
```
Requesting initial 1m kline data for 2 symbols: [US:AAPL US:GOOGL]
QOS API call successful for 1m kline batch (duration: 51.109ms)
Loaded US:AAPL 1m klines: 10 periods from 2025-07-14 22:56:22 to 2025-07-14 23:05:22
Loaded US:GOOGL 1m klines: 10 periods from 2025-07-14 22:56:22 to 2025-07-14 23:05:22
Batch completed for 1m kline: 2 success, 0 errors, 20 data points (request: 51.421334ms, process: 162.917µs, total: 51.584251ms)
```

### 完成总结日志
```
✓ 1m klines loaded: 10 success, 0 errors (duration: 51.929125ms)
✓ 5m klines loaded: 10 success, 0 errors (duration: 51.856541ms)
✓ 1h klines loaded: 10 success, 0 errors (duration: 51.920958ms)
Parallel load completed: 30 total success, 0 total errors

=== Initial kline data load completed ===
  - Success: 30
  - Errors: 0
  - Total duration: 52.020083ms
  - Average per symbol: 5.202008ms
  - Success rate: 100.0%
```

### 错误处理日志
```
Batch size too large for 1m kline, reducing batch size and retrying (attempt 1/3)
Splitting batch for 1m kline: 10 symbols -> 5 + 5 symbols
Recursive batch completed for 1m kline: 10 success, 0 errors
```

## 性能优化

1. **并行度控制**：批次间并行，K线类型间并行
2. **内存使用**：分批处理减少内存峰值
3. **网络效率**：小批次减少网络超时风险
4. **错误恢复**：快速识别和处理错误

## 总结

通过实施分批处理机制，成功解决了K线数据初始化时的JSON解析错误问题，提高了系统的稳定性和可靠性。该方案具有以下优势：

1. **稳定性**：避免大数据量导致的响应截断
2. **可扩展性**：支持更多品种而不影响性能
3. **容错性**：自动重试和错误恢复机制
4. **可配置性**：批次大小可根据环境调整
5. **可监控性**：详细的日志和错误报告
