# QOS Market API 延迟追踪功能

## 功能概述

QOS Market API 现在支持全面的延迟追踪功能，可以分阶段记录和统计延迟，帮助监控和优化系统性能。

## 功能特性

### 1. 分阶段延迟追踪

系统会追踪以下三个关键阶段的延迟：

- **QOS API调用延迟**: 从调用QOS.HK API到获得响应的时间
- **K线修复延迟**: 应用K线数据修复的处理时间
- **总延迟**: 从接收用户请求到返回最终结果的总时间

### 2. 时间窗口统计

支持多个时间窗口的延迟统计：

- **总计**: 自服务启动以来的所有延迟数据
- **最近24小时**: 过去24小时内的延迟统计
- **最近1小时**: 过去1小时内的延迟统计
- **最近1分钟**: 过去1分钟内的延迟统计

### 3. 统计指标

每个时间窗口提供以下统计指标：

- **请求次数**: 该时间窗口内的请求总数
- **平均延迟**: 平均延迟时间（毫秒）
- **最大延迟**: 最大延迟时间（毫秒）

## 技术实现

### 核心组件

#### 1. LatencyCollector (internal/stats/collector.go)

延迟统计收集器，负责：
- 记录延迟数据
- 计算统计指标
- 自动清理超过24小时的历史数据

```go
type LatencyCollector struct {
    mutex   sync.RWMutex
    records []LatencyRecord
}

type LatencyRecord struct {
    Timestamp time.Time
    Latency   time.Duration
}
```

#### 2. StatsCollector 扩展

扩展了现有的统计收集器，添加了三个延迟收集器：

```go
type StatsCollector struct {
    // 现有字段...
    qosAPILatency     *LatencyCollector
    correctionLatency *LatencyCollector
    totalLatency      *LatencyCollector
}
```

#### 3. HTTPHandler 集成

HTTP处理器集成了延迟追踪功能：

```go
type HTTPHandler struct {
    // 现有字段...
    latencyRecorder LatencyRecorder
}
```

### 数据流程

1. **请求开始**: 记录总延迟开始时间
2. **QOS API调用**: 记录API调用开始和结束时间
3. **K线修复**: 记录修复处理开始和结束时间
4. **响应返回**: 计算总延迟并记录所有统计数据
5. **日志输出**: 输出详细的延迟信息到日志

## 使用方法

### 1. 查看延迟统计

通过 `/metrics` 端点获取延迟统计信息：

```bash
curl http://localhost:8080/metrics
```

响应示例：
```json
{
  "latency": {
    "qos_api_latency": {
      "count": {
        "total": 150,
        "last_24_hours": 120,
        "last_hour": 25,
        "last_minute": 3
      },
      "average": {
        "total": 85,
        "last_24_hours": 82,
        "last_hour": 78,
        "last_minute": 75
      },
      "max": {
        "total": 250,
        "last_24_hours": 220,
        "last_hour": 180,
        "last_minute": 120
      }
    },
    "correction_latency": {
      "count": {...},
      "average": {...},
      "max": {...}
    },
    "total_latency": {
      "count": {...},
      "average": {...},
      "max": {...}
    }
  }
}
```

### 2. 查看延迟日志

系统会自动记录每个K线请求的详细延迟信息：

```
time="2025-07-13 15:00:34" level=info msg="K-line request completed - codes: [US:AAPL], type: 1d, count: 1, qos_api_latency: 101.069208ms, correction_latency: 51.084875ms, total_latency: 152.22025ms, results: 1"
```

日志包含以下信息：
- 请求的交易品种代码
- K线类型和数量
- QOS API调用延迟
- K线修复延迟
- 总延迟
- 返回结果数量

## 性能优化建议

### 1. 监控指标

重点关注以下指标：

- **QOS API延迟**: 如果持续较高，可能需要优化网络连接或考虑缓存策略
- **修复延迟**: 如果较高，可能需要优化修复算法或减少修复数据量
- **总延迟**: 整体性能指标，应保持在合理范围内

### 2. 告警阈值建议

- QOS API平均延迟 > 200ms
- 修复平均延迟 > 50ms
- 总平均延迟 > 300ms
- 最大延迟 > 1000ms

### 3. 优化策略

1. **缓存优化**: 提高缓存命中率可以显著减少QOS API调用延迟
2. **修复优化**: 优化修复算法，减少不必要的修复操作
3. **并发控制**: 合理控制并发请求数量，避免系统过载

## 测试验证

### 单元测试

运行延迟追踪相关的单元测试：

```bash
# 测试延迟收集器
go test ./internal/stats -v -run TestLatency

# 测试HTTP处理器延迟追踪
go test ./internal/handler -v -run TestHTTPHandlerLatency
```

### 集成测试

1. 启动服务
2. 发送K线数据请求
3. 检查 `/metrics` 端点的延迟统计
4. 查看日志中的延迟信息

## 配置说明

延迟追踪功能无需额外配置，会自动启用。相关配置项：

```yaml
# 监控配置
monitoring:
  enabled: true
  metrics_port: 9090
  health_check_interval: 30s

# 日志配置
logging:
  level: "info"  # 确保info级别以查看延迟日志
```

## 故障排除

### 常见问题

1. **延迟统计为空**
   - 检查监控服务是否正常启动
   - 确认有K线请求发送到服务

2. **延迟异常高**
   - 检查网络连接状况
   - 查看QOS.HK API响应时间
   - 检查系统资源使用情况

3. **统计数据不准确**
   - 确认系统时间同步
   - 检查是否有时钟跳跃

### 调试方法

1. 启用调试日志：
```yaml
logging:
  level: "debug"
```

2. 查看详细的缓存命中/未命中日志
3. 监控系统资源使用情况
4. 使用性能分析工具进行深入分析

## 版本历史

- **v1.0.0**: 初始实现，支持基本的延迟追踪和统计功能
- 支持QOS API调用、K线修复、总延迟的分阶段追踪
- 支持多时间窗口统计（1分钟、1小时、24小时、总计）
- 集成到现有的监控和日志系统
