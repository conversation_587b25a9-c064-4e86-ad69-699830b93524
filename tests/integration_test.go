package tests

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"qos-market-api/internal/cache"
	"qos-market-api/internal/client"
	"qos-market-api/internal/config"
	"qos-market-api/internal/handler"
	"qos-market-api/internal/logger"
)

func TestHealthCheck(t *testing.T) {
	// 创建测试配置
	cfg := &config.Config{
		QOS: config.QOSConfig{
			APIKey:         "test_key",
			HTTPBaseURL:    "https://qos.hk",
			WebSocketURL:   "wss://api.qos.hk/ws",
			RequestTimeout: 30 * time.Second,
			MaxRetries:     3,
			RetryDelay:     5 * time.Second,
		},
		Cache: config.CacheConfig{
			Memory: config.MemoryCacheConfig{
				MaxSize:         100,
				TTL:             300 * time.Second,
				CleanupInterval: 60 * time.Second,
			},
		},
		Logging: config.LoggingConfig{
			Level:  "info",
			Format: "text",
			Output: "stdout",
		},
	}

	// 初始化日志
	log, err := logger.New(&cfg.Logging)
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	// 创建组件
	cacheManager := cache.NewCacheManager(&cfg.Cache, log)
	qosClient := client.NewQOSClient(&cfg.QOS, log)
	httpHandler := handler.NewHTTPHandler(qosClient, cacheManager, cfg, log)

	// 创建测试服务器
	mux := httpHandler.SetupRoutes()
	server := httptest.NewServer(mux)
	defer server.Close()

	// 测试健康检查
	resp, err := http.Get(server.URL + "/health")
	if err != nil {
		t.Fatalf("Failed to make health check request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		t.Errorf("Expected status 200, got %d", resp.StatusCode)
	}

	var health map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&health); err != nil {
		t.Fatalf("Failed to decode health response: %v", err)
	}

	if health["status"] != "ok" {
		t.Errorf("Expected status 'ok', got %v", health["status"])
	}
}

func TestCacheManager(t *testing.T) {
	cfg := &config.CacheConfig{
		Memory: config.MemoryCacheConfig{
			MaxSize:         10,
			TTL:             1 * time.Second,
			CleanupInterval: 100 * time.Millisecond,
		},
		TTL: config.CacheTTLConfig{
			Snapshot: 1 * time.Second,
		},
	}

	log, _ := logger.New(&config.LoggingConfig{
		Level:  "info",
		Format: "text",
		Output: "stdout",
	})

	cacheManager := cache.NewCacheManager(cfg, nil, log)

	// 测试设置和获取
	key := "test_key"
	value := "test_value"

	err := cacheManager.Set(key, value, "snapshot")
	if err != nil {
		t.Fatalf("Failed to set cache: %v", err)
	}

	cached, found := cacheManager.Get(key)
	if !found {
		t.Error("Expected to find cached value")
	}

	if cached != value {
		t.Errorf("Expected %v, got %v", value, cached)
	}

	// 测试过期
	time.Sleep(2 * time.Second)

	_, found = cacheManager.Get(key)
	if found {
		t.Error("Expected cached value to be expired")
	}

	cacheManager.Stop()
}

func TestConfigValidation(t *testing.T) {
	// 测试有效配置
	validConfig := &config.Config{
		Server: config.ServerConfig{
			Port: 8080,
		},
		QOS: config.QOSConfig{
			APIKey:       "valid_key",
			HTTPBaseURL:  "https://qos.hk",
			WebSocketURL: "wss://api.qos.hk/ws",
		},
	}

	if err := validConfig.Validate(); err != nil {
		t.Errorf("Valid config should not return error: %v", err)
	}

	// 测试无效配置 - 缺少API密钥
	invalidConfig := &config.Config{
		Server: config.ServerConfig{
			Port: 8080,
		},
		QOS: config.QOSConfig{
			APIKey:       "",
			HTTPBaseURL:  "https://qos.hk",
			WebSocketURL: "wss://api.qos.hk/ws",
		},
	}

	if err := invalidConfig.Validate(); err == nil {
		t.Error("Invalid config should return error")
	}

	// 测试无效端口
	invalidPortConfig := &config.Config{
		Server: config.ServerConfig{
			Port: 0,
		},
		QOS: config.QOSConfig{
			APIKey:       "valid_key",
			HTTPBaseURL:  "https://qos.hk",
			WebSocketURL: "wss://api.qos.hk/ws",
		},
	}

	if err := invalidPortConfig.Validate(); err == nil {
		t.Error("Invalid port config should return error")
	}
}

func BenchmarkCacheSet(b *testing.B) {
	cfg := &config.CacheConfig{
		Memory: config.MemoryCacheConfig{
			MaxSize:         1000,
			TTL:             300 * time.Second,
			CleanupInterval: 60 * time.Second,
		},
	}

	log, _ := logger.New(&config.LoggingConfig{
		Level:  "error", // 减少日志输出
		Format: "text",
		Output: "stdout",
	})

	cacheManager := cache.NewCacheManager(cfg, log)
	defer cacheManager.Stop()

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		key := "benchmark_key_" + string(rune(i))
		value := "benchmark_value_" + string(rune(i))
		cacheManager.Set(key, value, "snapshot")
	}
}

func BenchmarkCacheGet(b *testing.B) {
	cfg := &config.CacheConfig{
		Memory: config.MemoryCacheConfig{
			MaxSize:         1000,
			TTL:             300 * time.Second,
			CleanupInterval: 60 * time.Second,
		},
	}

	log, _ := logger.New(&config.LoggingConfig{
		Level:  "error",
		Format: "text",
		Output: "stdout",
	})

	cacheManager := cache.NewCacheManager(cfg, nil, log)
	defer cacheManager.Stop()

	// 预填充缓存
	for i := 0; i < 100; i++ {
		key := "benchmark_key_" + string(rune(i))
		value := "benchmark_value_" + string(rune(i))
		cacheManager.Set(key, value, "snapshot")
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		key := "benchmark_key_" + string(rune(i%100))
		cacheManager.Get(key)
	}
}
