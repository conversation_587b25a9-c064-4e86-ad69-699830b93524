package test

import (
	"testing"
	"time"

	"qos-market-api/internal/cache"
	"qos-market-api/internal/config"
	"qos-market-api/internal/correction"
	"qos-market-api/internal/logger"
	"qos-market-api/internal/service"
)

// TestRecentKlineConfigurableTypes 测试最近K线可配置类型功能
func TestRecentKlineConfigurableTypes(t *testing.T) {
	// 创建日志器
	log, err := logger.New(&config.LoggingConfig{
		Level:  "info",
		Format: "text",
		Output: "stdout",
	})
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	t.Run("TestDefaultKlineTypes", func(t *testing.T) {
		// 测试默认K线类型（配置为空）
		cfg := &config.KlineConfig{
			Recent: config.RecentKlineConfig{
				Enabled:        true,
				UpdateInterval: 30 * time.Second,
				MaxPeriods:     100,
				SupportedTypes: []string{}, // 空配置，应该使用默认值
			},
		}

		service := createRecentKlineService(cfg, log, t)
		stats := service.GetStats()

		supportedTypes, ok := stats["supported_types"].([]string)
		if !ok {
			t.Fatal("Expected supported_types to be []string")
		}

		// 默认应该支持6种类型
		expectedTypes := []string{"1m", "5m", "15m", "30m", "1h", "1d"}
		if len(supportedTypes) != len(expectedTypes) {
			t.Errorf("Expected %d default types, got %d", len(expectedTypes), len(supportedTypes))
		}

		// 检查是否包含预期的类型
		for _, expected := range expectedTypes {
			found := false
			for _, actual := range supportedTypes {
				if actual == expected {
					found = true
					break
				}
			}
			if !found {
				t.Errorf("Expected default type %s not found", expected)
			}
		}
	})

	t.Run("TestCustomKlineTypes", func(t *testing.T) {
		// 测试自定义K线类型
		cfg := &config.KlineConfig{
			Recent: config.RecentKlineConfig{
				Enabled:        true,
				UpdateInterval: 30 * time.Second,
				MaxPeriods:     100,
				SupportedTypes: []string{"1m", "1h", "1d"}, // 只支持3种类型
			},
		}

		service := createRecentKlineService(cfg, log, t)
		stats := service.GetStats()

		supportedTypes, ok := stats["supported_types"].([]string)
		if !ok {
			t.Fatal("Expected supported_types to be []string")
		}

		// 应该只有3种类型
		if len(supportedTypes) != 3 {
			t.Errorf("Expected 3 custom types, got %d", len(supportedTypes))
		}

		// 检查配置的类型
		expectedTypes := []string{"1m", "1h", "1d"}
		for _, expected := range expectedTypes {
			found := false
			for _, actual := range supportedTypes {
				if actual == expected {
					found = true
					break
				}
			}
			if !found {
				t.Errorf("Expected custom type %s not found", expected)
			}
		}

		// 检查配置信息
		configuredTypes, ok := stats["configured_types"].([]string)
		if !ok {
			t.Fatal("Expected configured_types to be []string")
		}

		if len(configuredTypes) != 3 {
			t.Errorf("Expected 3 configured types, got %d", len(configuredTypes))
		}
	})

	t.Run("TestAllSupportedKlineTypes", func(t *testing.T) {
		// 测试所有支持的K线类型
		cfg := &config.KlineConfig{
			Recent: config.RecentKlineConfig{
				Enabled:        true,
				UpdateInterval: 30 * time.Second,
				MaxPeriods:     100,
				SupportedTypes: []string{"1m", "5m", "15m", "30m", "1h", "2h", "4h", "1d", "1w", "1M", "1y"},
			},
		}

		service := createRecentKlineService(cfg, log, t)
		stats := service.GetStats()

		supportedTypes, ok := stats["supported_types"].([]string)
		if !ok {
			t.Fatal("Expected supported_types to be []string")
		}

		// 应该支持11种类型
		if len(supportedTypes) != 11 {
			t.Errorf("Expected 11 types, got %d", len(supportedTypes))
		}
	})

	t.Run("TestInvalidKlineTypes", func(t *testing.T) {
		// 测试包含无效K线类型的配置
		cfg := &config.KlineConfig{
			Recent: config.RecentKlineConfig{
				Enabled:        true,
				UpdateInterval: 30 * time.Second,
				MaxPeriods:     100,
				SupportedTypes: []string{"1m", "invalid", "1h", "unknown"}, // 包含无效类型
			},
		}

		service := createRecentKlineService(cfg, log, t)
		stats := service.GetStats()

		supportedTypes, ok := stats["supported_types"].([]string)
		if !ok {
			t.Fatal("Expected supported_types to be []string")
		}

		// 应该只有2种有效类型
		if len(supportedTypes) != 2 {
			t.Errorf("Expected 2 valid types, got %d", len(supportedTypes))
		}

		// 检查有效的类型
		expectedTypes := []string{"1m", "1h"}
		for _, expected := range expectedTypes {
			found := false
			for _, actual := range supportedTypes {
				if actual == expected {
					found = true
					break
				}
			}
			if !found {
				t.Errorf("Expected valid type %s not found", expected)
			}
		}
	})

	t.Run("TestAllInvalidKlineTypes", func(t *testing.T) {
		// 测试全部无效K线类型的配置
		cfg := &config.KlineConfig{
			Recent: config.RecentKlineConfig{
				Enabled:        true,
				UpdateInterval: 30 * time.Second,
				MaxPeriods:     100,
				SupportedTypes: []string{"invalid1", "invalid2", "unknown"}, // 全部无效
			},
		}

		service := createRecentKlineService(cfg, log, t)
		stats := service.GetStats()

		supportedTypes, ok := stats["supported_types"].([]string)
		if !ok {
			t.Fatal("Expected supported_types to be []string")
		}

		// 应该回退到默认的6种类型
		if len(supportedTypes) != 6 {
			t.Errorf("Expected 6 default types when all invalid, got %d", len(supportedTypes))
		}
	})
}

// createRecentKlineService 创建测试用的最近K线服务
func createRecentKlineService(klineConfig *config.KlineConfig, log logger.Logger, t *testing.T) *service.RecentKlineService {
	// 创建缓存配置
	cacheConfig := &config.CacheConfig{
		Memory: config.MemoryCacheConfig{
			MaxSize:         1000,
			TTL:             time.Hour,
			CleanupInterval: 10 * time.Minute,
		},
	}

	// 创建缓存管理器
	cacheManager := cache.NewCacheManager(cacheConfig, klineConfig, log)
	defer cacheManager.Stop()

	// 创建修复器
	database, err := correction.NewDatabase(":memory:", log)
	if err != nil {
		t.Fatalf("Failed to create database: %v", err)
	}
	defer database.Close()

	cache := correction.NewCorrectionCache(database, time.Minute, log)
	defer cache.Stop()

	corrector := correction.NewCorrector(cache, log)

	// 创建模拟QOS客户端
	mockClient := &MockQOSClient{}

	// 创建符号配置
	symbols := &config.SymbolsConfig{
		USStocks: []string{"AAPL"},
	}

	// 创建最近K线服务
	return service.NewRecentKlineService(
		mockClient,
		cacheManager,
		corrector,
		klineConfig,
		symbols,
		log,
	)
}

// 使用已存在的MockQOSClient
