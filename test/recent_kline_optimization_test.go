package test

import (
	"os"
	"testing"
	"time"

	"qos-market-api/internal/cache"
	"qos-market-api/internal/config"
	"qos-market-api/internal/correction"
	"qos-market-api/internal/logger"
	"qos-market-api/internal/service"
	"qos-market-api/pkg/types"
)

// TestRecentKlineOptimization 测试最近K线延迟优化功能
func TestRecentKlineOptimization(t *testing.T) {
	// 创建日志器
	log, err := logger.New(&config.LoggingConfig{
		Level:  "debug",
		Format: "text",
		Output: "stdout",
	})
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	// 创建K线配置（包含延迟优化配置）
	klineConfig := &config.KlineConfig{
		Recent: config.RecentKlineConfig{
			Enabled:                true,
			UpdateInterval:         5 * time.Second,
			MaxPeriods:             100,
			SupportedTypes:         []string{"1m", "1h", "1d"},
			InitialLoadCount:       50, // 测试用较小值
			RefreshCount:           5,  // 测试用较小值
			IntegrityCheckInterval: 10 * time.Second,
			MaxDataAge:             30 * time.Second,
		},
	}

	// 创建缓存管理器
	cacheConfig := &config.CacheConfig{
		Memory: config.MemoryCacheConfig{
			MaxSize:         1000,
			TTL:             5 * time.Minute,
			CleanupInterval: time.Minute,
		},
	}
	cacheManager := cache.NewCacheManager(cacheConfig, klineConfig, log)

	// 创建修复器（测试用，创建临时数据库）
	tmpDB := "/tmp/test_optimization_corrections.db"
	os.Remove(tmpDB) // 清理可能存在的文件
	defer os.Remove(tmpDB)

	database, err := correction.NewDatabase(tmpDB, log)
	if err != nil {
		t.Fatalf("Failed to create test database: %v", err)
	}
	defer database.Close()

	cache := correction.NewCorrectionCache(database, time.Minute, log)
	defer cache.Stop()
	corrector := correction.NewCorrector(cache, log)

	// 创建交易品种配置
	symbolsConfig := &config.SymbolsConfig{
		USStocks: []string{"AAPL", "GOOGL"},
		HKStocks: []string{"00700"},
	}

	t.Run("TestInitialLoad", func(t *testing.T) {
		// 创建模拟客户端
		mockClient := &MockQOSClientOptimized{
			connected:    true,
			delay:        50 * time.Millisecond,
			callCount:    0,
			initialCalls: 0,
			refreshCalls: 0,
		}

		// 创建服务
		recentKlineService := service.NewRecentKlineService(
			mockClient,
			cacheManager,
			corrector,
			klineConfig,
			symbolsConfig,
			log,
		)
		// 启动服务
		if err := recentKlineService.Start(); err != nil {
			t.Fatalf("Failed to start recent kline service: %v", err)
		}
		defer recentKlineService.Stop()

		// 等待初始化完成
		time.Sleep(3 * time.Second)

		// 检查服务状态
		stats := recentKlineService.GetStats()
		if !stats["is_running"].(bool) {
			t.Error("Recent kline service should be running")
		}

		if !stats["is_initialized"].(bool) {
			t.Error("Recent kline service should be initialized")
		}

		// 检查初始化调用次数
		if mockClient.initialCalls == 0 {
			t.Error("Should have made initial load calls")
		}

		t.Logf("Initial load stats: %+v", stats)
		t.Logf("Mock client initial calls: %d", mockClient.initialCalls)
	})

	t.Run("TestIncrementalUpdate", func(t *testing.T) {
		// 创建新的模拟客户端
		mockClient := &MockQOSClientOptimized{
			connected:    true,
			delay:        50 * time.Millisecond,
			callCount:    0,
			initialCalls: 0,
			refreshCalls: 0,
		}

		// 创建新的服务实例
		recentKlineService := service.NewRecentKlineService(
			mockClient,
			cacheManager,
			corrector,
			klineConfig,
			symbolsConfig,
			log,
		)

		// 启动服务
		if err := recentKlineService.Start(); err != nil {
			t.Fatalf("Failed to start recent kline service: %v", err)
		}
		defer recentKlineService.Stop()

		// 等待初始化完成
		time.Sleep(3 * time.Second)

		// 记录初始调用次数
		initialCalls := mockClient.GetCallCount()

		// 等待一次增量更新
		time.Sleep(6 * time.Second)

		// 检查增量更新调用次数
		finalCalls := mockClient.GetCallCount()
		incrementalCalls := finalCalls - initialCalls

		if incrementalCalls == 0 {
			t.Error("Should have made incremental update calls")
		}

		t.Logf("Incremental update calls: %d", incrementalCalls)
		t.Logf("Mock client refresh calls: %d", mockClient.refreshCalls)
	})

	t.Run("TestIntegrityCheck", func(t *testing.T) {
		// 创建新的模拟客户端
		mockClient := &MockQOSClientOptimized{
			connected:    true,
			delay:        50 * time.Millisecond,
			callCount:    0,
			initialCalls: 0,
			refreshCalls: 0,
		}

		// 创建新的服务实例
		recentKlineService := service.NewRecentKlineService(
			mockClient,
			cacheManager,
			corrector,
			klineConfig,
			symbolsConfig,
			log,
		)

		// 启动服务
		if err := recentKlineService.Start(); err != nil {
			t.Fatalf("Failed to start recent kline service: %v", err)
		}
		defer recentKlineService.Stop()

		// 等待初始化完成
		time.Sleep(3 * time.Second)

		// 等待完整性检查触发
		time.Sleep(12 * time.Second)

		// 检查服务状态
		stats := recentKlineService.GetStats()
		cachedItems := stats["cached_items"].(int)

		if cachedItems == 0 {
			t.Error("Should have cached items")
		}

		t.Logf("Integrity check stats: %+v", stats)
	})
}

// MockQOSClientOptimized 优化的模拟QOS客户端
type MockQOSClientOptimized struct {
	connected      bool
	delay          time.Duration
	callCount      int
	initialCalls   int
	refreshCalls   int
	connectedSince time.Time
}

func (m *MockQOSClientOptimized) GetKline(requests []types.KlineRequest) ([]types.KlineData, error) {
	// 模拟网络延迟
	time.Sleep(m.delay)

	m.callCount++

	// 根据请求数量判断是初始化还是增量更新
	if len(requests) > 0 && requests[0].Count >= 50 {
		m.initialCalls++
	} else {
		m.refreshCalls++
	}

	// 生成模拟数据
	var result []types.KlineData
	for _, req := range requests {
		klines := make([]types.Kline, req.Count)
		baseTime := time.Now().Unix() - int64(req.Count*60) // 假设1分钟间隔

		for i := 0; i < req.Count; i++ {
			klines[i] = types.Kline{
				Code:       req.Code,
				OpenPrice:  "100.00",
				ClosePrice: "101.00",
				HighPrice:  "102.00",
				LowPrice:   "99.00",
				Volume:     "1000",
				Timestamp:  baseTime + int64(i*60),
				KlineType:  req.KlineType,
			}
		}

		result = append(result, types.KlineData{
			Code:   req.Code,
			Klines: klines,
		})
	}

	return result, nil
}

func (m *MockQOSClientOptimized) IsConnected() bool {
	if !m.connected {
		return false
	}
	// 模拟连接需要一点时间建立
	if m.connectedSince.IsZero() {
		m.connectedSince = time.Now()
	}
	return time.Since(m.connectedSince) > 100*time.Millisecond
}

func (m *MockQOSClientOptimized) GetCallCount() int {
	return m.callCount
}

func (m *MockQOSClientOptimized) ResetCallCount() {
	m.callCount = 0
	m.initialCalls = 0
	m.refreshCalls = 0
	m.connectedSince = time.Time{} // 重置连接时间
}
