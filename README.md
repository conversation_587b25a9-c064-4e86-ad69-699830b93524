# QOS Market API

一个稳定运行的行情接口程序，基于Go语言开发，从QOS.HK获取金融市场数据并提供HTTP和WebSocket接口。

## 功能特性

- 🔄 从QOS.HK的WebSocket和HTTP接口获取K线数据
- 🌐 提供HTTP和WebSocket接口供其他服务调用
- 💾 智能缓存机制，优先使用缓存数据
- 📈 支持美股、港股和数字货币
- ⚙️ 可配置的交易品种和数据类型
- 📝 完整的日志记录
- 🔧 异常处理和自动重启功能
- 🏥 健康检查和监控
- 🔧 **K线数据修复功能** - 支持对错误K线数据进行修复

## 支持的市场

- **美股 (US)**: 支持盘前、盘中、盘后、夜盘交易时段
- **港股 (HK)**: 上午和下午交易时段
- **A股**: 沪深交易所
- **数字货币 (CF)**: 24小时交易
- **外汇 (FX)**: 全球外汇市场
- **商品 (CM)**: 黄金、白银、大宗商品、能源

## 项目结构

```
qos-market-api/
├── cmd/
│   └── server/
│       └── main.go          # 主程序入口
├── internal/
│   ├── config/              # 配置管理
│   ├── cache/               # 缓存系统
│   ├── client/              # QOS.HK客户端
│   ├── handler/             # HTTP处理器
│   ├── websocket/           # WebSocket服务
│   ├── model/               # 数据模型
│   └── logger/              # 日志系统
├── pkg/
│   └── types/               # 公共类型定义
├── configs/
│   └── config.yaml          # 配置文件
├── docs/                    # 文档
├── scripts/                 # 脚本文件
└── tests/                   # 测试文件
```

## 快速开始

### 方法一：使用启动脚本（推荐）

```bash
# 运行启动脚本，它会自动检查依赖、构建和启动
./scripts/start.sh
```

### 方法二：使用 Makefile

```bash
# 1. 初始化项目
make init

# 2. 编辑配置文件，设置您的 QOS.HK API 密钥
vim configs/config.yaml

# 3. 构建并运行
make run
```

### 方法三：手动步骤

```bash
# 1. 复制配置文件
cp configs/config.example.yaml configs/config.yaml

# 2. 编辑配置文件，设置您的 QOS.HK API 密钥
vim configs/config.yaml

# 3. 安装依赖
go mod tidy

# 4. 构建
go build -o bin/qos-market-api cmd/server/main.go

# 5. 运行
./bin/qos-market-api -config=configs/config.yaml
```

### 4. 验证服务

访问健康检查接口：
```bash
curl http://localhost:8080/health
```

访问监控接口：
```bash
curl http://localhost:9090/metrics
```

## API文档

### HTTP接口

- `GET /health` - 健康检查
- `GET /api/v1/snapshot` - 获取实时行情快照
- `GET /api/v1/kline` - 获取K线数据
- `GET /api/v1/depth` - 获取盘口深度
- `GET /api/v1/trades` - 获取成交明细

### K线修复API

- `POST /api/v1/corrections` - 添加修复数据
- `GET /api/v1/corrections` - 获取修复数据列表
- `GET /api/v1/corrections/{id}` - 获取单个修复数据
- `PUT /api/v1/corrections/{id}` - 更新修复数据
- `DELETE /api/v1/corrections/{id}` - 删除修复数据
- `POST /api/v1/corrections/{id}/toggle` - 切换修复数据状态
- `GET /api/v1/corrections/stats` - 获取修复统计信息

### Web管理界面

- `GET /corrections` - K线数据修复管理界面

### WebSocket接口

连接地址: `ws://localhost:8080/ws`

支持的消息类型:
- 订阅实时行情
- 订阅K线数据
- 订阅盘口深度
- 订阅成交明细

## 配置说明

详细配置说明请参考 [配置文档](docs/config.md)

### K线修复功能配置

在 `configs/config.yaml` 中启用K线修复功能：

```yaml
# K线修复配置
correction:
  enabled: true                    # 是否启用修复功能
  database_path: "data/corrections.db"  # 数据库文件路径
  refresh_interval: 60s            # 缓存刷新间隔
```

## Docker 部署

### 使用 Docker Compose（推荐）

```bash
# 1. 编辑配置文件
cp configs/config.example.yaml configs/config.yaml
vim configs/config.yaml

# 2. 启动服务
docker-compose up -d

# 3. 查看日志
docker-compose logs -f qos-market-api

# 4. 停止服务
docker-compose down
```

### 使用 Docker

```bash
# 1. 构建镜像
make docker-build

# 2. 运行容器
make docker-run
```

## 开发

### 可用的 Make 命令

```bash
make help          # 显示所有可用命令
make init          # 初始化项目
make build         # 构建应用
make run           # 构建并运行
make dev           # 开发模式运行
make test          # 运行测试
make bench         # 运行基准测试
make test-coverage # 运行测试覆盖率
make lint          # 代码检查
make fmt           # 格式化代码
make clean         # 清理构建文件
```

### 依赖管理

```bash
go mod tidy
```

### 运行测试

```bash
make test
# 或
go test ./...
```

### 基准测试

```bash
make bench
```

### 代码覆盖率

```bash
make test-coverage
```

## 配置说明

详细的配置说明请参考：
- [配置文档](docs/config.md)
- [API文档](docs/API.md)
- [K线修复功能](docs/CORRECTION.md) - K线数据修复功能使用指南

## 监控和运维

### 健康检查

```bash
# 应用健康检查
curl http://localhost:8080/health

# 监控健康检查
curl http://localhost:9090/health
```

### 指标监控

```bash
# 获取应用指标
curl http://localhost:9090/metrics
```

### 日志

日志文件位置：`logs/qos-market-api.log`

可以通过配置文件调整日志级别和输出格式。

## 故障排除

### 常见问题

1. **API密钥未设置**
   - 确保在 `configs/config.yaml` 中设置了正确的 QOS.HK API 密钥

2. **端口被占用**
   - 检查端口 8080 和 9090 是否被其他程序占用
   - 可以在配置文件中修改端口

3. **WebSocket连接失败**
   - 检查网络连接
   - 确认 QOS.HK 服务可用

4. **内存使用过高**
   - 调整缓存配置中的 `max_size` 参数
   - 减少缓存的 TTL 时间

### 日志级别

可以通过修改配置文件中的 `logging.level` 来调整日志级别：
- `debug`: 详细调试信息
- `info`: 一般信息（推荐）
- `warn`: 警告信息
- `error`: 仅错误信息

## 贡献

欢迎提交 Issue 和 Pull Request！

## 许可证

MIT License
