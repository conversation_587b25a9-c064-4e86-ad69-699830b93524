package correction

import (
	"os"
	"testing"
	"time"

	"qos-market-api/internal/logger"
	"qos-market-api/pkg/types"
)

func TestDatabase(t *testing.T) {
	// 创建临时数据库
	dbPath := "test_corrections.db"
	defer os.Remove(dbPath)

	log := logger.GetLogger()
	db, err := NewDatabase(dbPath, log)
	if err != nil {
		t.Fatalf("Failed to create database: %v", err)
	}
	defer db.Close()

	// 测试添加修复数据
	req := &types.KlineCorrectionRequest{
		Code:      "US:AAPL",
		Timestamp: time.Now().Unix(),
		Field:     "cl",
		CorrValue: "150.25",
		Reason:    "Test correction",
	}

	correction, err := db.AddCorrection(req)
	if err != nil {
		t.Fatalf("Failed to add correction: %v", err)
	}

	if correction.ID == 0 {
		t.<PERSON>rror("Expected correction ID to be set")
	}

	if correction.Code != req.Code {
		t.<PERSON>rf("Expected code %s, got %s", req.Code, correction.Code)
	}

	// 测试获取修复数据
	retrieved, err := db.GetCorrection(correction.ID)
	if err != nil {
		t.Fatalf("Failed to get correction: %v", err)
	}

	if retrieved == nil {
		t.Fatal("Expected correction to be found")
	}

	if retrieved.CorrValue != req.CorrValue {
		t.Errorf("Expected correction value %s, got %s", req.CorrValue, retrieved.CorrValue)
	}

	// 测试获取活跃修复数据
	activeCorrections, err := db.GetActiveCorrections()
	if err != nil {
		t.Fatalf("Failed to get active corrections: %v", err)
	}

	if len(activeCorrections) != 1 {
		t.Errorf("Expected 1 active correction, got %d", len(activeCorrections))
	}

	// 测试切换状态
	err = db.ToggleCorrection(correction.ID, false)
	if err != nil {
		t.Fatalf("Failed to toggle correction: %v", err)
	}

	activeCorrections, err = db.GetActiveCorrections()
	if err != nil {
		t.Fatalf("Failed to get active corrections after toggle: %v", err)
	}

	if len(activeCorrections) != 0 {
		t.Errorf("Expected 0 active corrections after toggle, got %d", len(activeCorrections))
	}

	// 测试删除修复数据
	err = db.DeleteCorrection(correction.ID)
	if err != nil {
		t.Fatalf("Failed to delete correction: %v", err)
	}

	retrieved, err = db.GetCorrection(correction.ID)
	if err != nil {
		t.Fatalf("Failed to get correction after delete: %v", err)
	}

	if retrieved != nil {
		t.Error("Expected correction to be deleted")
	}
}

func TestCorrectionCache(t *testing.T) {
	// 创建临时数据库
	dbPath := "test_cache_corrections.db"
	defer os.Remove(dbPath)

	log := logger.GetLogger()
	db, err := NewDatabase(dbPath, log)
	if err != nil {
		t.Fatalf("Failed to create database: %v", err)
	}
	defer db.Close()

	// 添加测试数据
	req := &types.KlineCorrectionRequest{
		Code:      "US:AAPL",
		Timestamp: time.Now().Unix(),
		Field:     "cl",
		CorrValue: "150.25",
		Reason:    "Test correction",
	}

	_, err = db.AddCorrection(req)
	if err != nil {
		t.Fatalf("Failed to add correction: %v", err)
	}

	// 创建缓存
	cache := NewCorrectionCache(db, 1*time.Second, log)
	defer cache.Stop()

	// 等待缓存加载
	time.Sleep(100 * time.Millisecond)

	// 测试获取修复数据
	value, exists := cache.GetCorrection(req.Code, req.Timestamp, req.Field)
	if !exists {
		t.Error("Expected correction to exist in cache")
	}

	if value != req.CorrValue {
		t.Errorf("Expected correction value %s, got %s", req.CorrValue, value)
	}

	// 测试检查是否有修复数据
	hasCorrections := cache.HasCorrections(req.Code, req.Timestamp)
	if !hasCorrections {
		t.Error("Expected corrections to exist")
	}

	// 测试获取所有修复数据
	allCorrections := cache.GetAllCorrections(req.Code, req.Timestamp)
	if len(allCorrections) != 1 {
		t.Errorf("Expected 1 correction, got %d", len(allCorrections))
	}

	if allCorrections[req.Field] != req.CorrValue {
		t.Errorf("Expected correction value %s, got %s", req.CorrValue, allCorrections[req.Field])
	}
}

func TestCorrector(t *testing.T) {
	// 创建临时数据库
	dbPath := "test_corrector.db"
	defer os.Remove(dbPath)

	log := logger.GetLogger()
	db, err := NewDatabase(dbPath, log)
	if err != nil {
		t.Fatalf("Failed to create database: %v", err)
	}
	defer db.Close()

	// 添加测试数据
	timestamp := time.Now().Unix()
	req := &types.KlineCorrectionRequest{
		Code:      "US:AAPL",
		Timestamp: timestamp,
		Field:     "cl",
		CorrValue: "150.25",
		Reason:    "Test correction",
	}

	_, err = db.AddCorrection(req)
	if err != nil {
		t.Fatalf("Failed to add correction: %v", err)
	}

	// 创建修复器
	cache := NewCorrectionCache(db, 1*time.Second, log)
	defer cache.Stop()
	corrector := NewCorrector(cache, log)

	// 等待缓存加载
	time.Sleep(100 * time.Millisecond)

	// 创建测试K线数据
	klineData := &types.KlineData{
		Code: "US:AAPL",
		Klines: []types.Kline{
			{
				Code:       "US:AAPL",
				OpenPrice:  "149.00",
				ClosePrice: "149.50", // 这个值应该被修复为150.25
				HighPrice:  "151.00",
				LowPrice:   "148.00",
				Volume:     "1000000",
				Timestamp:  timestamp,
				KlineType:  types.KlineDay,
			},
		},
	}

	// 应用修复
	correctedData := corrector.ApplyCorrections(klineData)

	// 验证修复结果
	if len(correctedData.Klines) != 1 {
		t.Fatalf("Expected 1 kline, got %d", len(correctedData.Klines))
	}

	correctedKline := correctedData.Klines[0]
	if correctedKline.ClosePrice != req.CorrValue {
		t.Errorf("Expected corrected close price %s, got %s", req.CorrValue, correctedKline.ClosePrice)
	}

	// 验证其他字段未被修改
	if correctedKline.OpenPrice != "149.00" {
		t.Errorf("Expected open price to remain unchanged, got %s", correctedKline.OpenPrice)
	}
}
