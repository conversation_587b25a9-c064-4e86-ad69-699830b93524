package correction

import (
	"database/sql"
	"fmt"
	"time"

	"qos-market-api/internal/logger"
	"qos-market-api/pkg/types"

	_ "github.com/mattn/go-sqlite3"
)

// Database K线修复数据库操作
type Database struct {
	db     *sql.DB
	logger logger.Logger
}

// NewDatabase 创建新的数据库实例
func NewDatabase(dbPath string, log logger.Logger) (*Database, error) {
	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	database := &Database{
		db:     db,
		logger: log,
	}

	// 初始化表结构
	if err := database.initTables(); err != nil {
		return nil, fmt.Errorf("failed to init tables: %w", err)
	}

	return database, nil
}

// initTables 初始化数据库表
func (d *Database) initTables() error {
	createTableSQL := `
	CREATE TABLE IF NOT EXISTS kline_corrections (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		code TEXT NOT NULL,
		timestamp INTEGER NOT NULL,
		field TEXT NOT NULL,
		orig_value TEXT,
		corr_value TEXT NOT NULL,
		reason TEXT,
		created_at INTEGER NOT NULL,
		updated_at INTEGER NOT NULL,
		is_active BOOLEAN NOT NULL DEFAULT 1
	);

	CREATE INDEX IF NOT EXISTS idx_corrections_lookup
	ON kline_corrections(code, timestamp, field, is_active);

	CREATE INDEX IF NOT EXISTS idx_corrections_time
	ON kline_corrections(created_at);

	CREATE INDEX IF NOT EXISTS idx_corrections_code_time
	ON kline_corrections(code, timestamp, is_active);
	`

	_, err := d.db.Exec(createTableSQL)
	if err != nil {
		return fmt.Errorf("failed to create tables: %w", err)
	}

	d.logger.Info("Database tables initialized successfully")
	return nil
}

// AddCorrection 添加修复数据
func (d *Database) AddCorrection(req *types.KlineCorrectionRequest) (*types.KlineCorrection, error) {
	now := time.Now().Unix()

	insertSQL := `
	INSERT INTO kline_corrections
	(code, timestamp, field, corr_value, reason, created_at, updated_at, is_active)
	VALUES (?, ?, ?, ?, ?, ?, ?, 1)
	`

	result, err := d.db.Exec(insertSQL,
		req.Code, req.Timestamp, req.Field,
		req.CorrValue, req.Reason, now, now)
	if err != nil {
		return nil, fmt.Errorf("failed to insert correction: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return nil, fmt.Errorf("failed to get last insert id: %w", err)
	}

	correction := &types.KlineCorrection{
		ID:        id,
		Code:      req.Code,
		Timestamp: req.Timestamp,
		Field:     req.Field,
		CorrValue: req.CorrValue,
		Reason:    req.Reason,
		CreatedAt: now,
		UpdatedAt: now,
		IsActive:  true,
	}

	d.logger.Infof("Added correction: id=%d, code=%s, timestamp=%d", id, req.Code, req.Timestamp)
	return correction, nil
}

// GetCorrection 获取单个修复数据
func (d *Database) GetCorrection(id int64) (*types.KlineCorrection, error) {
	selectSQL := `
	SELECT id, code, timestamp, field,
		   COALESCE(orig_value, '') as orig_value,
		   corr_value,
		   COALESCE(reason, '') as reason,
		   created_at, updated_at, is_active
	FROM kline_corrections
	WHERE id = ?
	`

	row := d.db.QueryRow(selectSQL, id)

	correction := &types.KlineCorrection{}
	err := row.Scan(
		&correction.ID, &correction.Code, &correction.Timestamp,
		&correction.Field, &correction.OrigValue, &correction.CorrValue, &correction.Reason,
		&correction.CreatedAt, &correction.UpdatedAt, &correction.IsActive,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get correction: %w", err)
	}

	return correction, nil
}

// GetCorrections 获取修复数据列表
func (d *Database) GetCorrections(code string, limit, offset int) ([]*types.KlineCorrection, error) {
	var args []interface{}
	whereClause := "WHERE 1=1"

	if code != "" {
		whereClause += " AND code = ?"
		args = append(args, code)
	}

	selectSQL := fmt.Sprintf(`
	SELECT id, code, timestamp, field,
		   COALESCE(orig_value, '') as orig_value,
		   corr_value,
		   COALESCE(reason, '') as reason,
		   created_at, updated_at, is_active
	FROM kline_corrections
	%s
	ORDER BY created_at DESC
	LIMIT ? OFFSET ?
	`, whereClause)

	args = append(args, limit, offset)

	rows, err := d.db.Query(selectSQL, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query corrections: %w", err)
	}
	defer rows.Close()

	var corrections []*types.KlineCorrection
	for rows.Next() {
		correction := &types.KlineCorrection{}
		err := rows.Scan(
			&correction.ID, &correction.Code, &correction.Timestamp,
			&correction.Field, &correction.OrigValue, &correction.CorrValue, &correction.Reason,
			&correction.CreatedAt, &correction.UpdatedAt, &correction.IsActive,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan correction: %w", err)
		}
		corrections = append(corrections, correction)
	}

	return corrections, nil
}

// GetActiveCorrections 获取所有活跃的修复数据
func (d *Database) GetActiveCorrections() ([]*types.KlineCorrection, error) {
	selectSQL := `
	SELECT id, code, timestamp, field,
		   COALESCE(orig_value, '') as orig_value,
		   corr_value,
		   COALESCE(reason, '') as reason,
		   created_at, updated_at, is_active
	FROM kline_corrections
	WHERE is_active = 1
	ORDER BY code, timestamp
	`

	rows, err := d.db.Query(selectSQL)
	if err != nil {
		return nil, fmt.Errorf("failed to query active corrections: %w", err)
	}
	defer rows.Close()

	var corrections []*types.KlineCorrection
	for rows.Next() {
		correction := &types.KlineCorrection{}
		err := rows.Scan(
			&correction.ID, &correction.Code, &correction.Timestamp,
			&correction.Field, &correction.OrigValue, &correction.CorrValue, &correction.Reason,
			&correction.CreatedAt, &correction.UpdatedAt, &correction.IsActive,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan correction: %w", err)
		}
		corrections = append(corrections, correction)
	}

	return corrections, nil
}

// UpdateCorrection 更新修复数据
func (d *Database) UpdateCorrection(id int64, req *types.KlineCorrectionRequest) error {
	updateSQL := `
	UPDATE kline_corrections 
	SET corr_value = ?, reason = ?, updated_at = ?
	WHERE id = ?
	`

	now := time.Now().Unix()
	_, err := d.db.Exec(updateSQL, req.CorrValue, req.Reason, now, id)
	if err != nil {
		return fmt.Errorf("failed to update correction: %w", err)
	}

	d.logger.Infof("Updated correction: id=%d", id)
	return nil
}

// DeleteCorrection 删除修复数据
func (d *Database) DeleteCorrection(id int64) error {
	deleteSQL := `DELETE FROM kline_corrections WHERE id = ?`

	_, err := d.db.Exec(deleteSQL, id)
	if err != nil {
		return fmt.Errorf("failed to delete correction: %w", err)
	}

	d.logger.Infof("Deleted correction: id=%d", id)
	return nil
}

// ToggleCorrection 切换修复数据的启用状态
func (d *Database) ToggleCorrection(id int64, isActive bool) error {
	updateSQL := `
	UPDATE kline_corrections 
	SET is_active = ?, updated_at = ?
	WHERE id = ?
	`

	now := time.Now().Unix()
	_, err := d.db.Exec(updateSQL, isActive, now, id)
	if err != nil {
		return fmt.Errorf("failed to toggle correction: %w", err)
	}

	d.logger.Infof("Toggled correction: id=%d, active=%v", id, isActive)
	return nil
}

// Close 关闭数据库连接
func (d *Database) Close() error {
	return d.db.Close()
}
