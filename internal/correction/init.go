package correction

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"qos-market-api/internal/config"
	"qos-market-api/internal/logger"
)

// InitCorrectionSystem 初始化修复系统
func InitCorrectionSystem(cfg *config.Config, log logger.Logger) (*Handler, *Corrector, error) {
	if !cfg.Correction.Enabled {
		return nil, nil, nil
	}

	// 确保数据目录存在
	dbDir := filepath.Dir(cfg.Correction.DatabasePath)
	if err := os.MkdirAll(dbDir, 0755); err != nil {
		return nil, nil, fmt.Errorf("failed to create database directory: %w", err)
	}

	// 初始化数据库
	database, err := NewDatabase(cfg.Correction.DatabasePath, log)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to initialize database: %w", err)
	}

	// 初始化缓存
	refreshInterval := cfg.Correction.RefreshInterval
	if refreshInterval == 0 {
		refreshInterval = 60 * time.Second // 默认60秒
	}
	
	cache := NewCorrectionCache(database, refreshInterval, log)

	// 初始化修复器
	corrector := NewCorrector(cache, log)

	// 初始化处理器
	handler := NewHandler(database, cache, corrector, log)

	log.Info("Correction system initialized successfully")
	return handler, corrector, nil
}

// CloseCorrectionSystem 关闭修复系统
func CloseCorrectionSystem(handler *Handler, corrector *Corrector) {
	if handler != nil && handler.cache != nil {
		handler.cache.Stop()
	}
	if handler != nil && handler.database != nil {
		handler.database.Close()
	}
}
