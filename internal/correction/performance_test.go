package correction

import (
	"fmt"
	"testing"
	"time"

	"qos-market-api/internal/config"
	"qos-market-api/internal/logger"
	"qos-market-api/pkg/types"
)

// createTestLogger 创建测试用的logger
func createTestLogger() logger.Logger {
	cfg := &config.LoggingConfig{
		Level:  "info",
		Format: "text",
		Output: "stdout",
	}
	log, _ := logger.New(cfg)
	return log
}

// BenchmarkCorrectionPerformance 性能基准测试
func BenchmarkCorrectionPerformance(b *testing.B) {
	// 创建测试数据库
	db, err := NewDatabase(":memory:", createTestLogger())
	if err != nil {
		b.Fatalf("Failed to create database: %v", err)
	}
	defer db.Close()

	// 创建缓存和修复器
	cache := NewCorrectionCache(db, 1*time.Second, createTestLogger())
	defer cache.Stop()
	corrector := NewCorrector(cache, createTestLogger())

	// 准备测试数据 - 添加多个修复数据
	testCode := "US:AAPL"
	baseTimestamp := time.Now().Unix()

	// 添加不同时间点的修复数据
	for i := 0; i < 100; i++ {
		timestamp := baseTimestamp + int64(i*60) // 每分钟一个修复点

		// 添加各种字段的修复数据
		fields := []string{"o", "cl", "h", "l", "v"}
		for _, field := range fields {
			req := &types.KlineCorrectionRequest{
				Code:      testCode,
				Timestamp: timestamp,
				Field:     field,
				CorrValue: fmt.Sprintf("%.2f", 150.0+float64(i)*0.1),
				Reason:    fmt.Sprintf("Test correction %d", i),
			}

			if _, err := db.AddCorrection(req); err != nil {
				b.Fatalf("Failed to add correction: %v", err)
			}
		}
	}

	// 强制刷新缓存
	if err := cache.ForceRefresh(); err != nil {
		b.Fatalf("Failed to refresh cache: %v", err)
	}

	// 创建测试K线数据
	klineData := &types.KlineData{
		Code:   testCode,
		Klines: make([]types.Kline, 1000), // 1000个K线数据点
	}

	// 填充K线数据
	for i := 0; i < 1000; i++ {
		klineData.Klines[i] = types.Kline{
			Code:       testCode,
			OpenPrice:  "149.00",
			ClosePrice: "149.50",
			HighPrice:  "151.00",
			LowPrice:   "148.00",
			Volume:     "1000000",
			Timestamp:  baseTimestamp + int64(i*300), // 5分钟K线
			KlineType:  types.Kline5Min,
		}
	}

	// 重置性能指标
	corrector.ResetMetrics()

	// 开始基准测试
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = corrector.ApplyCorrections(klineData)
	}
	b.StopTimer()

	// 输出性能统计
	metrics := corrector.GetMetrics()
	avgLatencyMs := float64(corrector.GetAverageLatencyNs()) / 1000000.0
	maxLatencyMs := float64(metrics.MaxLatencyNs) / 1000000.0

	b.Logf("Performance Metrics:")
	b.Logf("  Total Corrections: %d", metrics.TotalCorrections)
	b.Logf("  Average Latency: %.2f ms", avgLatencyMs)
	b.Logf("  Max Latency: %.2f ms", maxLatencyMs)
	b.Logf("  Cache Hits: %d", metrics.CacheHits)
	b.Logf("  Cache Misses: %d", metrics.CacheMisses)
}

// TestCorrectionLatency 延迟测试
func TestCorrectionLatency(t *testing.T) {
	// 创建测试数据库
	db, err := NewDatabase(":memory:", createTestLogger())
	if err != nil {
		t.Fatalf("Failed to create database: %v", err)
	}
	defer db.Close()

	// 创建缓存和修复器
	cache := NewCorrectionCache(db, 1*time.Second, createTestLogger())
	defer cache.Stop()
	corrector := NewCorrector(cache, createTestLogger())

	// 准备测试数据
	testCode := "US:AAPL"
	baseTimestamp := time.Now().Unix()

	// 添加修复数据
	req := &types.KlineCorrectionRequest{
		Code:      testCode,
		Timestamp: baseTimestamp,
		Field:     "cl",
		CorrValue: "150.25",
		Reason:    "Test correction",
	}

	if _, err := db.AddCorrection(req); err != nil {
		t.Fatalf("Failed to add correction: %v", err)
	}

	// 强制刷新缓存
	if err := cache.ForceRefresh(); err != nil {
		t.Fatalf("Failed to refresh cache: %v", err)
	}

	// 创建测试K线数据
	klineData := &types.KlineData{
		Code: testCode,
		Klines: []types.Kline{
			{
				Code:       testCode,
				OpenPrice:  "149.00",
				ClosePrice: "149.50",
				HighPrice:  "151.00",
				LowPrice:   "148.00",
				Volume:     "1000000",
				Timestamp:  baseTimestamp,
				KlineType:  types.KlineDay,
			},
		},
	}

	// 重置性能指标
	corrector.ResetMetrics()

	// 执行修复
	start := time.Now()
	correctedData := corrector.ApplyCorrections(klineData)
	latency := time.Since(start)

	// 验证修复结果
	if len(correctedData.Klines) != 1 {
		t.Fatalf("Expected 1 kline, got %d", len(correctedData.Klines))
	}

	if correctedData.Klines[0].ClosePrice != "150.25" {
		t.Errorf("Expected close price 150.25, got %s", correctedData.Klines[0].ClosePrice)
	}

	// 检查延迟
	if latency > 10*time.Millisecond {
		t.Errorf("Correction latency too high: %v", latency)
	}

	// 输出性能指标
	metrics := corrector.GetMetrics()
	avgLatencyMs := float64(corrector.GetAverageLatencyNs()) / 1000000.0

	t.Logf("Correction completed in %v", latency)
	t.Logf("Average latency: %.2f ms", avgLatencyMs)
	t.Logf("Total corrections: %d", metrics.TotalCorrections)
}

// TestMultipleCorrectionsInRange 测试时间范围内多个修复值的应用
func TestMultipleCorrectionsInRange(t *testing.T) {
	// 创建测试数据库
	db, err := NewDatabase(":memory:", createTestLogger())
	if err != nil {
		t.Fatalf("Failed to create database: %v", err)
	}
	defer db.Close()

	// 创建缓存和修复器
	cache := NewCorrectionCache(db, 1*time.Second, createTestLogger())
	defer cache.Stop()
	corrector := NewCorrector(cache, createTestLogger())

	// 准备测试数据
	testCode := "US:AAPL"
	baseTimestamp := time.Now().Unix()

	// 添加多个时间点的修复数据（在同一个日K线范围内）
	corrections := []struct {
		timestamp int64
		field     string
		value     string
	}{
		{baseTimestamp + 3600, "cl", "150.25"}, // 1小时后
		{baseTimestamp + 7200, "cl", "151.50"}, // 2小时后（更新的值）
		{baseTimestamp + 10800, "h", "152.00"}, // 3小时后，不同字段
	}

	for _, corr := range corrections {
		req := &types.KlineCorrectionRequest{
			Code:      testCode,
			Timestamp: corr.timestamp,
			Field:     corr.field,
			CorrValue: corr.value,
			Reason:    "Test multiple corrections",
		}

		if _, err := db.AddCorrection(req); err != nil {
			t.Fatalf("Failed to add correction: %v", err)
		}
	}

	// 强制刷新缓存
	if err := cache.ForceRefresh(); err != nil {
		t.Fatalf("Failed to refresh cache: %v", err)
	}

	// 创建日K线数据（覆盖所有修复时间点）
	klineData := &types.KlineData{
		Code: testCode,
		Klines: []types.Kline{
			{
				Code:       testCode,
				OpenPrice:  "149.00",
				ClosePrice: "149.50", // 应该被修复为151.50（最新的值）
				HighPrice:  "151.00", // 应该被修复为152.00
				LowPrice:   "148.00",
				Volume:     "1000000",
				Timestamp:  baseTimestamp,
				KlineType:  types.KlineDay,
			},
		},
	}

	// 执行修复
	correctedData := corrector.ApplyCorrections(klineData)

	// 验证修复结果
	if len(correctedData.Klines) != 1 {
		t.Fatalf("Expected 1 kline, got %d", len(correctedData.Klines))
	}

	kline := correctedData.Klines[0]

	// 验证应用了最新的收盘价修复值
	if kline.ClosePrice != "151.50" {
		t.Errorf("Expected close price 151.50 (latest correction), got %s", kline.ClosePrice)
	}

	// 验证应用了最高价修复值
	if kline.HighPrice != "152.00" {
		t.Errorf("Expected high price 152.00, got %s", kline.HighPrice)
	}

	// 验证未修复的字段保持原值
	if kline.OpenPrice != "149.00" {
		t.Errorf("Expected open price 149.00 (unchanged), got %s", kline.OpenPrice)
	}

	t.Logf("Successfully applied multiple corrections in time range")
	t.Logf("Final values: O=%s, C=%s, H=%s, L=%s, V=%s",
		kline.OpenPrice, kline.ClosePrice, kline.HighPrice, kline.LowPrice, kline.Volume)
}
