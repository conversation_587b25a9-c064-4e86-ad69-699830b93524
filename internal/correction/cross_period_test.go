package correction

import (
	"os"
	"testing"
	"time"

	"qos-market-api/internal/logger"
	"qos-market-api/pkg/types"
)

// TestCrossPeriodCorrection 测试跨周期修复功能
func TestCrossPeriodCorrection(t *testing.T) {
	// 创建临时数据库
	dbPath := "test_cross_period.db"
	defer os.Remove(dbPath)

	// 创建日志器
	log := logger.GetLogger()

	// 创建数据库
	db, err := NewDatabase(dbPath, log)
	if err != nil {
		t.Fatalf("Failed to create database: %v", err)
	}
	defer db.Close()

	// 创建缓存
	cache := NewCorrectionCache(db, 1*time.Second, log)
	defer cache.Stop()

	// 创建修复器
	corrector := NewCorrector(cache, log)

	// 等待缓存初始化
	time.Sleep(100 * time.Millisecond)

	// 测试场景：修复日线最高价，应该影响周线和月线
	testCrossPeriodHighPriceCorrection(t, db, cache, corrector)

	// 测试场景：修复分钟线最高价，应该影响小时线和日线
	testMinuteToDayCorrection(t, db, cache, corrector)

	// 测试场景：修复开盘价传播
	testOpenPriceCorrection(t, db, cache, corrector)
}

// testCrossPeriodHighPriceCorrection 测试跨周期最高价修复
func testCrossPeriodHighPriceCorrection(t *testing.T, db *Database, cache *CorrectionCache, corrector *Corrector) {
	// 创建一个日线最高价修复
	timestamp := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC).Unix() // 2024年1月15日

	req := &types.KlineCorrectionRequest{
		Code:      "US:AAPL",
		Timestamp: timestamp,
		Field:     "h",
		CorrValue: "200.00",
		Reason:    "测试跨周期最高价修复",
	}

	// 添加修复数据
	correction, err := db.AddCorrection(req)
	if err != nil {
		t.Fatalf("Failed to add correction: %v", err)
	}

	// 刷新缓存
	if err := cache.ForceRefresh(); err != nil {
		t.Fatalf("Failed to refresh cache: %v", err)
	}

	// 简化后的修复逻辑不再需要复杂的影响范围计算
	// 修复数据直接应用到覆盖时间戳的K线

	// 测试日线K线数据修复
	dayKline := types.Kline{
		Code:       "US:AAPL",
		OpenPrice:  "190.00",
		ClosePrice: "195.00",
		HighPrice:  "198.00", // 应该被修复为200.00
		LowPrice:   "189.00",
		Volume:     "1000000",
		Timestamp:  timestamp,
		KlineType:  types.KlineDay,
	}

	correctedKline := corrector.applyCrossPeriodCorrections(dayKline)
	if correctedKline.HighPrice != "200.00" {
		t.Errorf("Expected high price to be corrected to 200.00, got %s", correctedKline.HighPrice)
	}

	// 测试时间戳在K线范围内的情况
	// 创建一个1小时K线，其时间范围包含修复时间戳
	hourKline := types.Kline{
		Code:       "US:AAPL",
		OpenPrice:  "190.00",
		ClosePrice: "195.00",
		HighPrice:  "198.00", // 应该被修复为200.00
		LowPrice:   "189.00",
		Volume:     "500000",
		Timestamp:  timestamp - 1800, // 提前30分钟，使修复时间戳在K线范围内
		KlineType:  types.Kline1Hour,
	}

	correctedHourKline := corrector.applyCrossPeriodCorrections(hourKline)
	if correctedHourKline.HighPrice != "200.00" {
		t.Errorf("Expected hour kline high price to be corrected to 200.00, got %s", correctedHourKline.HighPrice)
	}

	t.Logf("Original correction: %+v", correction)
	t.Logf("Day kline corrected: %+v", correctedKline)
	t.Logf("Hour kline corrected: %+v", correctedHourKline)
}

// testMinuteToDayCorrection 测试分钟线到日线的修复传播
func testMinuteToDayCorrection(t *testing.T, db *Database, cache *CorrectionCache, corrector *Corrector) {
	// 创建一个1分钟线最高价修复
	timestamp := time.Date(2024, 1, 15, 14, 30, 0, 0, time.UTC).Unix() // 2024年1月15日14:30

	req := &types.KlineCorrectionRequest{
		Code:      "US:AAPL",
		Timestamp: timestamp,
		Field:     "h",
		CorrValue: "205.00",
		Reason:    "测试分钟线最高价修复",
	}

	// 添加修复数据
	_, err := db.AddCorrection(req)
	if err != nil {
		t.Fatalf("Failed to add correction: %v", err)
	}

	// 刷新缓存
	if err := cache.ForceRefresh(); err != nil {
		t.Fatalf("Failed to refresh cache: %v", err)
	}

	// 测试1分钟K线数据修复
	minuteKline := types.Kline{
		Code:       "US:AAPL",
		OpenPrice:  "200.00",
		ClosePrice: "202.00",
		HighPrice:  "203.00", // 应该被修复为205.00
		LowPrice:   "199.00",
		Volume:     "10000",
		Timestamp:  timestamp,
		KlineType:  types.Kline1Min,
	}

	correctedMinuteKline := corrector.applyCrossPeriodCorrections(minuteKline)
	if correctedMinuteKline.HighPrice != "205.00" {
		t.Errorf("Expected minute high price to be corrected to 205.00, got %s", correctedMinuteKline.HighPrice)
	}

	// 测试同一天的日线K线（时间范围包含修复时间戳）
	// 日线从当天0点开始，包含14:30的修复时间戳
	dayStart := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC).Unix()
	dayKline := types.Kline{
		Code:       "US:AAPL",
		OpenPrice:  "190.00",
		ClosePrice: "202.00",
		HighPrice:  "203.00", // 应该被修复为205.00，因为修复时间戳在日线范围内
		LowPrice:   "189.00",
		Volume:     "1000000",
		Timestamp:  dayStart,
		KlineType:  types.KlineDay,
	}

	correctedDayKline := corrector.applyCrossPeriodCorrections(dayKline)

	t.Logf("Minute kline corrected: %+v", correctedMinuteKline)
	t.Logf("Day kline corrected: %+v", correctedDayKline)
}

// testOpenPriceCorrection 测试开盘价修复传播
func testOpenPriceCorrection(t *testing.T, db *Database, cache *CorrectionCache, corrector *Corrector) {
	// 创建一个日线开盘价修复
	timestamp := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC).Unix() // 2024年1月15日

	req := &types.KlineCorrectionRequest{
		Code:      "US:AAPL",
		Timestamp: timestamp,
		Field:     "o",
		CorrValue: "185.00",
		Reason:    "测试开盘价修复",
	}

	// 添加修复数据
	_, err := db.AddCorrection(req)
	if err != nil {
		t.Fatalf("Failed to add correction: %v", err)
	}

	// 刷新缓存
	if err := cache.ForceRefresh(); err != nil {
		t.Fatalf("Failed to refresh cache: %v", err)
	}

	// 测试日线K线数据修复
	dayKline := types.Kline{
		Code:       "US:AAPL",
		OpenPrice:  "190.00", // 应该被修复为185.00
		ClosePrice: "195.00",
		HighPrice:  "198.00",
		LowPrice:   "189.00",
		Volume:     "1000000",
		Timestamp:  timestamp,
		KlineType:  types.KlineDay,
	}

	correctedKline := corrector.applyCrossPeriodCorrections(dayKline)
	if correctedKline.OpenPrice != "185.00" {
		t.Errorf("Expected open price to be corrected to 185.00, got %s", correctedKline.OpenPrice)
	}

	t.Logf("Day kline open price corrected: %+v", correctedKline)
}

// TestSimplifiedCorrection 测试简化后的修复逻辑
func TestSimplifiedCorrection(t *testing.T) {
	// 测试时间戳覆盖逻辑
	timestamp := time.Date(2024, 1, 15, 14, 35, 30, 0, time.UTC).Unix()

	// 创建一个1小时K线，其时间范围应该包含修复时间戳
	hourStart := time.Date(2024, 1, 15, 14, 0, 0, 0, time.UTC).Unix()
	hourEnd := hourStart + 3600 - 1

	if timestamp >= hourStart && timestamp <= hourEnd {
		t.Logf("Correction timestamp %d is within hour kline range [%d, %d]", timestamp, hourStart, hourEnd)
	} else {
		t.Errorf("Correction timestamp %d should be within hour kline range [%d, %d]", timestamp, hourStart, hourEnd)
	}

	// 测试日线范围
	dayStart := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC).Unix()
	dayEnd := dayStart + 86400 - 1

	if timestamp >= dayStart && timestamp <= dayEnd {
		t.Logf("Correction timestamp %d is within day kline range [%d, %d]", timestamp, dayStart, dayEnd)
	} else {
		t.Errorf("Correction timestamp %d should be within day kline range [%d, %d]", timestamp, dayStart, dayEnd)
	}
}
