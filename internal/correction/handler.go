package correction

import (
	"encoding/json"
	"net/http"
	"strconv"
	"strings"

	"qos-market-api/internal/logger"
	"qos-market-api/pkg/types"
)

// Handler 修复数据管理处理器
type Handler struct {
	database  *Database
	cache     *CorrectionCache
	corrector *Corrector
	logger    logger.Logger
}

// NewHandler 创建新的处理器
func NewHandler(database *Database, cache *CorrectionCache, corrector *Corrector, log logger.Logger) *Handler {
	return &Handler{
		database:  database,
		cache:     cache,
		corrector: corrector,
		logger:    log,
	}
}

// writeJSONResponse 写入JSON响应
func (h *Handler) writeJSONResponse(w http.ResponseWriter, statusCode int, data interface{}) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(data)
}

// writeErrorResponse 写入错误响应
func (h *Handler) writeErrorResponse(w http.ResponseWriter, statusCode int, message string) {
	h.writeJSONResponse(w, statusCode, map[string]string{
		"error": message,
	})
}

// AddCorrection 添加修复数据
func (h *Handler) AddCorrection(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		h.writeErrorResponse(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	var req types.KlineCorrectionRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.writeErrorResponse(w, http.StatusBadRequest, "Invalid JSON format")
		return
	}

	// 验证字段
	if req.Code == "" || req.Timestamp == 0 || req.Field == "" || req.CorrValue == "" {
		h.writeErrorResponse(w, http.StatusBadRequest, "Missing required fields")
		return
	}

	// 验证修复字段
	validFields := map[string]bool{"o": true, "cl": true, "h": true, "l": true, "v": true}
	if !validFields[req.Field] {
		h.writeErrorResponse(w, http.StatusBadRequest, "Invalid field, must be one of: o, cl, h, l, v")
		return
	}

	correction, err := h.database.AddCorrection(&req)
	if err != nil {
		h.logger.Errorf("Failed to add correction: %v", err)
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to add correction")
		return
	}

	// 刷新缓存
	if err := h.cache.ForceRefresh(); err != nil {
		h.logger.Errorf("Failed to refresh cache after adding correction: %v", err)
	}

	h.writeJSONResponse(w, http.StatusCreated, map[string]interface{}{
		"msg":  "Correction added successfully",
		"data": correction,
	})
}

// GetCorrections 获取修复数据列表
func (h *Handler) GetCorrections(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		h.writeErrorResponse(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	// 解析查询参数
	code := r.URL.Query().Get("code")
	limitStr := r.URL.Query().Get("limit")
	offsetStr := r.URL.Query().Get("offset")

	limit := 50 // 默认限制
	if limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 1000 {
			limit = l
		}
	}

	offset := 0
	if offsetStr != "" {
		if o, err := strconv.Atoi(offsetStr); err == nil && o >= 0 {
			offset = o
		}
	}

	corrections, err := h.database.GetCorrections(code, limit, offset)
	if err != nil {
		h.logger.Errorf("Failed to get corrections: %v", err)
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to get corrections")
		return
	}

	h.writeJSONResponse(w, http.StatusOK, map[string]interface{}{
		"msg":  "OK",
		"data": corrections,
	})
}

// GetCorrection 获取单个修复数据
func (h *Handler) GetCorrection(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		h.writeErrorResponse(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	// 从URL路径中提取ID
	path := strings.TrimPrefix(r.URL.Path, "/api/v1/corrections/")
	idStr := strings.Split(path, "/")[0]

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		h.writeErrorResponse(w, http.StatusBadRequest, "Invalid correction ID")
		return
	}

	correction, err := h.database.GetCorrection(id)
	if err != nil {
		h.logger.Errorf("Failed to get correction: %v", err)
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to get correction")
		return
	}

	if correction == nil {
		h.writeErrorResponse(w, http.StatusNotFound, "Correction not found")
		return
	}

	h.writeJSONResponse(w, http.StatusOK, map[string]interface{}{
		"msg":  "OK",
		"data": correction,
	})
}

// UpdateCorrection 更新修复数据
func (h *Handler) UpdateCorrection(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPut {
		h.writeErrorResponse(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	// 从URL路径中提取ID
	path := strings.TrimPrefix(r.URL.Path, "/api/v1/corrections/")
	idStr := strings.Split(path, "/")[0]

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		h.writeErrorResponse(w, http.StatusBadRequest, "Invalid correction ID")
		return
	}

	var req types.KlineCorrectionRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.writeErrorResponse(w, http.StatusBadRequest, "Invalid JSON format")
		return
	}

	// 验证字段
	if req.CorrValue == "" {
		h.writeErrorResponse(w, http.StatusBadRequest, "Missing correction value")
		return
	}

	if err := h.database.UpdateCorrection(id, &req); err != nil {
		h.logger.Errorf("Failed to update correction: %v", err)
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to update correction")
		return
	}

	// 刷新缓存
	if err := h.cache.ForceRefresh(); err != nil {
		h.logger.Errorf("Failed to refresh cache after updating correction: %v", err)
	}

	h.writeJSONResponse(w, http.StatusOK, map[string]string{
		"msg": "Correction updated successfully",
	})
}

// DeleteCorrection 删除修复数据
func (h *Handler) DeleteCorrection(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodDelete {
		h.writeErrorResponse(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	// 从URL路径中提取ID
	path := strings.TrimPrefix(r.URL.Path, "/api/v1/corrections/")
	idStr := strings.Split(path, "/")[0]

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		h.writeErrorResponse(w, http.StatusBadRequest, "Invalid correction ID")
		return
	}

	if err := h.database.DeleteCorrection(id); err != nil {
		h.logger.Errorf("Failed to delete correction: %v", err)
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to delete correction")
		return
	}

	// 刷新缓存
	if err := h.cache.ForceRefresh(); err != nil {
		h.logger.Errorf("Failed to refresh cache after deleting correction: %v", err)
	}

	h.writeJSONResponse(w, http.StatusOK, map[string]string{
		"msg": "Correction deleted successfully",
	})
}

// ToggleCorrection 切换修复数据状态
func (h *Handler) ToggleCorrection(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		h.writeErrorResponse(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	// 从URL路径中提取ID
	path := strings.TrimPrefix(r.URL.Path, "/api/v1/corrections/")
	parts := strings.Split(path, "/")
	if len(parts) < 2 || parts[1] != "toggle" {
		h.writeErrorResponse(w, http.StatusBadRequest, "Invalid URL path")
		return
	}

	id, err := strconv.ParseInt(parts[0], 10, 64)
	if err != nil {
		h.writeErrorResponse(w, http.StatusBadRequest, "Invalid correction ID")
		return
	}

	var req struct {
		IsActive bool `json:"is_active"`
	}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.writeErrorResponse(w, http.StatusBadRequest, "Invalid JSON format")
		return
	}

	if err := h.database.ToggleCorrection(id, req.IsActive); err != nil {
		h.logger.Errorf("Failed to toggle correction: %v", err)
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to toggle correction")
		return
	}

	// 刷新缓存
	if err := h.cache.ForceRefresh(); err != nil {
		h.logger.Errorf("Failed to refresh cache after toggling correction: %v", err)
	}

	h.writeJSONResponse(w, http.StatusOK, map[string]string{
		"msg": "Correction status updated successfully",
	})
}

// GetStats 获取修复统计信息
func (h *Handler) GetStats(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		h.writeErrorResponse(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	stats := h.corrector.GetCorrectionStats()

	h.writeJSONResponse(w, http.StatusOK, map[string]interface{}{
		"msg":  "OK",
		"data": stats,
	})
}
