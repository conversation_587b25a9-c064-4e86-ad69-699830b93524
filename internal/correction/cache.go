package correction

import (
	"fmt"
	"sync"
	"time"

	"qos-market-api/internal/logger"
	"qos-market-api/pkg/types"
)

// CorrectionKey 修复数据缓存键
type CorrectionKey struct {
	Code      string
	Timestamp int64
	Field     string
}

// String 返回键的字符串表示
func (k CorrectionKey) String() string {
	return fmt.Sprintf("%s:%d:%s", k.Code, k.Timestamp, k.Field)
}

// CorrectionCache 修复数据内存缓存
type CorrectionCache struct {
	corrections     map[CorrectionKey]string // 键到修复值的映射
	mutex           sync.RWMutex
	database        *Database
	logger          logger.Logger
	stopCh          chan struct{}
	refreshInterval time.Duration
}

// NewCorrectionCache 创建新的修复数据缓存
func NewCorrectionCache(database *Database, refreshInterval time.Duration, log logger.Logger) *CorrectionCache {
	cache := &CorrectionCache{
		corrections:     make(map[CorrectionKey]string),
		database:        database,
		logger:          log,
		stopCh:          make(chan struct{}),
		refreshInterval: refreshInterval,
	}

	// 初始加载数据
	if err := cache.refresh(); err != nil {
		log.Errorf("Failed to initial load corrections: %v", err)
	}

	// 启动定期刷新
	go cache.startRefreshLoop()

	return cache
}

// startRefreshLoop 启动定期刷新循环
func (c *CorrectionCache) startRefreshLoop() {
	ticker := time.NewTicker(c.refreshInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if err := c.refresh(); err != nil {
				c.logger.Errorf("Failed to refresh corrections cache: %v", err)
			}
		case <-c.stopCh:
			return
		}
	}
}

// refresh 从数据库刷新缓存数据
func (c *CorrectionCache) refresh() error {
	corrections, err := c.database.GetActiveCorrections()
	if err != nil {
		return fmt.Errorf("failed to get active corrections: %w", err)
	}

	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 清空现有缓存
	c.corrections = make(map[CorrectionKey]string)

	// 加载新数据
	for _, correction := range corrections {
		key := CorrectionKey{
			Code:      correction.Code,
			Timestamp: correction.Timestamp,
			Field:     correction.Field,
		}
		c.corrections[key] = correction.CorrValue
	}

	c.logger.Infof("Refreshed corrections cache: loaded %d corrections", len(corrections))
	return nil
}

// GetCorrection 获取修复值
func (c *CorrectionCache) GetCorrection(code string, timestamp int64, field string) (string, bool) {
	key := CorrectionKey{
		Code:      code,
		Timestamp: timestamp,
		Field:     field,
	}

	c.mutex.RLock()
	defer c.mutex.RUnlock()

	value, exists := c.corrections[key]
	return value, exists
}

// HasCorrections 检查是否有修复数据
func (c *CorrectionCache) HasCorrections(code string, timestamp int64) bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	// 检查所有可能的字段
	fields := []string{"o", "cl", "h", "l", "v"}
	for _, field := range fields {
		key := CorrectionKey{
			Code:      code,
			Timestamp: timestamp,
			Field:     field,
		}
		if _, exists := c.corrections[key]; exists {
			return true
		}
	}
	return false
}

// GetAllCorrections 获取指定K线的所有修复数据
func (c *CorrectionCache) GetAllCorrections(code string, timestamp int64) map[string]string {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	result := make(map[string]string)
	fields := []string{"o", "cl", "h", "l", "v"}

	for _, field := range fields {
		key := CorrectionKey{
			Code:      code,
			Timestamp: timestamp,
			Field:     field,
		}
		if value, exists := c.corrections[key]; exists {
			result[field] = value
		}
	}

	return result
}

// GetCorrectionsInRange 获取时间范围内的所有修复数据
func (c *CorrectionCache) GetCorrectionsInRange(code string, startTime, endTime int64) map[string][]CorrectionEntry {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	result := make(map[string][]CorrectionEntry)
	fields := []string{"o", "cl", "h", "l", "v"}

	// 初始化结果map
	for _, field := range fields {
		result[field] = make([]CorrectionEntry, 0)
	}

	// 遍历缓存中的所有修复数据
	for key, value := range c.corrections {
		if key.Code == code && key.Timestamp >= startTime && key.Timestamp <= endTime {
			entry := CorrectionEntry{
				Timestamp: key.Timestamp,
				Value:     value,
			}
			result[key.Field] = append(result[key.Field], entry)
		}
	}

	return result
}

// CorrectionEntry 修复数据条目
type CorrectionEntry struct {
	Timestamp int64
	Value     string
}

// GetCorrectionsInRangeOptimized 优化的时间范围查询，返回合并后的修复值
func (c *CorrectionCache) GetCorrectionsInRangeOptimized(code string, startTime, endTime int64) map[string]string {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	result := make(map[string]string)
	latestTimestamps := make(map[string]int64) // 记录每个字段的最新时间戳

	// 遍历缓存中的所有修复数据
	for key, value := range c.corrections {
		if key.Code == code && key.Timestamp >= startTime && key.Timestamp <= endTime {
			// 如果这个字段还没有值，或者当前时间戳更新，则更新
			if lastTimestamp, exists := latestTimestamps[key.Field]; !exists || key.Timestamp > lastTimestamp {
				result[key.Field] = value
				latestTimestamps[key.Field] = key.Timestamp
			}
		}
	}

	return result
}

// GetCorrectionsForCodeInRange 获取指定品种在时间范围内的所有修复数据
func (c *CorrectionCache) GetCorrectionsForCodeInRange(code string, startTime, endTime int64) []*types.KlineCorrection {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	var corrections []*types.KlineCorrection

	for key, value := range c.corrections {
		if key.Code == code && key.Timestamp >= startTime && key.Timestamp <= endTime {
			correction := &types.KlineCorrection{
				Code:      key.Code,
				Timestamp: key.Timestamp,
				Field:     key.Field,
				CorrValue: value,
			}
			corrections = append(corrections, correction)
		}
	}

	return corrections
}

// Size 获取缓存大小
func (c *CorrectionCache) Size() int {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return len(c.corrections)
}

// Clear 清空缓存
func (c *CorrectionCache) Clear() {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.corrections = make(map[CorrectionKey]string)
	c.logger.Info("Corrections cache cleared")
}

// Stop 停止缓存刷新
func (c *CorrectionCache) Stop() {
	close(c.stopCh)
	c.logger.Info("Corrections cache stopped")
}

// ForceRefresh 强制刷新缓存
func (c *CorrectionCache) ForceRefresh() error {
	return c.refresh()
}

// GetStats 获取缓存统计信息
func (c *CorrectionCache) GetStats() map[string]interface{} {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	// 按品种统计
	codeStats := make(map[string]int)
	// 按字段统计
	fieldStats := make(map[string]int)

	for key := range c.corrections {
		codeStats[key.Code]++
		fieldStats[key.Field]++
	}

	return map[string]interface{}{
		"total_corrections": len(c.corrections),
		"by_code":           codeStats,
		"by_field":          fieldStats,
		"refresh_interval":  c.refreshInterval.String(),
	}
}
