package correction

import (
	"qos-market-api/internal/logger"
	"qos-market-api/pkg/types"
	"time"
)

// CorrectionMetrics 修复性能指标
type CorrectionMetrics struct {
	TotalCorrections int64
	TotalLatencyNs   int64
	MaxLatencyNs     int64
	CacheHits        int64
	CacheMisses      int64
}

// Corrector K线数据修复器
type Corrector struct {
	cache   *CorrectionCache
	logger  logger.Logger
	metrics *CorrectionMetrics
}

// NewCorrector 创建新的修复器
func NewCorrector(cache *CorrectionCache, log logger.Logger) *Corrector {
	return &Corrector{
		cache:   cache,
		logger:  log,
		metrics: &CorrectionMetrics{},
	}
}

// ApplyCorrections 应用修复数据到K线数据（带性能监控）
func (c *Corrector) ApplyCorrections(klineData *types.KlineData) *types.KlineData {
	if klineData == nil || len(klineData.Klines) == 0 {
		return klineData
	}

	// 开始性能监控
	startTime := time.Now()
	defer func() {
		latency := time.Since(startTime).Nanoseconds()
		c.updateMetrics(latency)
	}()

	// 创建修复后的数据副本
	correctedData := &types.KlineData{
		Code:   klineData.Code,
		Klines: make([]types.Kline, len(klineData.Klines)),
	}

	correctionCount := 0

	// 处理每个K线数据点
	for i, kline := range klineData.Klines {
		correctedKline := kline // 复制原始数据

		// 应用跨周期修复
		correctedKline = c.applyCrossPeriodCorrections(correctedKline)
		if correctedKline.OpenPrice != kline.OpenPrice ||
			correctedKline.ClosePrice != kline.ClosePrice ||
			correctedKline.HighPrice != kline.HighPrice ||
			correctedKline.LowPrice != kline.LowPrice ||
			correctedKline.Volume != kline.Volume {
			correctionCount++
		}

		correctedData.Klines[i] = correctedKline
	}

	if correctionCount > 0 {
		c.logger.Debugf("Applied %d corrections to %s kline data", correctionCount, klineData.Code)
	}

	return correctedData
}

// applyCrossPeriodCorrections 应用修复数据（优化版本）
func (c *Corrector) applyCrossPeriodCorrections(kline types.Kline) types.Kline {
	// 使用优化的修复应用方法，确保应用所有相关的修复数据
	correctedKline := c.applyAllCorrections(kline)

	return correctedKline
}

// getCorrectionsForKline 获取K线时间范围内的所有修复数据
func (c *Corrector) getCorrectionsForKline(kline types.Kline) map[string]string {
	// 计算K线的时间范围
	startTime, endTime := c.getKlineTimeRange(kline)

	// 使用优化的批量查询方法，获取时间范围内最新的修复值
	corrections := c.cache.GetCorrectionsInRangeOptimized(kline.Code, startTime, endTime)

	return corrections
}

// getAllCorrectionsForKline 获取K线时间范围内的所有修复数据（包括多个时间点的修复）
func (c *Corrector) getAllCorrectionsForKline(kline types.Kline) map[string][]CorrectionEntry {
	// 计算K线的时间范围
	startTime, endTime := c.getKlineTimeRange(kline)

	// 获取时间范围内的所有修复数据
	corrections := c.cache.GetCorrectionsInRange(kline.Code, startTime, endTime)

	return corrections
}

// applyAllCorrections 应用所有修复数据到K线（按时间顺序应用）
func (c *Corrector) applyAllCorrections(kline types.Kline) types.Kline {
	correctedKline := kline

	// 获取所有修复数据
	allCorrections := c.getAllCorrectionsForKline(kline)

	// 按字段处理修复数据
	for field, entries := range allCorrections {
		if len(entries) == 0 {
			continue
		}

		// 按时间戳排序，应用最新的修复值
		var latestEntry CorrectionEntry
		latestTimestamp := int64(0)

		for _, entry := range entries {
			if entry.Timestamp > latestTimestamp {
				latestTimestamp = entry.Timestamp
				latestEntry = entry
			}
		}

		// 应用最新的修复值
		if latestTimestamp > 0 {
			switch field {
			case "o":
				correctedKline.OpenPrice = latestEntry.Value
			case "cl":
				correctedKline.ClosePrice = latestEntry.Value
			case "h":
				correctedKline.HighPrice = latestEntry.Value
			case "l":
				correctedKline.LowPrice = latestEntry.Value
			case "v":
				correctedKline.Volume = latestEntry.Value
			}
		}
	}

	return correctedKline
}

// getKlineTimeRange 获取K线的时间范围
func (c *Corrector) getKlineTimeRange(kline types.Kline) (int64, int64) {
	// 根据K线类型计算时间范围
	switch kline.KlineType {
	case types.Kline1Min:
		return kline.Timestamp, kline.Timestamp + 60 - 1
	case types.Kline5Min:
		return kline.Timestamp, kline.Timestamp + 300 - 1
	case types.Kline15Min:
		return kline.Timestamp, kline.Timestamp + 900 - 1
	case types.Kline30Min:
		return kline.Timestamp, kline.Timestamp + 1800 - 1
	case types.Kline1Hour:
		return kline.Timestamp, kline.Timestamp + 3600 - 1
	case types.Kline2Hour:
		return kline.Timestamp, kline.Timestamp + 7200 - 1
	case types.Kline4Hour:
		return kline.Timestamp, kline.Timestamp + 14400 - 1
	case types.KlineDay:
		return kline.Timestamp, kline.Timestamp + 86400 - 1
	case types.KlineWeek:
		return kline.Timestamp, kline.Timestamp + 604800 - 1
	case types.KlineMonth:
		// 简化处理，假设一个月30天
		return kline.Timestamp, kline.Timestamp + 2592000 - 1
	case types.KlineYear:
		// 简化处理，假设一年365天
		return kline.Timestamp, kline.Timestamp + 31536000 - 1
	default:
		// 默认返回当前时间戳
		return kline.Timestamp, kline.Timestamp
	}
}

// applyFieldCorrections 应用字段修复
func (c *Corrector) applyFieldCorrections(kline types.Kline, corrections map[string]string) types.Kline {
	for field, corrValue := range corrections {
		switch field {
		case "o":
			kline.OpenPrice = corrValue
		case "cl":
			kline.ClosePrice = corrValue
		case "h":
			kline.HighPrice = corrValue
		case "l":
			kline.LowPrice = corrValue
		case "v":
			kline.Volume = corrValue
		}
	}
	return kline
}

// ApplyCorrectionsToKlines 应用修复数据到K线数组
func (c *Corrector) ApplyCorrectionsToKlines(klines []types.Kline) []types.Kline {
	if len(klines) == 0 {
		return klines
	}

	correctedKlines := make([]types.Kline, len(klines))
	correctionCount := 0

	for i, kline := range klines {
		correctedKline := kline // 复制原始数据

		// 获取该K线时间范围内的所有修复数据
		corrections := c.getCorrectionsForKline(kline)

		// 应用修复数据
		if len(corrections) > 0 {
			correctedKline = c.applyFieldCorrections(correctedKline, corrections)
			correctionCount += len(corrections)
		}

		correctedKlines[i] = correctedKline
	}

	if correctionCount > 0 {
		c.logger.Debugf("Applied %d corrections to %d klines", correctionCount, len(klines))
	}

	return correctedKlines
}

// ApplySingleCorrection 应用单个K线的修复数据
func (c *Corrector) ApplySingleCorrection(kline *types.Kline) *types.Kline {
	if kline == nil {
		return kline
	}

	// 创建修复后的K线副本
	correctedKline := *kline

	// 获取该K线时间范围内的所有修复数据
	corrections := c.getCorrectionsForKline(correctedKline)

	// 应用修复数据
	if len(corrections) > 0 {
		correctedKline = c.applyFieldCorrections(correctedKline, corrections)
		c.logger.Debugf("Applied %d corrections to kline %s at %d",
			len(corrections), kline.Code, kline.Timestamp)
	}

	return &correctedKline
}

// updateMetrics 更新性能指标
func (c *Corrector) updateMetrics(latencyNs int64) {
	c.metrics.TotalCorrections++
	c.metrics.TotalLatencyNs += latencyNs
	if latencyNs > c.metrics.MaxLatencyNs {
		c.metrics.MaxLatencyNs = latencyNs
	}
}

// GetMetrics 获取性能指标
func (c *Corrector) GetMetrics() CorrectionMetrics {
	return *c.metrics
}

// ResetMetrics 重置性能指标
func (c *Corrector) ResetMetrics() {
	c.metrics = &CorrectionMetrics{}
}

// GetAverageLatencyNs 获取平均延迟（纳秒）
func (c *Corrector) GetAverageLatencyNs() int64 {
	if c.metrics.TotalCorrections == 0 {
		return 0
	}
	return c.metrics.TotalLatencyNs / c.metrics.TotalCorrections
}

// HasCorrections 检查是否有修复数据
func (c *Corrector) HasCorrections(code string, timestamp int64) bool {
	return c.cache.HasCorrections(code, timestamp)
}

// GetCorrectionStats 获取修复统计信息
func (c *Corrector) GetCorrectionStats() map[string]interface{} {
	return c.cache.GetStats()
}

// RefreshCache 刷新缓存
func (c *Corrector) RefreshCache() error {
	return c.cache.ForceRefresh()
}
