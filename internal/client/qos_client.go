package client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"

	"github.com/gorilla/websocket"

	"qos-market-api/internal/config"
	"qos-market-api/internal/logger"
	"qos-market-api/pkg/types"
)

// HTTPStatsRecorder HTTP统计记录器接口
type HTTPStatsRecorder interface {
	RecordQOSHTTPCall()
}

// QOSClient QOS.HK API客户端
type QOSClient struct {
	config     *config.QOSConfig
	httpClient *http.Client
	logger     logger.Logger

	// 统计记录器
	statsRecorder HTTPStatsRecorder

	// WebSocket相关
	wsConn          *websocket.Conn
	wsMutex         sync.RWMutex
	wsConnected     bool
	wsReconnectChan chan struct{}
	wsCloseChan     chan struct{}

	// 订阅管理
	subscriptions map[string]bool
	subMutex      sync.RWMutex

	// 消息处理
	messageHandlers map[string]func([]byte)
	handlerMutex    sync.RWMutex
}

// NewQOSClient 创建新的QOS客户端
func NewQOSClient(cfg *config.QOSConfig, log logger.Logger) *QOSClient {
	return &QOSClient{
		config: cfg,
		httpClient: &http.Client{
			Timeout: cfg.RequestTimeout,
		},
		logger:          log,
		subscriptions:   make(map[string]bool),
		messageHandlers: make(map[string]func([]byte)),
		wsReconnectChan: make(chan struct{}, 1),
		wsCloseChan:     make(chan struct{}),
	}
}

// Start 启动客户端
func (c *QOSClient) Start(ctx context.Context) error {
	c.logger.Info("Starting QOS client...")

	// 启动WebSocket连接
	go c.wsConnectionManager(ctx)

	return nil
}

// Stop 停止客户端
func (c *QOSClient) Stop() error {
	c.logger.Info("Stopping QOS client...")

	close(c.wsCloseChan)

	c.wsMutex.Lock()
	if c.wsConn != nil {
		c.wsConn.Close()
		c.wsConn = nil
		c.wsConnected = false
	}
	c.wsMutex.Unlock()

	return nil
}

// IsConnected 检查WebSocket是否连接
func (c *QOSClient) IsConnected() bool {
	c.wsMutex.RLock()
	defer c.wsMutex.RUnlock()
	return c.wsConnected
}

// SetHTTPStatsRecorder 设置HTTP统计记录器
func (c *QOSClient) SetHTTPStatsRecorder(recorder HTTPStatsRecorder) {
	c.statsRecorder = recorder
}

// httpRequest 发送HTTP请求
func (c *QOSClient) httpRequest(endpoint string, payload interface{}) ([]byte, error) {
	// 记录HTTP调用统计
	if c.statsRecorder != nil {
		c.statsRecorder.RecordQOSHTTPCall()
	}

	var body []byte
	var err error

	if payload != nil {
		body, err = json.Marshal(payload)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal payload: %w", err)
		}
	}

	url := c.config.HTTPBaseURL + endpoint

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(body))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 添加API密钥
	req.Header.Set("key", c.config.APIKey)
	req.Header.Set("Content-Type", "application/json")

	// 重试机制
	var resp *http.Response
	for i := 0; i <= c.config.MaxRetries; i++ {
		resp, err = c.httpClient.Do(req)
		if err == nil {
			break
		}

		if i < c.config.MaxRetries {
			c.logger.Warnf("HTTP request failed (attempt %d/%d): %v", i+1, c.config.MaxRetries+1, err)
			time.Sleep(c.config.RetryDelay)
		}
	}

	if err != nil {
		return nil, fmt.Errorf("HTTP request failed after %d retries: %w", c.config.MaxRetries+1, err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP request failed with status: %d", resp.StatusCode)
	}

	// 使用io.ReadAll来正确读取响应体
	result, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// 检查响应体是否为空
	if len(result) == 0 {
		return nil, fmt.Errorf("empty response body")
	}

	return result, nil
}

// GetInstrumentInfo 获取交易品种基础信息
func (c *QOSClient) GetInstrumentInfo(codes []string) ([]types.InstrumentInfo, error) {
	// 根据API文档，codes应该是字符串数组，每个元素包含逗号分隔的代码
	payload := map[string]interface{}{
		"codes": codes,
	}

	data, err := c.httpRequest("/instrument-info", payload)
	if err != nil {
		return nil, err
	}

	var response types.APIResponse
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if response.Message != "OK" {
		return nil, fmt.Errorf("API error: %s", response.Message)
	}

	var instruments []types.InstrumentInfo
	dataBytes, err := json.Marshal(response.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal data: %w", err)
	}

	if err := json.Unmarshal(dataBytes, &instruments); err != nil {
		return nil, fmt.Errorf("failed to unmarshal instruments: %w", err)
	}

	return instruments, nil
}

// GetSnapshot 获取实时行情快照
func (c *QOSClient) GetSnapshot(codes []string) ([]types.Snapshot, error) {
	// 根据API文档，codes应该是字符串数组，每个元素包含逗号分隔的代码
	payload := map[string]interface{}{
		"codes": codes,
	}

	data, err := c.httpRequest("/snapshot", payload)
	if err != nil {
		return nil, err
	}

	var response types.APIResponse
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if response.Message != "OK" {
		return nil, fmt.Errorf("API error: %s", response.Message)
	}

	var snapshots []types.Snapshot
	dataBytes, err := json.Marshal(response.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal data: %w", err)
	}

	if err := json.Unmarshal(dataBytes, &snapshots); err != nil {
		return nil, fmt.Errorf("failed to unmarshal snapshots: %w", err)
	}

	return snapshots, nil
}

// GetDepth 获取盘口深度
func (c *QOSClient) GetDepth(codes []string) ([]types.Depth, error) {
	// 根据API文档，codes应该是字符串数组，每个元素包含逗号分隔的代码
	payload := map[string]interface{}{
		"codes": codes,
	}

	data, err := c.httpRequest("/depth", payload)
	if err != nil {
		return nil, err
	}

	var response types.APIResponse
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if response.Message != "OK" {
		return nil, fmt.Errorf("API error: %s", response.Message)
	}

	var depths []types.Depth
	dataBytes, err := json.Marshal(response.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal data: %w", err)
	}

	if err := json.Unmarshal(dataBytes, &depths); err != nil {
		return nil, fmt.Errorf("failed to unmarshal depths: %w", err)
	}

	return depths, nil
}

// GetKline 获取K线数据
func (c *QOSClient) GetKline(requests []types.KlineRequest) ([]types.KlineData, error) {
	payload := map[string]interface{}{
		"kline_reqs": requests,
	}

	data, err := c.httpRequest("/kline", payload)
	if err != nil {
		return nil, err
	}

	// 检查响应数据是否为空
	if len(data) == 0 {
		return nil, fmt.Errorf("empty response data from /kline endpoint")
	}

	// 检查响应数据是否被截断（可能导致JSON解析错误）
	if !json.Valid(data) {
		c.logger.Errorf("Invalid JSON response from /kline endpoint (length: %d bytes)", len(data))
		return nil, fmt.Errorf("invalid JSON response from /kline endpoint")
	}

	// 记录响应数据长度用于调试（不输出具体数据值）
	c.logger.Debugf("Received response from /kline endpoint (length: %d bytes)", len(data))

	var response types.APIResponse
	if err := json.Unmarshal(data, &response); err != nil {
		// 检查是否是因为数据过大导致的截断
		if strings.Contains(err.Error(), "unexpected end of JSON input") {
			c.logger.Errorf("JSON parsing failed due to truncated response (length: %d), requests: %d symbols",
				len(data), len(requests))
			return nil, fmt.Errorf("response data truncated, try reducing batch size: %w", err)
		}
		c.logger.Errorf("Failed to unmarshal API response (length: %d bytes): %v", len(data), err)
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if response.Message != "OK" {
		return nil, fmt.Errorf("API error: %s", response.Message)
	}

	// 检查响应数据是否为nil
	if response.Data == nil {
		c.logger.Warnf("API response data is nil for /kline endpoint")
		return []types.KlineData{}, nil // 返回空数组而不是错误
	}

	var klineData []types.KlineData
	dataBytes, err := json.Marshal(response.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal data: %w", err)
	}

	if err := json.Unmarshal(dataBytes, &klineData); err != nil {
		c.logger.Errorf("Failed to unmarshal kline data, data bytes: %s", string(dataBytes))
		return nil, fmt.Errorf("failed to unmarshal kline data: %w", err)
	}

	return klineData, nil
}

// GetHistoryKline 获取历史K线数据
func (c *QOSClient) GetHistoryKline(requests []types.KlineRequest) ([]types.KlineData, error) {
	payload := map[string]interface{}{
		"kline_reqs": requests,
	}

	data, err := c.httpRequest("/history", payload)
	if err != nil {
		return nil, err
	}

	// 检查响应数据是否为空
	if len(data) == 0 {
		return nil, fmt.Errorf("empty response data from /history endpoint")
	}

	// 记录响应数据长度用于调试（不输出具体数据值）
	c.logger.Debugf("Received response from /history endpoint (length: %d bytes)", len(data))

	var response types.APIResponse
	if err := json.Unmarshal(data, &response); err != nil {
		c.logger.Errorf("Failed to unmarshal API response (length: %d bytes): %v", len(data), err)
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if response.Message != "OK" {
		return nil, fmt.Errorf("API error: %s", response.Message)
	}

	// 检查响应数据是否为nil
	if response.Data == nil {
		c.logger.Warnf("API response data is nil for /history endpoint")
		return []types.KlineData{}, nil // 返回空数组而不是错误
	}

	var klineData []types.KlineData
	dataBytes, err := json.Marshal(response.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal data: %w", err)
	}

	if err := json.Unmarshal(dataBytes, &klineData); err != nil {
		c.logger.Errorf("Failed to unmarshal kline data, data bytes: %s", string(dataBytes))
		return nil, fmt.Errorf("failed to unmarshal kline data: %w", err)
	}

	return klineData, nil
}

// wsConnectionManager WebSocket连接管理器
func (c *QOSClient) wsConnectionManager(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		case <-c.wsCloseChan:
			return
		default:
			if err := c.connectWebSocket(); err != nil {
				c.logger.Errorf("Failed to connect WebSocket: %v", err)
				time.Sleep(c.config.RetryDelay)
				continue
			}

			// 连接成功后，启动消息处理
			c.handleWebSocketMessages(ctx)

			// 连接断开后等待重连
			select {
			case <-c.wsReconnectChan:
				c.logger.Info("Reconnecting WebSocket...")
			case <-time.After(c.config.RetryDelay):
				c.logger.Info("Reconnecting WebSocket after delay...")
			case <-ctx.Done():
				return
			case <-c.wsCloseChan:
				return
			}
		}
	}
}

// connectWebSocket 连接WebSocket
func (c *QOSClient) connectWebSocket() error {
	u, err := url.Parse(c.config.WebSocketURL)
	if err != nil {
		return fmt.Errorf("invalid WebSocket URL: %w", err)
	}

	// 添加API密钥参数
	q := u.Query()
	q.Set("key", c.config.APIKey)
	u.RawQuery = q.Encode()

	dialer := websocket.Dialer{
		HandshakeTimeout: c.config.RequestTimeout,
	}

	conn, _, err := dialer.Dial(u.String(), nil)
	if err != nil {
		return fmt.Errorf("failed to dial WebSocket: %w", err)
	}

	c.wsMutex.Lock()
	c.wsConn = conn
	c.wsConnected = true
	c.wsMutex.Unlock()

	c.logger.Info("WebSocket connected successfully")

	// 重新订阅之前的订阅
	c.resubscribe()

	return nil
}

// handleWebSocketMessages 处理WebSocket消息
func (c *QOSClient) handleWebSocketMessages(ctx context.Context) {
	defer func() {
		c.wsMutex.Lock()
		if c.wsConn != nil {
			c.wsConn.Close()
			c.wsConn = nil
		}
		c.wsConnected = false
		c.wsMutex.Unlock()

		// 触发重连
		select {
		case c.wsReconnectChan <- struct{}{}:
		default:
		}
	}()

	// 启动心跳
	go c.heartbeat(ctx)

	for {
		select {
		case <-ctx.Done():
			return
		case <-c.wsCloseChan:
			return
		default:
			c.wsMutex.RLock()
			conn := c.wsConn
			c.wsMutex.RUnlock()

			if conn == nil {
				return
			}

			_, message, err := conn.ReadMessage()
			if err != nil {
				c.logger.Errorf("WebSocket read error: %v", err)
				return
			}

			// 处理消息
			go c.processMessage(message)
		}
	}
}

// heartbeat 心跳机制
func (c *QOSClient) heartbeat(ctx context.Context) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-c.wsCloseChan:
			return
		case <-ticker.C:
			if err := c.sendHeartbeat(); err != nil {
				c.logger.Errorf("Failed to send heartbeat: %v", err)
				return
			}
		}
	}
}

// sendHeartbeat 发送心跳
func (c *QOSClient) sendHeartbeat() error {
	heartbeat := map[string]interface{}{
		"type": "PING",
	}

	return c.sendWebSocketMessage(heartbeat)
}

// sendWebSocketMessage 发送WebSocket消息
func (c *QOSClient) sendWebSocketMessage(message interface{}) error {
	c.wsMutex.RLock()
	conn := c.wsConn
	connected := c.wsConnected
	c.wsMutex.RUnlock()

	if !connected || conn == nil {
		return fmt.Errorf("WebSocket not connected")
	}

	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	if err := conn.WriteMessage(websocket.TextMessage, data); err != nil {
		return fmt.Errorf("failed to write message: %w", err)
	}

	return nil
}

// processMessage 处理接收到的消息
func (c *QOSClient) processMessage(data []byte) {
	var msg types.WebSocketMessage
	if err := json.Unmarshal(data, &msg); err != nil {
		c.logger.Errorf("Failed to unmarshal WebSocket message: %v", err)
		return
	}

	// 根据消息类型调用相应的处理器
	c.handlerMutex.RLock()
	handler, exists := c.messageHandlers[msg.Type]
	c.handlerMutex.RUnlock()

	if exists {
		handler(data)
	} else {
		c.logger.Debugf("No handler for message type: %s", msg.Type)
	}
}

// RegisterMessageHandler 注册消息处理器
func (c *QOSClient) RegisterMessageHandler(msgType string, handler func([]byte)) {
	c.handlerMutex.Lock()
	c.messageHandlers[msgType] = handler
	c.handlerMutex.Unlock()
}

// SubscribeSnapshot 订阅实时快照
func (c *QOSClient) SubscribeSnapshot(codes []string) error {
	subscription := types.SubscriptionRequest{
		Type:  "SS",
		Codes: codes,
	}

	if err := c.sendWebSocketMessage(subscription); err != nil {
		return err
	}

	// 记录订阅
	c.subMutex.Lock()
	for _, code := range codes {
		c.subscriptions["SS:"+code] = true
	}
	c.subMutex.Unlock()

	return nil
}

// SubscribeDepth 订阅盘口深度
func (c *QOSClient) SubscribeDepth(codes []string) error {
	subscription := types.SubscriptionRequest{
		Type:  "SD",
		Codes: codes,
	}

	if err := c.sendWebSocketMessage(subscription); err != nil {
		return err
	}

	// 记录订阅
	c.subMutex.Lock()
	for _, code := range codes {
		c.subscriptions["SD:"+code] = true
	}
	c.subMutex.Unlock()

	return nil
}

// SubscribeKline 订阅K线数据
func (c *QOSClient) SubscribeKline(codes []string, klineType types.KlineType) error {
	subscription := map[string]interface{}{
		"type":  "SK",
		"codes": codes,
		"kt":    int(klineType),
	}

	if err := c.sendWebSocketMessage(subscription); err != nil {
		return err
	}

	// 记录订阅
	c.subMutex.Lock()
	for _, code := range codes {
		c.subscriptions[fmt.Sprintf("SK:%s:%d", code, int(klineType))] = true
	}
	c.subMutex.Unlock()

	return nil
}

// resubscribe 重新订阅
func (c *QOSClient) resubscribe() {
	c.subMutex.RLock()
	subscriptions := make(map[string]bool)
	for k, v := range c.subscriptions {
		subscriptions[k] = v
	}
	c.subMutex.RUnlock()

	for subKey := range subscriptions {
		// 解析订阅键并重新订阅
		// 这里简化处理，实际应用中可能需要更复杂的逻辑
		c.logger.Debugf("Resubscribing: %s", subKey)
	}
}

// RequestSnapshot 请求实时快照
func (c *QOSClient) RequestSnapshot(codes []string, reqID int) error {
	request := types.SubscriptionRequest{
		Type:  "RS",
		Codes: codes,
		ReqID: reqID,
	}

	return c.sendWebSocketMessage(request)
}

// RequestKline 请求K线数据
func (c *QOSClient) RequestKline(requests []types.KlineRequest, reqID int) error {
	request := types.KlineRequestMessage{
		Type:      "RK",
		KlineReqs: requests,
		ReqID:     reqID,
	}

	return c.sendWebSocketMessage(request)
}
