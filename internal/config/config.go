package config

import (
	"fmt"
	"os"
	"time"

	"gopkg.in/yaml.v3"
)

// Config 应用程序配置结构
type Config struct {
	Server     ServerConfig     `yaml:"server"`
	QOS        QOSConfig        `yaml:"qos"`
	Cache      CacheConfig      `yaml:"cache"`
	Logging    LoggingConfig    `yaml:"logging"`
	Monitoring MonitoringConfig `yaml:"monitoring"`
	Symbols    SymbolsConfig    `yaml:"symbols"`
	WebSocket  WebSocketConfig  `yaml:"websocket"`
	RateLimit  RateLimitConfig  `yaml:"rate_limit"`
	Recovery   RecoveryConfig   `yaml:"recovery"`
	Correction CorrectionConfig `yaml:"correction"`
	Kline      KlineConfig      `yaml:"kline"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Host         string        `yaml:"host"`
	Port         int           `yaml:"port"`
	ReadTimeout  time.Duration `yaml:"read_timeout"`
	WriteTimeout time.Duration `yaml:"write_timeout"`
	IdleTimeout  time.Duration `yaml:"idle_timeout"`
}

// QOSConfig QOS.HK API配置
type QOSConfig struct {
	APIKey         string        `yaml:"api_key"`
	HTTPBaseURL    string        `yaml:"http_base_url"`
	WebSocketURL   string        `yaml:"websocket_url"`
	RequestTimeout time.Duration `yaml:"request_timeout"`
	MaxRetries     int           `yaml:"max_retries"`
	RetryDelay     time.Duration `yaml:"retry_delay"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	Memory MemoryCacheConfig `yaml:"memory"`
	TTL    CacheTTLConfig    `yaml:"ttl"`
}

// MemoryCacheConfig 内存缓存配置
type MemoryCacheConfig struct {
	MaxSize         int           `yaml:"max_size"`
	TTL             time.Duration `yaml:"ttl"`
	CleanupInterval time.Duration `yaml:"cleanup_interval"`
}

// CacheTTLConfig 不同数据类型的缓存TTL配置
type CacheTTLConfig struct {
	Snapshot time.Duration `yaml:"snapshot"`
	Kline1m  time.Duration `yaml:"kline_1m"`
	Kline5m  time.Duration `yaml:"kline_5m"`
	Kline15m time.Duration `yaml:"kline_15m"`
	Kline30m time.Duration `yaml:"kline_30m"`
	Kline1h  time.Duration `yaml:"kline_1h"`
	Kline2h  time.Duration `yaml:"kline_2h"`
	Kline4h  time.Duration `yaml:"kline_4h"`
	Kline1d  time.Duration `yaml:"kline_1d"`
	Kline1w  time.Duration `yaml:"kline_1w"`
	Kline1M  time.Duration `yaml:"kline_1M"`
	Kline1y  time.Duration `yaml:"kline_1y"`
	Depth    time.Duration `yaml:"depth"`
	Trades   time.Duration `yaml:"trades"`
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Level  string        `yaml:"level"`
	Format string        `yaml:"format"`
	Output string        `yaml:"output"`
	File   LogFileConfig `yaml:"file"`
}

// LogFileConfig 日志文件配置
type LogFileConfig struct {
	Path       string `yaml:"path"`
	MaxSize    int    `yaml:"max_size"`
	MaxBackups int    `yaml:"max_backups"`
	MaxAge     int    `yaml:"max_age"`
	Compress   bool   `yaml:"compress"`
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	Enabled             bool          `yaml:"enabled"`
	MetricsPort         int           `yaml:"metrics_port"`
	HealthCheckInterval time.Duration `yaml:"health_check_interval"`
}

// SymbolsConfig 交易品种配置
type SymbolsConfig struct {
	USStocks    []string      `yaml:"us_stocks"`
	HKStocks    []string      `yaml:"hk_stocks"`
	AStocks     AStocksConfig `yaml:"a_stocks"`
	Crypto      []string      `yaml:"crypto"`
	Forex       []string      `yaml:"forex"`
	Commodities []string      `yaml:"commodities"`
}

// AStocksConfig A股配置
type AStocksConfig struct {
	SH []string `yaml:"sh"`
	SZ []string `yaml:"sz"`
}

// WebSocketConfig WebSocket配置
type WebSocketConfig struct {
	MaxConnections  int           `yaml:"max_connections"`
	ReadBufferSize  int           `yaml:"read_buffer_size"`
	WriteBufferSize int           `yaml:"write_buffer_size"`
	PingPeriod      time.Duration `yaml:"ping_period"`
	PongWait        time.Duration `yaml:"pong_wait"`
	WriteWait       time.Duration `yaml:"write_wait"`
	MaxMessageSize  int64         `yaml:"max_message_size"`
}

// RateLimitConfig 限流配置
type RateLimitConfig struct {
	Enabled           bool `yaml:"enabled"`
	RequestsPerMinute int  `yaml:"requests_per_minute"`
	Burst             int  `yaml:"burst"`
}

// RecoveryConfig 恢复配置
type RecoveryConfig struct {
	MaxReconnectAttempts int           `yaml:"max_reconnect_attempts"`
	ReconnectDelay       time.Duration `yaml:"reconnect_delay"`
	HealthCheckInterval  time.Duration `yaml:"health_check_interval"`
	RestartOnPanic       bool          `yaml:"restart_on_panic"`
}

// CorrectionConfig K线修复配置
type CorrectionConfig struct {
	Enabled         bool          `yaml:"enabled"`
	DatabasePath    string        `yaml:"database_path"`
	RefreshInterval time.Duration `yaml:"refresh_interval"`
}

// KlineConfig K线数据配置
type KlineConfig struct {
	Recent  RecentKlineConfig  `yaml:"recent"`
	History HistoryKlineConfig `yaml:"history"`
}

// RecentKlineConfig 最近K线配置
type RecentKlineConfig struct {
	UpdateInterval time.Duration `yaml:"update_interval"` // 后台更新频率
	MaxPeriods     int           `yaml:"max_periods"`     // 保留的最大周期数
	Enabled        bool          `yaml:"enabled"`         // 是否启用最近K线缓存
	SupportedTypes []string      `yaml:"supported_types"` // 支持的K线类型列表

	// 延迟优化配置
	InitialLoadCount       int           `yaml:"initial_load_count"`       // 初始化时加载的K线条数，默认1000
	InitialLoadBatchSize   int           `yaml:"initial_load_batch_size"`  // 初始化时批处理大小，默认5
	RefreshCount           int           `yaml:"refresh_count"`            // 实时刷新时获取的K线条数，默认10
	IntegrityCheckInterval time.Duration `yaml:"integrity_check_interval"` // 完整性检查间隔，默认5分钟
	MaxDataAge             time.Duration `yaml:"max_data_age"`             // 数据最大存活时间，超过则重新加载，默认30分钟
}

// HistoryKlineConfig 历史K线配置
type HistoryKlineConfig struct {
	CacheTTL time.Duration `yaml:"cache_ttl"` // 缓存有效期
	Enabled  bool          `yaml:"enabled"`   // 是否启用历史K线缓存
}

// Load 从文件加载配置
func Load(configPath string) (*Config, error) {
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// 验证配置
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("config validation failed: %w", err)
	}

	return &config, nil
}

// Validate 验证配置
func (c *Config) Validate() error {
	if c.QOS.APIKey == "" || c.QOS.APIKey == "your_api_key_here" {
		return fmt.Errorf("QOS API key is required")
	}

	if c.Server.Port <= 0 || c.Server.Port > 65535 {
		return fmt.Errorf("invalid server port: %d", c.Server.Port)
	}

	if c.QOS.HTTPBaseURL == "" {
		return fmt.Errorf("QOS HTTP base URL is required")
	}

	if c.QOS.WebSocketURL == "" {
		return fmt.Errorf("QOS WebSocket URL is required")
	}

	return nil
}

// GetAddress 获取服务器监听地址
func (c *Config) GetAddress() string {
	return fmt.Sprintf("%s:%d", c.Server.Host, c.Server.Port)
}

// GetMetricsAddress 获取监控指标服务地址
func (c *Config) GetMetricsAddress() string {
	return fmt.Sprintf("%s:%d", c.Server.Host, c.Monitoring.MetricsPort)
}

// GetAllSymbols 获取所有配置的交易品种
func (c *Config) GetAllSymbols() map[string][]string {
	symbols := make(map[string][]string)

	// 美股
	for _, symbol := range c.Symbols.USStocks {
		symbols["US:"+symbol] = append(symbols["US:"+symbol], symbol)
	}

	// 港股
	for _, symbol := range c.Symbols.HKStocks {
		symbols["HK:"+symbol] = append(symbols["HK:"+symbol], symbol)
	}

	// A股上海
	for _, symbol := range c.Symbols.AStocks.SH {
		symbols["SH:"+symbol] = append(symbols["SH:"+symbol], symbol)
	}

	// A股深圳
	for _, symbol := range c.Symbols.AStocks.SZ {
		symbols["SZ:"+symbol] = append(symbols["SZ:"+symbol], symbol)
	}

	// 数字货币
	for _, symbol := range c.Symbols.Crypto {
		symbols["CF:"+symbol] = append(symbols["CF:"+symbol], symbol)
	}

	// 外汇
	for _, symbol := range c.Symbols.Forex {
		symbols["FX:"+symbol] = append(symbols["FX:"+symbol], symbol)
	}

	// 商品
	for _, symbol := range c.Symbols.Commodities {
		symbols["CM:"+symbol] = append(symbols["CM:"+symbol], symbol)
	}

	return symbols
}

// GetSupportedSymbols 获取支持的品种列表
func (c *Config) GetSupportedSymbols() []string {
	var symbols []string

	// 美股
	for _, symbol := range c.Symbols.USStocks {
		symbols = append(symbols, "US:"+symbol)
	}

	// 港股
	for _, symbol := range c.Symbols.HKStocks {
		symbols = append(symbols, "HK:"+symbol)
	}

	// A股上海
	for _, symbol := range c.Symbols.AStocks.SH {
		symbols = append(symbols, "SH:"+symbol)
	}

	// A股深圳
	for _, symbol := range c.Symbols.AStocks.SZ {
		symbols = append(symbols, "SZ:"+symbol)
	}

	// 数字货币
	for _, symbol := range c.Symbols.Crypto {
		symbols = append(symbols, "CF:"+symbol)
	}

	// 外汇
	for _, symbol := range c.Symbols.Forex {
		symbols = append(symbols, "FX:"+symbol)
	}

	// 商品
	for _, symbol := range c.Symbols.Commodities {
		symbols = append(symbols, "CM:"+symbol)
	}

	return symbols
}

// IsSymbolSupported 检查品种是否被支持
func (c *Config) IsSymbolSupported(symbol string) bool {
	supportedSymbols := c.GetSupportedSymbols()
	for _, supported := range supportedSymbols {
		if supported == symbol {
			return true
		}
	}
	return false
}
