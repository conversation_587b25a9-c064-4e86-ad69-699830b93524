package cache

import (
	"sync"
	"time"

	"qos-market-api/internal/config"
	"qos-market-api/internal/logger"
)

// CacheItem 缓存项
type CacheItem struct {
	Data      interface{} `json:"data"`
	ExpiresAt time.Time   `json:"expires_at"`
}

// IsExpired 检查是否过期
func (item *CacheItem) IsExpired() bool {
	return time.Now().After(item.ExpiresAt)
}

// Cache 缓存接口
type Cache interface {
	Set(key string, value interface{}, ttl time.Duration) error
	Get(key string) (interface{}, bool)
	Delete(key string) error
	Clear() error
	Size() int
	Keys() []string
}

// MemoryCache 内存缓存实现
type MemoryCache struct {
	items  map[string]*CacheItem
	mutex  sync.RWMutex
	config *config.MemoryCacheConfig
	logger logger.Logger
	stopCh chan struct{}
}

// NewMemoryCache 创建新的内存缓存
func NewMemoryCache(cfg *config.MemoryCacheConfig, log logger.Logger) *MemoryCache {
	cache := &MemoryCache{
		items:  make(map[string]*CacheItem),
		config: cfg,
		logger: log,
		stopCh: make(chan struct{}),
	}

	// 启动清理协程
	go cache.cleanup()

	return cache
}

// Set 设置缓存项
func (c *MemoryCache) Set(key string, value interface{}, ttl time.Duration) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 检查缓存大小限制
	if len(c.items) >= c.config.MaxSize {
		// 删除最旧的项
		c.evictOldest()
	}

	expiresAt := time.Now().Add(ttl)
	c.items[key] = &CacheItem{
		Data:      value,
		ExpiresAt: expiresAt,
	}

	c.logger.Debugf("Cache set: key=%s, ttl=%v", key, ttl)
	return nil
}

// Get 获取缓存项
func (c *MemoryCache) Get(key string) (interface{}, bool) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	item, exists := c.items[key]
	if !exists {
		return nil, false
	}

	if item.IsExpired() {
		// 异步删除过期项
		go func() {
			c.mutex.Lock()
			delete(c.items, key)
			c.mutex.Unlock()
		}()
		return nil, false
	}

	c.logger.Debugf("Cache hit: key=%s", key)
	return item.Data, true
}

// Delete 删除缓存项
func (c *MemoryCache) Delete(key string) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	delete(c.items, key)
	c.logger.Debugf("Cache delete: key=%s", key)
	return nil
}

// Clear 清空缓存
func (c *MemoryCache) Clear() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.items = make(map[string]*CacheItem)
	c.logger.Info("Cache cleared")
	return nil
}

// Size 获取缓存大小
func (c *MemoryCache) Size() int {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	return len(c.items)
}

// Keys 获取所有键
func (c *MemoryCache) Keys() []string {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	keys := make([]string, 0, len(c.items))
	for key := range c.items {
		keys = append(keys, key)
	}

	return keys
}

// Stop 停止缓存
func (c *MemoryCache) Stop() {
	close(c.stopCh)
}

// cleanup 清理过期项
func (c *MemoryCache) cleanup() {
	ticker := time.NewTicker(c.config.CleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			c.cleanupExpired()
		case <-c.stopCh:
			return
		}
	}
}

// cleanupExpired 清理过期项
func (c *MemoryCache) cleanupExpired() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	now := time.Now()
	expiredKeys := make([]string, 0)

	for key, item := range c.items {
		if now.After(item.ExpiresAt) {
			expiredKeys = append(expiredKeys, key)
		}
	}

	for _, key := range expiredKeys {
		delete(c.items, key)
	}

	if len(expiredKeys) > 0 {
		c.logger.Infof("Cleaned up %d expired cache items", len(expiredKeys))
	}
}

// evictOldest 删除最旧的项
func (c *MemoryCache) evictOldest() {
	var oldestKey string
	var oldestTime time.Time

	for key, item := range c.items {
		if oldestKey == "" || item.ExpiresAt.Before(oldestTime) {
			oldestKey = key
			oldestTime = item.ExpiresAt
		}
	}

	if oldestKey != "" {
		delete(c.items, oldestKey)
		c.logger.Debugf("Evicted oldest cache item: key=%s", oldestKey)
	}
}

// CacheManager 缓存管理器
type CacheManager struct {
	cache       Cache
	config      *config.CacheConfig
	klineConfig *config.KlineConfig
	logger      logger.Logger
	monitor     StatsRecorder // 统计记录器接口
}

// StatsRecorder 统计记录器接口
type StatsRecorder interface {
	RecordCacheHit()
	RecordCacheMiss()
}

// NewCacheManager 创建新的缓存管理器
func NewCacheManager(cfg *config.CacheConfig, klineConfig *config.KlineConfig, log logger.Logger) *CacheManager {
	var cache Cache

	// 目前只支持内存缓存，后续可以扩展Redis等
	cache = NewMemoryCache(&cfg.Memory, log)

	return &CacheManager{
		cache:       cache,
		config:      cfg,
		klineConfig: klineConfig,
		logger:      log,
	}
}

// GetTTL 根据数据类型获取TTL
func (cm *CacheManager) GetTTL(dataType string) time.Duration {
	switch dataType {
	case "snapshot":
		return cm.config.TTL.Snapshot
	case "kline_1m":
		return cm.config.TTL.Kline1m
	case "kline_5m":
		return cm.config.TTL.Kline5m
	case "kline_15m":
		return cm.config.TTL.Kline15m
	case "kline_30m":
		return cm.config.TTL.Kline30m
	case "kline_1h":
		return cm.config.TTL.Kline1h
	case "kline_2h":
		return cm.config.TTL.Kline2h
	case "kline_4h":
		return cm.config.TTL.Kline4h
	case "kline_1d":
		return cm.config.TTL.Kline1d
	case "kline_1w":
		return cm.config.TTL.Kline1w
	case "kline_1M":
		return cm.config.TTL.Kline1M
	case "kline_1y":
		return cm.config.TTL.Kline1y
	case "depth":
		return cm.config.TTL.Depth
	case "trades":
		return cm.config.TTL.Trades
	default:
		return cm.config.Memory.TTL
	}
}

// GetHistoryTTL 获取历史数据的TTL（用于历史K线长期缓存）
func (cm *CacheManager) GetHistoryTTL() time.Duration {
	if cm.klineConfig != nil && cm.klineConfig.History.Enabled {
		return cm.klineConfig.History.CacheTTL
	}
	// 默认24小时
	return 24 * time.Hour
}

// SetHistoryKline 设置历史K线缓存（使用长期TTL）
func (cm *CacheManager) SetHistoryKline(key string, value interface{}) error {
	ttl := cm.GetHistoryTTL()
	return cm.cache.Set(key, value, ttl)
}

// SetRecentKline 设置最近K线缓存（使用短期TTL）
func (cm *CacheManager) SetRecentKline(key string, value interface{}, klineType string) error {
	dataType := "kline_" + klineType
	ttl := cm.GetTTL(dataType)
	return cm.cache.Set(key, value, ttl)
}

// Set 设置缓存
func (cm *CacheManager) Set(key string, value interface{}, dataType string) error {
	ttl := cm.GetTTL(dataType)
	return cm.cache.Set(key, value, ttl)
}

// Get 获取缓存
func (cm *CacheManager) Get(key string) (interface{}, bool) {
	value, found := cm.cache.Get(key)

	// 记录统计信息
	if cm.monitor != nil {
		if found {
			cm.monitor.RecordCacheHit()
		} else {
			cm.monitor.RecordCacheMiss()
		}
	}

	return value, found
}

// Delete 删除缓存
func (cm *CacheManager) Delete(key string) error {
	return cm.cache.Delete(key)
}

// Clear 清空缓存
func (cm *CacheManager) Clear() error {
	return cm.cache.Clear()
}

// Size 获取缓存大小
func (cm *CacheManager) Size() int {
	return cm.cache.Size()
}

// Keys 获取所有键
func (cm *CacheManager) Keys() []string {
	return cm.cache.Keys()
}

// SetStatsRecorder 设置统计记录器
func (cm *CacheManager) SetStatsRecorder(recorder StatsRecorder) {
	cm.monitor = recorder
}

// Stop 停止缓存管理器
func (cm *CacheManager) Stop() {
	if mc, ok := cm.cache.(*MemoryCache); ok {
		mc.Stop()
	}
}
