package logger

import (
	"io"
	"os"
	"path/filepath"

	"github.com/sirupsen/logrus"
	"gopkg.in/natefinch/lumberjack.v2"

	"qos-market-api/internal/config"
)

// Logger 日志记录器接口
type Logger interface {
	Debug(args ...interface{})
	Debugf(format string, args ...interface{})
	Info(args ...interface{})
	Infof(format string, args ...interface{})
	Warn(args ...interface{})
	Warnf(format string, args ...interface{})
	Error(args ...interface{})
	Errorf(format string, args ...interface{})
	Fatal(args ...interface{})
	Fatalf(format string, args ...interface{})
	WithField(key string, value interface{}) Logger
	WithFields(fields map[string]interface{}) Logger
}

// logrusLogger logrus实现的日志记录器
type logrusLogger struct {
	logger *logrus.Logger
	entry  *logrus.Entry
}

// New 创建新的日志记录器
func New(cfg *config.LoggingConfig) (Logger, error) {
	logger := logrus.New()

	// 设置日志级别
	level, err := logrus.ParseLevel(cfg.Level)
	if err != nil {
		return nil, err
	}
	logger.SetLevel(level)

	// 设置日志格式
	switch cfg.Format {
	case "json":
		logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: "2006-01-02 15:04:05",
		})
	default:
		logger.SetFormatter(&logrus.TextFormatter{
			FullTimestamp:   true,
			TimestampFormat: "2006-01-02 15:04:05",
		})
	}

	// 设置输出
	var writers []io.Writer

	switch cfg.Output {
	case "file":
		fileWriter := getFileWriter(cfg.File)
		writers = append(writers, fileWriter)
	case "both":
		writers = append(writers, os.Stdout)
		fileWriter := getFileWriter(cfg.File)
		writers = append(writers, fileWriter)
	default: // stdout
		writers = append(writers, os.Stdout)
	}

	if len(writers) > 1 {
		logger.SetOutput(io.MultiWriter(writers...))
	} else {
		logger.SetOutput(writers[0])
	}

	return &logrusLogger{
		logger: logger,
		entry:  logrus.NewEntry(logger),
	}, nil
}

// getFileWriter 获取文件写入器
func getFileWriter(cfg config.LogFileConfig) io.Writer {
	// 确保日志目录存在
	if err := os.MkdirAll(filepath.Dir(cfg.Path), 0755); err != nil {
		logrus.Errorf("Failed to create log directory: %v", err)
		return os.Stdout
	}

	return &lumberjack.Logger{
		Filename:   cfg.Path,
		MaxSize:    cfg.MaxSize,
		MaxBackups: cfg.MaxBackups,
		MaxAge:     cfg.MaxAge,
		Compress:   cfg.Compress,
	}
}

// Debug 输出调试级别日志
func (l *logrusLogger) Debug(args ...interface{}) {
	l.entry.Debug(args...)
}

// Debugf 输出格式化调试级别日志
func (l *logrusLogger) Debugf(format string, args ...interface{}) {
	l.entry.Debugf(format, args...)
}

// Info 输出信息级别日志
func (l *logrusLogger) Info(args ...interface{}) {
	l.entry.Info(args...)
}

// Infof 输出格式化信息级别日志
func (l *logrusLogger) Infof(format string, args ...interface{}) {
	l.entry.Infof(format, args...)
}

// Warn 输出警告级别日志
func (l *logrusLogger) Warn(args ...interface{}) {
	l.entry.Warn(args...)
}

// Warnf 输出格式化警告级别日志
func (l *logrusLogger) Warnf(format string, args ...interface{}) {
	l.entry.Warnf(format, args...)
}

// Error 输出错误级别日志
func (l *logrusLogger) Error(args ...interface{}) {
	l.entry.Error(args...)
}

// Errorf 输出格式化错误级别日志
func (l *logrusLogger) Errorf(format string, args ...interface{}) {
	l.entry.Errorf(format, args...)
}

// Fatal 输出致命错误级别日志并退出程序
func (l *logrusLogger) Fatal(args ...interface{}) {
	l.entry.Fatal(args...)
}

// Fatalf 输出格式化致命错误级别日志并退出程序
func (l *logrusLogger) Fatalf(format string, args ...interface{}) {
	l.entry.Fatalf(format, args...)
}

// WithField 添加字段
func (l *logrusLogger) WithField(key string, value interface{}) Logger {
	return &logrusLogger{
		logger: l.logger,
		entry:  l.entry.WithField(key, value),
	}
}

// WithFields 添加多个字段
func (l *logrusLogger) WithFields(fields map[string]interface{}) Logger {
	return &logrusLogger{
		logger: l.logger,
		entry:  l.entry.WithFields(fields),
	}
}

// 全局日志记录器实例
var globalLogger Logger

// Init 初始化全局日志记录器
func Init(cfg *config.LoggingConfig) error {
	logger, err := New(cfg)
	if err != nil {
		return err
	}
	globalLogger = logger
	return nil
}

// GetLogger 获取全局日志记录器
func GetLogger() Logger {
	if globalLogger == nil {
		// 如果没有初始化，使用默认配置
		cfg := &config.LoggingConfig{
			Level:  "info",
			Format: "text",
			Output: "stdout",
		}
		logger, _ := New(cfg)
		globalLogger = logger
	}
	return globalLogger
}

// 便捷函数
func Debug(args ...interface{}) {
	GetLogger().Debug(args...)
}

func Debugf(format string, args ...interface{}) {
	GetLogger().Debugf(format, args...)
}

func Info(args ...interface{}) {
	GetLogger().Info(args...)
}

func Infof(format string, args ...interface{}) {
	GetLogger().Infof(format, args...)
}

func Warn(args ...interface{}) {
	GetLogger().Warn(args...)
}

func Warnf(format string, args ...interface{}) {
	GetLogger().Warnf(format, args...)
}

func Error(args ...interface{}) {
	GetLogger().Error(args...)
}

func Errorf(format string, args ...interface{}) {
	GetLogger().Errorf(format, args...)
}

func Fatal(args ...interface{}) {
	GetLogger().Fatal(args...)
}

func Fatalf(format string, args ...interface{}) {
	GetLogger().Fatalf(format, args...)
}

func WithField(key string, value interface{}) Logger {
	return GetLogger().WithField(key, value)
}

func WithFields(fields map[string]interface{}) Logger {
	return GetLogger().WithFields(fields)
}
