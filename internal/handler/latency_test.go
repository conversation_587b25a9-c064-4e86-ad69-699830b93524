package handler

import (
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"qos-market-api/internal/cache"
	"qos-market-api/internal/config"
	"qos-market-api/internal/logger"
	"qos-market-api/pkg/types"
)

// MockLatencyRecorder 模拟延迟记录器
type MockLatencyRecorder struct {
	QOSAPILatencies       []time.Duration
	CorrectionLatencies   []time.Duration
	TotalLatencies        []time.Duration
	RecentKlineLatencies  []time.Duration
	HistoryKlineLatencies []time.Duration
	CacheLatencies        []time.Duration
}

func (m *MockLatencyRecorder) RecordQOSAPILatency(latency time.Duration) {
	m.QOSAPILatencies = append(m.QOSAPILatencies, latency)
}

func (m *MockLatencyRecorder) RecordCorrectionLatency(latency time.Duration) {
	m.CorrectionLatencies = append(m.CorrectionLatencies, latency)
}

func (m *MockLatencyRecorder) RecordTotalLatency(latency time.Duration) {
	m.TotalLatencies = append(m.TotalLatencies, latency)
}

func (m *MockLatencyRecorder) RecordRecentKlineLatency(latency time.Duration) {
	m.RecentKlineLatencies = append(m.RecentKlineLatencies, latency)
}

func (m *MockLatencyRecorder) RecordHistoryKlineLatency(latency time.Duration) {
	m.HistoryKlineLatencies = append(m.HistoryKlineLatencies, latency)
}

func (m *MockLatencyRecorder) RecordCacheLatency(latency time.Duration) {
	m.CacheLatencies = append(m.CacheLatencies, latency)
}

// MockQOSClient 模拟QOS客户端
type MockQOSClient struct {
	delay time.Duration
}

func (m *MockQOSClient) GetSnapshot(codes []string) ([]types.Snapshot, error) {
	return nil, nil
}

func (m *MockQOSClient) GetDepth(codes []string) ([]types.Depth, error) {
	return nil, nil
}

func (m *MockQOSClient) GetInstrumentInfo(codes []string) ([]types.InstrumentInfo, error) {
	return nil, nil
}

func (m *MockQOSClient) GetKline(requests []types.KlineRequest) ([]types.KlineData, error) {
	// 模拟延迟
	time.Sleep(m.delay)

	// 返回模拟数据
	result := make([]types.KlineData, len(requests))
	for i, req := range requests {
		result[i] = types.KlineData{
			Code: req.Code,
			Klines: []types.Kline{
				{
					Code:       req.Code,
					OpenPrice:  "100.00",
					ClosePrice: "101.00",
					HighPrice:  "102.00",
					LowPrice:   "99.00",
					Volume:     "1000",
					Timestamp:  time.Now().Unix(),
					KlineType:  req.KlineType,
				},
			},
		}
	}
	return result, nil
}

func (m *MockQOSClient) GetHistoryKline(requests []types.KlineRequest) ([]types.KlineData, error) {
	// 模拟延迟
	time.Sleep(m.delay)

	// 返回模拟数据
	result := make([]types.KlineData, len(requests))
	for i, req := range requests {
		result[i] = types.KlineData{
			Code: req.Code,
			Klines: []types.Kline{
				{
					Code:       req.Code,
					OpenPrice:  "100.00",
					ClosePrice: "101.00",
					HighPrice:  "102.00",
					LowPrice:   "99.00",
					Volume:     "1000",
					Timestamp:  time.Now().Unix(),
					KlineType:  req.KlineType,
				},
			},
		}
	}
	return result, nil
}

func (m *MockQOSClient) IsConnected() bool {
	return true
}

// MockCorrector 模拟修复器
type MockCorrector struct {
	delay time.Duration
}

func (m *MockCorrector) ApplyCorrections(klineData *types.KlineData) *types.KlineData {
	// 模拟修复延迟
	time.Sleep(m.delay)
	return klineData
}

func TestHTTPHandlerLatencyTracking(t *testing.T) {
	// 创建测试配置
	cfg := &config.Config{
		QOS: config.QOSConfig{
			APIKey:         "test_key",
			HTTPBaseURL:    "https://qos.hk",
			WebSocketURL:   "wss://api.qos.hk/ws",
			RequestTimeout: 30 * time.Second,
		},
		Cache: config.CacheConfig{
			Memory: config.MemoryCacheConfig{
				MaxSize:         100,
				TTL:             300 * time.Second,
				CleanupInterval: 60 * time.Second,
			},
		},
		Logging: config.LoggingConfig{
			Level:  "info",
			Format: "text",
			Output: "stdout",
		},
		Symbols: config.SymbolsConfig{
			USStocks: []string{"AAPL", "TSLA"},
			HKStocks: []string{"700", "9988"},
			Crypto:   []string{"BTCUSDT", "ETHUSDT"},
		},
	}

	// 初始化日志
	log, err := logger.New(&cfg.Logging)
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	// 创建组件
	cacheManager := cache.NewCacheManager(&cfg.Cache, nil, log)
	defer cacheManager.Stop()

	// 创建模拟QOS客户端，设置100ms延迟
	mockQOSClient := &MockQOSClient{delay: 100 * time.Millisecond}

	// 创建HTTP处理器
	httpHandler := NewHTTPHandler(mockQOSClient, cacheManager, cfg, log)

	// 创建模拟修复器，设置50ms延迟
	mockCorrector := &MockCorrector{delay: 50 * time.Millisecond}
	httpHandler.SetCorrector(mockCorrector)

	// 创建模拟延迟记录器
	mockRecorder := &MockLatencyRecorder{}
	httpHandler.SetLatencyRecorder(mockRecorder)

	// 创建测试请求
	req := httptest.NewRequest("GET", "/api/v1/kline?codes=US:AAPL&kline_type=1001&count=1", nil)
	w := httptest.NewRecorder()

	// 执行请求
	httpHandler.GetKline(w, req)

	// 验证响应状态
	if w.Code != http.StatusOK {
		t.Errorf("Expected status 200, got %d", w.Code)
	}

	// 验证延迟记录
	if len(mockRecorder.QOSAPILatencies) != 1 {
		t.Errorf("Expected 1 QOS API latency record, got %d", len(mockRecorder.QOSAPILatencies))
	}

	if len(mockRecorder.CorrectionLatencies) != 1 {
		t.Errorf("Expected 1 correction latency record, got %d", len(mockRecorder.CorrectionLatencies))
	}

	if len(mockRecorder.TotalLatencies) != 1 {
		t.Errorf("Expected 1 total latency record, got %d", len(mockRecorder.TotalLatencies))
	}

	// 验证延迟值的合理性
	qosLatency := mockRecorder.QOSAPILatencies[0]
	if qosLatency < 90*time.Millisecond || qosLatency > 200*time.Millisecond {
		t.Errorf("QOS API latency %v is not in expected range [90ms, 200ms]", qosLatency)
	}

	correctionLatency := mockRecorder.CorrectionLatencies[0]
	if correctionLatency < 40*time.Millisecond || correctionLatency > 100*time.Millisecond {
		t.Errorf("Correction latency %v is not in expected range [40ms, 100ms]", correctionLatency)
	}

	totalLatency := mockRecorder.TotalLatencies[0]
	if totalLatency < qosLatency+correctionLatency {
		t.Errorf("Total latency %v should be at least the sum of QOS API (%v) and correction (%v) latencies",
			totalLatency, qosLatency, correctionLatency)
	}
}

func TestHTTPHandlerLatencyTrackingWithCache(t *testing.T) {
	// 创建测试配置
	cfg := &config.Config{
		Cache: config.CacheConfig{
			Memory: config.MemoryCacheConfig{
				MaxSize:         100,
				TTL:             300 * time.Second,
				CleanupInterval: 60 * time.Second,
			},
			TTL: config.CacheTTLConfig{
				Kline1d: 86400 * time.Second,
			},
		},
		Logging: config.LoggingConfig{
			Level:  "info",
			Format: "text",
			Output: "stdout",
		},
		Symbols: config.SymbolsConfig{
			USStocks: []string{"AAPL", "TSLA"},
			HKStocks: []string{"700", "9988"},
			Crypto:   []string{"BTCUSDT", "ETHUSDT"},
		},
	}

	// 初始化日志
	log, err := logger.New(&cfg.Logging)
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	// 创建组件
	cacheManager := cache.NewCacheManager(&cfg.Cache, nil, log)
	defer cacheManager.Stop()

	// 创建模拟QOS客户端
	mockQOSClient := &MockQOSClient{delay: 100 * time.Millisecond}

	// 创建HTTP处理器
	httpHandler := NewHTTPHandler(mockQOSClient, cacheManager, cfg, log)

	// 创建模拟修复器
	mockCorrector := &MockCorrector{delay: 50 * time.Millisecond}
	httpHandler.SetCorrector(mockCorrector)

	// 创建模拟延迟记录器
	mockRecorder := &MockLatencyRecorder{}
	httpHandler.SetLatencyRecorder(mockRecorder)

	// 预填充缓存 - 使用正确的缓存键格式
	cacheKey := types.GetCacheKey("kline", "US:AAPL", types.KlineDay.String(), "1")
	t.Logf("Setting cache key: %s", cacheKey)
	testData := types.KlineData{
		Code: "US:AAPL",
		Klines: []types.Kline{
			{
				Code:       "US:AAPL",
				OpenPrice:  "150.00",
				ClosePrice: "151.00",
				HighPrice:  "152.00",
				LowPrice:   "149.00",
				Volume:     "2000",
				Timestamp:  time.Now().Unix(),
				KlineType:  types.KlineDay,
			},
		},
	}
	cacheManager.Set(cacheKey, testData, "kline_1d")

	// 验证缓存是否设置成功
	if cached, found := cacheManager.Get(cacheKey); found {
		t.Logf("Cache verification successful: %v", cached)
	} else {
		t.Logf("Cache verification failed: key not found")
	}

	// 创建测试请求
	req := httptest.NewRequest("GET", "/api/v1/kline?codes=US:AAPL&kline_type=1001&count=1", nil)
	w := httptest.NewRecorder()

	// 执行请求
	httpHandler.GetKline(w, req)

	// 验证响应状态
	if w.Code != http.StatusOK {
		t.Errorf("Expected status 200, got %d", w.Code)
	}

	// 验证延迟记录 - 缓存命中时不应该有QOS API延迟
	if len(mockRecorder.QOSAPILatencies) != 0 {
		t.Errorf("Expected 0 QOS API latency records for cache hit, got %d", len(mockRecorder.QOSAPILatencies))
	}

	// 但应该有修复延迟（缓存的数据也需要应用修复）
	if len(mockRecorder.CorrectionLatencies) != 1 {
		t.Errorf("Expected 1 correction latency record, got %d", len(mockRecorder.CorrectionLatencies))
	}

	// 应该有总延迟
	if len(mockRecorder.TotalLatencies) != 1 {
		t.Errorf("Expected 1 total latency record, got %d", len(mockRecorder.TotalLatencies))
	}
}

func TestHTTPHandlerLatencyTrackingMultipleCodes(t *testing.T) {
	// 创建测试配置
	cfg := &config.Config{
		Cache: config.CacheConfig{
			Memory: config.MemoryCacheConfig{
				MaxSize:         100,
				TTL:             300 * time.Second,
				CleanupInterval: 60 * time.Second,
			},
		},
		Logging: config.LoggingConfig{
			Level:  "info",
			Format: "text",
			Output: "stdout",
		},
		Symbols: config.SymbolsConfig{
			USStocks: []string{"AAPL", "TSLA"},
			HKStocks: []string{"700", "9988"},
			Crypto:   []string{"BTCUSDT", "ETHUSDT"},
		},
	}

	// 初始化日志
	log, err := logger.New(&cfg.Logging)
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	// 创建组件
	cacheManager := cache.NewCacheManager(&cfg.Cache, nil, log)
	defer cacheManager.Stop()

	// 创建模拟QOS客户端
	mockQOSClient := &MockQOSClient{delay: 100 * time.Millisecond}

	// 创建HTTP处理器
	httpHandler := NewHTTPHandler(mockQOSClient, cacheManager, cfg, log)

	// 创建模拟修复器
	mockCorrector := &MockCorrector{delay: 50 * time.Millisecond}
	httpHandler.SetCorrector(mockCorrector)

	// 创建模拟延迟记录器
	mockRecorder := &MockLatencyRecorder{}
	httpHandler.SetLatencyRecorder(mockRecorder)

	// 创建测试请求 - 请求多个代码
	req := httptest.NewRequest("GET", "/api/v1/kline?codes=US:AAPL,US:TSLA&kline_type=1001&count=1", nil)
	w := httptest.NewRecorder()

	// 执行请求
	httpHandler.GetKline(w, req)

	// 验证响应状态
	if w.Code != http.StatusOK {
		t.Errorf("Expected status 200, got %d", w.Code)
	}

	// 验证延迟记录 - 应该有累积的延迟
	if len(mockRecorder.QOSAPILatencies) != 1 {
		t.Errorf("Expected 1 QOS API latency record, got %d", len(mockRecorder.QOSAPILatencies))
	}

	if len(mockRecorder.CorrectionLatencies) != 1 {
		t.Errorf("Expected 1 correction latency record, got %d", len(mockRecorder.CorrectionLatencies))
	}

	if len(mockRecorder.TotalLatencies) != 1 {
		t.Errorf("Expected 1 total latency record, got %d", len(mockRecorder.TotalLatencies))
	}

	// QOS API延迟应该大约是2倍的单个延迟（因为有2个代码）
	qosLatency := mockRecorder.QOSAPILatencies[0]
	if qosLatency < 180*time.Millisecond || qosLatency > 400*time.Millisecond {
		t.Errorf("QOS API latency %v is not in expected range for 2 codes [180ms, 400ms]", qosLatency)
	}

	// 修复延迟也应该是累积的
	correctionLatency := mockRecorder.CorrectionLatencies[0]
	if correctionLatency < 80*time.Millisecond || correctionLatency > 200*time.Millisecond {
		t.Errorf("Correction latency %v is not in expected range for 2 codes [80ms, 200ms]", correctionLatency)
	}
}
