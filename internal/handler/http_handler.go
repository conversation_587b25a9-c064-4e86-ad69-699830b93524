package handler

import (
	"encoding/json"
	"net/http"
	"strconv"
	"strings"
	"time"

	"qos-market-api/internal/cache"
	"qos-market-api/internal/config"
	"qos-market-api/internal/logger"
	"qos-market-api/pkg/types"
)

// QOSClient QOS客户端接口
type QOSClient interface {
	GetSnapshot(codes []string) ([]types.Snapshot, error)
	GetKline(requests []types.KlineRequest) ([]types.KlineData, error)
	GetHistoryKline(requests []types.KlineRequest) ([]types.KlineData, error)
	GetDepth(codes []string) ([]types.Depth, error)
	GetInstrumentInfo(codes []string) ([]types.InstrumentInfo, error)
	IsConnected() bool
}

// LatencyRecorder 延迟记录器接口
type LatencyRecorder interface {
	RecordQOSAPILatency(latency time.Duration)
	RecordCorrectionLatency(latency time.Duration)
	RecordTotalLatency(latency time.Duration)
	// 新增：区分不同数据源的延迟记录
	RecordRecentKlineLatency(latency time.Duration)
	RecordHistoryKlineLatency(latency time.Duration)
	RecordCacheLatency(latency time.Duration)
}

// HTTPHandler HTTP处理器
type HTTPHandler struct {
	qosClient          QOSClient
	cacheManager       *cache.CacheManager
	config             *config.Config
	logger             logger.Logger
	corrector          interface{}     // 修复器，暂时用interface{}避免循环导入
	latencyRecorder    LatencyRecorder // 延迟记录器
	recentKlineService interface{}     // 最近K线服务
}

// NewHTTPHandler 创建新的HTTP处理器
func NewHTTPHandler(qosClient QOSClient, cacheManager *cache.CacheManager, cfg *config.Config, log logger.Logger) *HTTPHandler {
	return &HTTPHandler{
		qosClient:       qosClient,
		cacheManager:    cacheManager,
		config:          cfg,
		logger:          log,
		corrector:       nil, // 稍后设置
		latencyRecorder: nil, // 稍后设置
	}
}

// SetCorrector 设置修复器
func (h *HTTPHandler) SetCorrector(corrector interface{}) {
	h.corrector = corrector
}

// SetLatencyRecorder 设置延迟记录器
func (h *HTTPHandler) SetLatencyRecorder(recorder LatencyRecorder) {
	h.latencyRecorder = recorder
}

// SetRecentKlineService 设置最近K线服务
func (h *HTTPHandler) SetRecentKlineService(service interface{}) {
	h.recentKlineService = service
}

// logUserRequest 记录用户请求详细信息
func (h *HTTPHandler) logUserRequest(endpoint, klineType string, codes []string, count int, endTime int64, resultCount int, status string, latency time.Duration) {
	fields := map[string]interface{}{
		"endpoint":     endpoint,
		"codes":        codes,
		"code_count":   len(codes),
		"result_count": resultCount,
		"status":       status,
		"latency_ms":   latency.Milliseconds(),
	}

	// 添加K线相关参数（如果适用）
	if klineType != "" {
		fields["kline_type"] = klineType
	}
	if count > 0 {
		fields["count"] = count
	}
	if endTime > 0 {
		fields["end_time"] = endTime
		fields["end_time_readable"] = time.Unix(endTime, 0).Format("2006-01-02 15:04:05")
	}

	// 记录请求日志
	h.logger.WithFields(fields).Infof("User request: %s - %s", endpoint, status)
}

// applyCorrectionToKlineData 应用修复数据到K线数据
func (h *HTTPHandler) applyCorrectionToKlineData(klineData *types.KlineData) *types.KlineData {
	if h.corrector == nil || klineData == nil {
		return klineData
	}

	// 使用类型断言调用修复器的ApplyCorrections方法
	if corrector, ok := h.corrector.(interface {
		ApplyCorrections(klineData *types.KlineData) *types.KlineData
	}); ok {
		return corrector.ApplyCorrections(klineData)
	}

	return klineData
}

// HealthCheck 健康检查
func (h *HTTPHandler) HealthCheck(w http.ResponseWriter, r *http.Request) {
	status := map[string]interface{}{
		"status":    "ok",
		"timestamp": time.Now().Unix(),
		"version":   "1.0.0",
		"services": map[string]interface{}{
			"qos_websocket": h.qosClient.IsConnected(),
			"cache":         h.cacheManager.Size(),
		},
		"supported_symbols": h.config.GetSupportedSymbols(),
	}

	h.writeJSONResponse(w, http.StatusOK, status)
}

// GetSnapshot 获取实时行情快照
func (h *HTTPHandler) GetSnapshot(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()

	codes := h.parseCodesFromQuery(r)
	if len(codes) == 0 {
		h.logUserRequest("snapshot", "", codes, 0, 0, 0, "ERROR: codes parameter is required", time.Since(startTime))
		h.writeErrorResponse(w, http.StatusBadRequest, "codes parameter is required")
		return
	}

	// 记录用户请求信息
	h.logUserRequest("snapshot", "", codes, 0, 0, 0, "REQUEST", 0)

	// 检查缓存
	var snapshots []types.Snapshot
	var missingCodes []string

	for _, code := range codes {
		cacheKey := types.GetCacheKey("snapshot", code)
		if cached, found := h.cacheManager.Get(cacheKey); found {
			if snapshot, ok := cached.(types.Snapshot); ok {
				snapshots = append(snapshots, snapshot)
			}
		} else {
			missingCodes = append(missingCodes, code)
		}
	}

	// 获取缺失的数据
	if len(missingCodes) > 0 {
		// 将代码按市场分组，符合API文档格式
		groupedCodes := h.groupCodesByMarket(missingCodes)
		freshSnapshots, err := h.qosClient.GetSnapshot(groupedCodes)
		if err != nil {
			h.logger.Errorf("Failed to get snapshots: %v", err)
			h.logUserRequest("snapshot", "", codes, 0, 0, len(snapshots), "ERROR: Failed to get market data", time.Since(startTime))
			h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to get market data")
			return
		}

		// 缓存新数据
		for _, snapshot := range freshSnapshots {
			cacheKey := types.GetCacheKey("snapshot", snapshot.Code)
			h.cacheManager.Set(cacheKey, snapshot, "snapshot")
		}

		snapshots = append(snapshots, freshSnapshots...)
	}

	totalLatency := time.Since(startTime)
	h.logUserRequest("snapshot", "", codes, 0, 0, len(snapshots), "SUCCESS", totalLatency)

	h.writeJSONResponse(w, http.StatusOK, map[string]interface{}{
		"msg":  "OK",
		"data": snapshots,
	})
}

// GetKline 获取K线数据（智能路由到最近K线或历史K线）
func (h *HTTPHandler) GetKline(w http.ResponseWriter, r *http.Request) {
	// 记录总延迟开始时间
	totalStartTime := time.Now()

	codes := h.parseCodesFromQuery(r)
	if len(codes) == 0 {
		h.logUserRequest("kline", "", codes, 0, 0, 0, "ERROR: codes parameter is required", time.Since(totalStartTime))
		h.writeErrorResponse(w, http.StatusBadRequest, "codes parameter is required")
		return
	}

	// 解析K线类型
	klineTypeStr := r.URL.Query().Get("kline_type")
	if klineTypeStr == "" {
		klineTypeStr = "1001" // 默认日线
	}

	klineTypeInt, err := strconv.Atoi(klineTypeStr)
	if err != nil {
		h.logUserRequest("kline", klineTypeStr, codes, 0, 0, 0, "ERROR: invalid kline_type parameter", time.Since(totalStartTime))
		h.writeErrorResponse(w, http.StatusBadRequest, "invalid kline_type parameter")
		return
	}
	klineType := types.KlineType(klineTypeInt)

	// 解析数量
	countStr := r.URL.Query().Get("count")
	count := 100 // 默认100根
	if countStr != "" {
		if c, err := strconv.Atoi(countStr); err == nil && c > 0 {
			count = c
		}
	}

	// 解析结束时间（用于判断是否为历史数据查询）
	endTimeStr := r.URL.Query().Get("end_time")
	var endTime int64
	if endTimeStr != "" {
		if et, err := strconv.ParseInt(endTimeStr, 10, 64); err == nil {
			endTime = et
		}
	}

	// 记录用户请求信息
	h.logUserRequest("kline", klineTypeStr, codes, count, endTime, 0, "REQUEST", 0)

	// 智能选择数据源：如果指定了结束时间或请求大量历史数据，使用历史K线接口
	useHistoryAPI := h.shouldUseHistoryAPI(count, endTime, klineType)

	if useHistoryAPI {
		h.getHistoryKline(w, r, codes, klineType, count, endTime, totalStartTime)
	} else {
		h.getRecentKline(w, r, codes, klineType, count, totalStartTime)
	}
}

// GetDepth 获取盘口深度
func (h *HTTPHandler) GetDepth(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()

	codes := h.parseCodesFromQuery(r)
	if len(codes) == 0 {
		h.logUserRequest("depth", "", codes, 0, 0, 0, "ERROR: codes parameter is required", time.Since(startTime))
		h.writeErrorResponse(w, http.StatusBadRequest, "codes parameter is required")
		return
	}

	// 记录用户请求信息
	h.logUserRequest("depth", "", codes, 0, 0, 0, "REQUEST", 0)

	// 检查缓存
	var depths []types.Depth
	var missingCodes []string

	for _, code := range codes {
		cacheKey := types.GetCacheKey("depth", code)
		if cached, found := h.cacheManager.Get(cacheKey); found {
			if depth, ok := cached.(types.Depth); ok {
				depths = append(depths, depth)
			}
		} else {
			missingCodes = append(missingCodes, code)
		}
	}

	// 获取缺失的数据
	if len(missingCodes) > 0 {
		// 将代码按市场分组，符合API文档格式
		groupedCodes := h.groupCodesByMarket(missingCodes)
		freshDepths, err := h.qosClient.GetDepth(groupedCodes)
		if err != nil {
			h.logger.Errorf("Failed to get depths: %v", err)
			h.logUserRequest("depth", "", codes, 0, 0, len(depths), "ERROR: Failed to get market data", time.Since(startTime))
			h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to get market data")
			return
		}

		// 缓存新数据
		for _, depth := range freshDepths {
			cacheKey := types.GetCacheKey("depth", depth.Code)
			h.cacheManager.Set(cacheKey, depth, "depth")
		}

		depths = append(depths, freshDepths...)
	}

	totalLatency := time.Since(startTime)
	h.logUserRequest("depth", "", codes, 0, 0, len(depths), "SUCCESS", totalLatency)

	h.writeJSONResponse(w, http.StatusOK, map[string]interface{}{
		"msg":  "OK",
		"data": depths,
	})
}

// GetInstrumentInfo 获取交易品种基础信息
func (h *HTTPHandler) GetInstrumentInfo(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()

	codes := h.parseCodesFromQuery(r)
	if len(codes) == 0 {
		h.logUserRequest("instrument", "", codes, 0, 0, 0, "ERROR: codes parameter is required", time.Since(startTime))
		h.writeErrorResponse(w, http.StatusBadRequest, "codes parameter is required")
		return
	}

	// 记录用户请求信息
	h.logUserRequest("instrument", "", codes, 0, 0, 0, "REQUEST", 0)

	// 将代码按市场分组，符合API文档格式
	groupedCodes := h.groupCodesByMarket(codes)
	instruments, err := h.qosClient.GetInstrumentInfo(groupedCodes)
	if err != nil {
		h.logger.Errorf("Failed to get instrument info: %v", err)
		h.logUserRequest("instrument", "", codes, 0, 0, 0, "ERROR: Failed to get instrument info", time.Since(startTime))
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to get instrument info")
		return
	}

	totalLatency := time.Since(startTime)
	h.logUserRequest("instrument", "", codes, 0, 0, len(instruments), "SUCCESS", totalLatency)

	h.writeJSONResponse(w, http.StatusOK, map[string]interface{}{
		"msg":  "OK",
		"data": instruments,
	})
}

// GetSupportedSymbols 获取支持的交易品种列表
func (h *HTTPHandler) GetSupportedSymbols(w http.ResponseWriter, r *http.Request) {
	startTime := time.Now()

	if r.Method != http.MethodGet {
		h.logUserRequest("symbols", "", []string{}, 0, 0, 0, "ERROR: Method not allowed", time.Since(startTime))
		h.writeErrorResponse(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	// 记录用户请求信息
	h.logUserRequest("symbols", "", []string{}, 0, 0, 0, "REQUEST", 0)

	symbols := h.config.GetSupportedSymbols()

	totalLatency := time.Since(startTime)
	h.logUserRequest("symbols", "", []string{}, 0, 0, len(symbols), "SUCCESS", totalLatency)

	h.writeJSONResponse(w, http.StatusOK, map[string]interface{}{
		"msg":  "OK",
		"data": symbols,
	})
}

// parseCodesFromQuery 从查询参数中解析代码列表并进行过滤
func (h *HTTPHandler) parseCodesFromQuery(r *http.Request) []string {
	codesParam := r.URL.Query().Get("codes")
	if codesParam == "" {
		return nil
	}

	codes := strings.Split(codesParam, ",")
	var result []string
	for _, code := range codes {
		code = strings.TrimSpace(code)
		if code != "" {
			// 检查品种是否被支持
			if h.config.IsSymbolSupported(code) {
				result = append(result, code)
			} else {
				h.logger.Warnf("Unsupported symbol requested: %s", code)
			}
		}
	}

	return result
}

// groupCodesByMarket 将代码按市场分组，符合API文档格式
func (h *HTTPHandler) groupCodesByMarket(codes []string) []string {
	// 按市场分组代码
	marketGroups := make(map[string][]string)

	for _, code := range codes {
		if len(code) < 3 {
			continue
		}

		// 提取市场前缀 (如 "US:", "HK:", "SH:" 等)
		colonIndex := strings.Index(code, ":")
		if colonIndex == -1 {
			continue
		}

		market := code[:colonIndex+1] // 包含冒号
		symbol := code[colonIndex+1:] // 不包含冒号

		marketGroups[market] = append(marketGroups[market], symbol)
	}

	// 将每个市场的代码用逗号连接
	var result []string
	for market, symbols := range marketGroups {
		if len(symbols) > 0 {
			groupedCode := market + strings.Join(symbols, ",")
			result = append(result, groupedCode)
		}
	}

	return result
}

// writeJSONResponse 写入JSON响应
func (h *HTTPHandler) writeJSONResponse(w http.ResponseWriter, statusCode int, data interface{}) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	if err := json.NewEncoder(w).Encode(data); err != nil {
		h.logger.Errorf("Failed to encode JSON response: %v", err)
	}
}

// writeErrorResponse 写入错误响应
func (h *HTTPHandler) writeErrorResponse(w http.ResponseWriter, statusCode int, message string) {
	h.writeJSONResponse(w, statusCode, map[string]interface{}{
		"msg":   "ERROR",
		"error": message,
	})
}

// shouldUseHistoryAPI 判断是否应该使用历史K线API
func (h *HTTPHandler) shouldUseHistoryAPI(count int, endTime int64, klineType types.KlineType) bool {
	// 如果指定了结束时间，使用历史API
	if endTime > 0 {
		return true
	}

	// // 如果请求的数量超过最近K线的最大周期数，使用历史API
	// maxRecentPeriods := 1000 // 默认值
	// if h.config != nil && h.config.Kline.Recent.MaxPeriods > 0 {
	// 	maxRecentPeriods = h.config.Kline.Recent.MaxPeriods
	// }

	// if count > maxRecentPeriods {
	// 	return true
	// }

	// // 对于长周期K线（周线、月线、年线），优先使用历史API
	// if klineType == types.KlineWeek || klineType == types.KlineMonth || klineType == types.KlineYear {
	// 	return true
	// }

	return false
}

// getRecentKline 获取最近K线数据（从缓存）
func (h *HTTPHandler) getRecentKline(w http.ResponseWriter, r *http.Request, codes []string, klineType types.KlineType, count int, totalStartTime time.Time) {
	// 解析复权类型
	adjustStr := r.URL.Query().Get("adjust")
	adjust := 1 // 默认前复权
	if adjustStr != "" {
		if a, err := strconv.Atoi(adjustStr); err == nil {
			adjust = a
		}
	}

	var allKlineData []types.KlineData
	var totalQOSAPILatency time.Duration
	var totalCorrectionLatency time.Duration

	for _, code := range codes {
		// 首先尝试从最近K线缓存获取
		cacheKey := types.GetCacheKey("recent_kline", code, klineType.String())
		if cached, found := h.cacheManager.Get(cacheKey); found {
			if klineData, ok := cached.(types.KlineData); ok {
				// 对缓存的数据也应用修复
				correctionStartTime := time.Now()
				correctedData := h.applyCorrectionToKlineData(&klineData)
				correctionLatency := time.Since(correctionStartTime)
				totalCorrectionLatency += correctionLatency

				// 限制返回的数量
				if len(correctedData.Klines) > count {
					correctedData.Klines = correctedData.Klines[len(correctedData.Klines)-count:]
				}

				allKlineData = append(allKlineData, *correctedData)
				continue
			}
		}

		// 缓存未命中，从API获取数据
		requests := []types.KlineRequest{
			{
				Code:       code,
				Count:      count,
				AdjustType: adjust,
				KlineType:  klineType,
			},
		}

		// 记录QOS API调用延迟
		qosAPIStartTime := time.Now()
		klineDataList, err := h.qosClient.GetKline(requests)
		qosAPILatency := time.Since(qosAPIStartTime)
		totalQOSAPILatency += qosAPILatency

		if err != nil {
			h.logger.Errorf("Failed to get recent kline for %s: %v", code, err)
			continue
		}

		for _, klineData := range klineDataList {
			// 应用修复数据
			correctionStartTime := time.Now()
			correctedData := h.applyCorrectionToKlineData(&klineData)
			correctionLatency := time.Since(correctionStartTime)
			totalCorrectionLatency += correctionLatency

			// 缓存修复后的数据到最近K线缓存
			cacheKey := types.GetCacheKey("recent_kline", correctedData.Code, klineType.String())
			if err := h.cacheManager.SetRecentKline(cacheKey, *correctedData, klineType.String()); err != nil {
				h.logger.Errorf("Failed to cache recent kline data: %v", err)
			}

			allKlineData = append(allKlineData, *correctedData)
		}
	}

	// 记录延迟统计
	if h.latencyRecorder != nil {
		h.latencyRecorder.RecordQOSAPILatency(totalQOSAPILatency)
		h.latencyRecorder.RecordCorrectionLatency(totalCorrectionLatency)
		h.latencyRecorder.RecordHistoryKlineLatency(time.Since(totalStartTime))
		h.latencyRecorder.RecordTotalLatency(time.Since(totalStartTime))
	}

	// 记录成功的用户请求
	totalLatency := time.Since(totalStartTime)
	h.logUserRequest("kline_recent", klineType.String(), codes, count, 0, len(allKlineData), "SUCCESS", totalLatency)

	h.writeJSONResponse(w, http.StatusOK, map[string]interface{}{
		"msg":  "OK",
		"data": allKlineData,
	})
}

// getHistoryKline 获取历史K线数据
func (h *HTTPHandler) getHistoryKline(w http.ResponseWriter, r *http.Request, codes []string, klineType types.KlineType, count int, endTime int64, totalStartTime time.Time) {
	// 解析复权类型
	adjustStr := r.URL.Query().Get("adjust")
	adjust := 1 // 默认前复权
	if adjustStr != "" {
		if a, err := strconv.Atoi(adjustStr); err == nil {
			adjust = a
		}
	}

	var allKlineData []types.KlineData
	var totalQOSAPILatency time.Duration
	var totalCorrectionLatency time.Duration

	for _, code := range codes {
		// 构建历史数据缓存键
		cacheKey := types.GetCacheKey("history_kline", code, klineType.String(), strconv.Itoa(count))
		if endTime > 0 {
			cacheKey += ":" + strconv.FormatInt(endTime, 10)
		}

		// 检查历史数据缓存
		if cached, found := h.cacheManager.Get(cacheKey); found {
			if klineData, ok := cached.(types.KlineData); ok {
				// 对缓存的数据也应用修复
				correctionStartTime := time.Now()
				correctedData := h.applyCorrectionToKlineData(&klineData)
				correctionLatency := time.Since(correctionStartTime)
				totalCorrectionLatency += correctionLatency

				allKlineData = append(allKlineData, *correctedData)
				continue
			}
		}

		// 缓存未命中，从历史API获取数据
		requests := []types.KlineRequest{
			{
				Code:       code,
				Count:      count,
				AdjustType: adjust,
				KlineType:  klineType,
				EndTime:    endTime,
			},
		}

		// 记录QOS API调用延迟
		qosAPIStartTime := time.Now()
		klineDataList, err := h.qosClient.GetHistoryKline(requests)
		qosAPILatency := time.Since(qosAPIStartTime)
		totalQOSAPILatency += qosAPILatency

		if err != nil {
			h.logger.Errorf("Failed to get history kline for %s: %v", code, err)
			continue
		}

		for _, klineData := range klineDataList {
			// 应用修复数据
			correctionStartTime := time.Now()
			correctedData := h.applyCorrectionToKlineData(&klineData)
			correctionLatency := time.Since(correctionStartTime)
			totalCorrectionLatency += correctionLatency

			// 缓存修复后的数据到历史K线缓存
			if err := h.cacheManager.SetHistoryKline(cacheKey, *correctedData); err != nil {
				h.logger.Errorf("Failed to cache history kline data: %v", err)
			}

			allKlineData = append(allKlineData, *correctedData)
		}
	}

	// 记录延迟统计
	if h.latencyRecorder != nil {
		h.latencyRecorder.RecordQOSAPILatency(totalQOSAPILatency)
		h.latencyRecorder.RecordCorrectionLatency(totalCorrectionLatency)
		h.latencyRecorder.RecordRecentKlineLatency(time.Since(totalStartTime))
		h.latencyRecorder.RecordTotalLatency(time.Since(totalStartTime))
	}

	// 记录成功的用户请求
	totalLatency := time.Since(totalStartTime)
	h.logUserRequest("kline_history", klineType.String(), codes, count, endTime, len(allKlineData), "SUCCESS", totalLatency)

	h.writeJSONResponse(w, http.StatusOK, map[string]interface{}{
		"msg":  "OK",
		"data": allKlineData,
	})
}

// GetHistoryKlineAPI 专门的历史K线获取接口
func (h *HTTPHandler) GetHistoryKlineAPI(w http.ResponseWriter, r *http.Request) {
	// 记录总延迟开始时间
	totalStartTime := time.Now()

	codes := h.parseCodesFromQuery(r)
	if len(codes) == 0 {
		h.logUserRequest("history", "", codes, 0, 0, 0, "ERROR: codes parameter is required", time.Since(totalStartTime))
		h.writeErrorResponse(w, http.StatusBadRequest, "codes parameter is required")
		return
	}

	// 解析K线类型
	klineTypeStr := r.URL.Query().Get("kline_type")
	if klineTypeStr == "" {
		klineTypeStr = "1001" // 默认日线
	}

	klineTypeInt, err := strconv.Atoi(klineTypeStr)
	if err != nil {
		h.logUserRequest("history", klineTypeStr, codes, 0, 0, 0, "ERROR: invalid kline_type parameter", time.Since(totalStartTime))
		h.writeErrorResponse(w, http.StatusBadRequest, "invalid kline_type parameter")
		return
	}
	klineType := types.KlineType(klineTypeInt)

	// 解析数量
	countStr := r.URL.Query().Get("count")
	count := 100 // 默认100根
	if countStr != "" {
		if c, err := strconv.Atoi(countStr); err == nil && c > 0 {
			count = c
		}
	}

	// 解析结束时间
	endTimeStr := r.URL.Query().Get("end_time")
	var endTime int64
	if endTimeStr != "" {
		if et, err := strconv.ParseInt(endTimeStr, 10, 64); err == nil {
			endTime = et
		}
	}

	// 记录用户请求信息
	h.logUserRequest("history", klineTypeStr, codes, count, endTime, 0, "REQUEST", 0)

	// 强制使用历史K线接口
	h.getHistoryKline(w, r, codes, klineType, count, endTime, totalStartTime)
}

// GetRecentKlineServiceStatus 获取最近K线服务状态
func (h *HTTPHandler) GetRecentKlineServiceStatus(w http.ResponseWriter, r *http.Request) {
	if h.recentKlineService == nil {
		h.writeJSONResponse(w, http.StatusOK, map[string]interface{}{
			"msg": "OK",
			"data": map[string]interface{}{
				"enabled": false,
				"message": "Recent kline service is not enabled",
			},
		})
		return
	}

	// 使用类型断言获取服务状态
	if service, ok := h.recentKlineService.(interface {
		GetStats() map[string]interface{}
	}); ok {
		stats := service.GetStats()
		h.writeJSONResponse(w, http.StatusOK, map[string]interface{}{
			"msg":  "OK",
			"data": stats,
		})
	} else {
		h.writeJSONResponse(w, http.StatusInternalServerError, map[string]interface{}{
			"msg": "Failed to get service status",
		})
	}
}

// SetupRoutes 设置路由
func (h *HTTPHandler) SetupRoutes() *http.ServeMux {
	mux := http.NewServeMux()

	// 健康检查
	mux.HandleFunc("/health", h.HealthCheck)

	// API路由
	mux.HandleFunc("/api/v1/snapshot", h.GetSnapshot)
	mux.HandleFunc("/api/v1/kline", h.GetKline)
	mux.HandleFunc("/api/v1/history", h.GetHistoryKlineAPI)
	mux.HandleFunc("/api/v1/depth", h.GetDepth)
	mux.HandleFunc("/api/v1/instrument", h.GetInstrumentInfo)
	mux.HandleFunc("/api/v1/symbols", h.GetSupportedSymbols)
	mux.HandleFunc("/api/v1/recent-kline-status", h.GetRecentKlineServiceStatus)

	// Web测试界面
	mux.HandleFunc("/kline-test", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "web/kline_test.html")
	})

	return mux
}

// SetupRoutesWithCorrection 设置包含修复功能的路由
func (h *HTTPHandler) SetupRoutesWithCorrection(correctionHandler interface{}) *http.ServeMux {
	mux := h.SetupRoutes()

	// 修复数据管理API
	if handler, ok := correctionHandler.(interface {
		AddCorrection(w http.ResponseWriter, r *http.Request)
		GetCorrections(w http.ResponseWriter, r *http.Request)
		GetCorrection(w http.ResponseWriter, r *http.Request)
		UpdateCorrection(w http.ResponseWriter, r *http.Request)
		DeleteCorrection(w http.ResponseWriter, r *http.Request)
		ToggleCorrection(w http.ResponseWriter, r *http.Request)
		GetStats(w http.ResponseWriter, r *http.Request)
	}); ok {
		mux.HandleFunc("/api/v1/corrections", func(w http.ResponseWriter, r *http.Request) {
			switch r.Method {
			case http.MethodPost:
				handler.AddCorrection(w, r)
			case http.MethodGet:
				handler.GetCorrections(w, r)
			default:
				http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			}
		})

		mux.HandleFunc("/api/v1/corrections/", func(w http.ResponseWriter, r *http.Request) {
			path := strings.TrimPrefix(r.URL.Path, "/api/v1/corrections/")
			if path == "stats" {
				handler.GetStats(w, r)
				return
			}

			if strings.Contains(path, "/toggle") {
				handler.ToggleCorrection(w, r)
				return
			}

			switch r.Method {
			case http.MethodGet:
				handler.GetCorrection(w, r)
			case http.MethodPut:
				handler.UpdateCorrection(w, r)
			case http.MethodDelete:
				handler.DeleteCorrection(w, r)
			default:
				http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			}
		})
	}

	// Web管理界面
	mux.HandleFunc("/corrections", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "web/corrections.html")
	})

	return mux
}
