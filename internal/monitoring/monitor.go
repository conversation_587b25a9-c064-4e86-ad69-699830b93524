package monitoring

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"runtime"
	"sync"
	"time"

	"qos-market-api/internal/cache"
	"qos-market-api/internal/client"
	"qos-market-api/internal/config"
	"qos-market-api/internal/logger"
	"qos-market-api/internal/recovery"
	"qos-market-api/internal/stats"
)

// Monitor 监控器
type Monitor struct {
	config *config.MonitoringConfig
	logger logger.Logger

	// 组件引用
	qosClient    *client.QOSClient
	cacheManager *cache.CacheManager
	corrector    interface{} // 修复器

	// 统计收集器
	statsCollector *stats.StatsCollector

	// 指标数据
	metrics      *Metrics
	metricsMutex sync.RWMutex

	// HTTP服务器
	httpServer *http.Server
}

// Metrics 指标数据
type Metrics struct {
	// 系统指标
	StartTime     time.Time `json:"start_time"`
	Uptime        string    `json:"uptime"`
	GoVersion     string    `json:"go_version"`
	NumGoroutines int       `json:"num_goroutines"`

	// 内存指标
	MemoryStats MemoryStats `json:"memory_stats"`

	// 服务指标
	QOSConnected     bool `json:"qos_connected"`
	CacheSize        int  `json:"cache_size"`
	WebSocketClients int  `json:"websocket_clients"`

	// 请求指标
	HTTPRequests    RequestStats `json:"http_requests"`
	WebSocketEvents RequestStats `json:"websocket_events"`

	// 缓存统计
	Cache stats.CacheStats `json:"cache"`

	// QOS API统计
	QOSAPI stats.QOSAPIStats `json:"qos_api"`

	// 延迟统计
	Latency stats.LatencyStatsCollection `json:"latency"`

	// K线修复统计
	Corrections CorrectionStats `json:"corrections"`

	// 错误指标
	Errors ErrorStats `json:"errors"`
}

// MemoryStats 内存统计
type MemoryStats struct {
	Alloc        uint64 `json:"alloc"`         // 当前分配的内存
	TotalAlloc   uint64 `json:"total_alloc"`   // 总分配的内存
	Sys          uint64 `json:"sys"`           // 系统内存
	NumGC        uint32 `json:"num_gc"`        // GC次数
	HeapAlloc    uint64 `json:"heap_alloc"`    // 堆内存分配
	HeapSys      uint64 `json:"heap_sys"`      // 堆系统内存
	HeapInuse    uint64 `json:"heap_inuse"`    // 堆使用内存
	HeapReleased uint64 `json:"heap_released"` // 堆释放内存
}

// RequestStats 请求统计
type RequestStats struct {
	Total   int64   `json:"total"`
	Success int64   `json:"success"`
	Failed  int64   `json:"failed"`
	Rate    float64 `json:"rate"` // 每秒请求数
}

// CorrectionStats K线修复统计
type CorrectionStats struct {
	Enabled           bool           `json:"enabled"`
	TotalCorrections  int            `json:"total_corrections"`
	ActiveCorrections int            `json:"active_corrections"`
	CacheSize         int            `json:"cache_size"`
	LastRefresh       time.Time      `json:"last_refresh"`
	ByCode            map[string]int `json:"by_code"`
	ByType            map[string]int `json:"by_type"`
	ByField           map[string]int `json:"by_field"`
}

// ErrorStats 错误统计
type ErrorStats struct {
	Total        int64            `json:"total"`
	LastError    string           `json:"last_error"`
	LastErrorAt  time.Time        `json:"last_error_at"`
	ErrorsByType map[string]int64 `json:"errors_by_type"`
}

// NewMonitor 创建新的监控器
func NewMonitor(cfg *config.MonitoringConfig, qosClient *client.QOSClient, cacheManager *cache.CacheManager, log logger.Logger) *Monitor {
	return &Monitor{
		config:         cfg,
		logger:         log,
		qosClient:      qosClient,
		cacheManager:   cacheManager,
		corrector:      nil, // 稍后设置
		statsCollector: stats.NewStatsCollector(),
		metrics: &Metrics{
			StartTime: time.Now(),
			GoVersion: runtime.Version(),
			Errors: ErrorStats{
				ErrorsByType: make(map[string]int64),
			},
			Corrections: CorrectionStats{
				ByCode:  make(map[string]int),
				ByType:  make(map[string]int),
				ByField: make(map[string]int),
			},
		},
	}
}

// SetCorrector 设置修复器
func (m *Monitor) SetCorrector(corrector interface{}) {
	m.corrector = corrector
}

// Start 启动监控器
func (m *Monitor) Start(ctx context.Context) error {
	if !m.config.Enabled {
		m.logger.Info("Monitoring is disabled")
		return nil
	}

	m.logger.Info("Starting monitor...")

	// 启动指标收集
	go m.collectMetrics(ctx)

	// 启动HTTP服务器
	if err := m.startHTTPServer(); err != nil {
		return err
	}

	return nil
}

// Stop 停止监控器
func (m *Monitor) Stop() error {
	if m.httpServer != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		return m.httpServer.Shutdown(ctx)
	}
	return nil
}

// startHTTPServer 启动HTTP服务器
func (m *Monitor) startHTTPServer() error {
	mux := http.NewServeMux()
	mux.HandleFunc("/metrics", m.handleMetrics)
	mux.HandleFunc("/health", m.handleHealth)

	m.httpServer = &http.Server{
		Addr:    m.getAddress(),
		Handler: mux,
	}

	go func() {
		if err := m.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			m.logger.Errorf("Monitor HTTP server error: %v", err)
		}
	}()

	m.logger.Infof("Monitor HTTP server started on %s", m.getAddress())
	return nil
}

// getAddress 获取监控服务地址
func (m *Monitor) getAddress() string {
	return fmt.Sprintf(":%d", m.config.MetricsPort)
}

// collectMetrics 收集指标
func (m *Monitor) collectMetrics(ctx context.Context) {
	ticker := time.NewTicker(m.config.HealthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			m.updateMetrics()
		}
	}
}

// updateMetrics 更新指标
func (m *Monitor) updateMetrics() {
	m.metricsMutex.Lock()
	defer m.metricsMutex.Unlock()

	// 更新系统指标
	m.metrics.Uptime = time.Since(m.metrics.StartTime).String()
	m.metrics.NumGoroutines = runtime.NumGoroutine()

	// 更新内存指标
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)
	m.metrics.MemoryStats = MemoryStats{
		Alloc:        memStats.Alloc,
		TotalAlloc:   memStats.TotalAlloc,
		Sys:          memStats.Sys,
		NumGC:        memStats.NumGC,
		HeapAlloc:    memStats.HeapAlloc,
		HeapSys:      memStats.HeapSys,
		HeapInuse:    memStats.HeapInuse,
		HeapReleased: memStats.HeapReleased,
	}

	// 更新服务指标
	m.metrics.QOSConnected = m.qosClient.IsConnected()
	m.metrics.CacheSize = m.cacheManager.Size()

	// 更新统计数据
	m.metrics.Cache = m.statsCollector.GetCacheStats()
	m.metrics.QOSAPI = m.statsCollector.GetQOSAPIStats()
	m.metrics.Latency = m.statsCollector.GetLatencyStats()

	// 更新修复统计
	m.updateCorrectionStats()
}

// updateCorrectionStats 更新修复统计
func (m *Monitor) updateCorrectionStats() {
	if m.corrector == nil {
		m.metrics.Corrections.Enabled = false
		return
	}

	// 使用类型断言获取修复统计
	if corrector, ok := m.corrector.(interface {
		GetCorrectionStats() map[string]interface{}
	}); ok {
		stats := corrector.GetCorrectionStats()

		m.metrics.Corrections.Enabled = true

		if totalCorrections, ok := stats["total_corrections"].(int); ok {
			m.metrics.Corrections.TotalCorrections = totalCorrections
		}

		if cacheSize, ok := stats["cache_size"].(int); ok {
			m.metrics.Corrections.CacheSize = cacheSize
		}

		if byCode, ok := stats["by_code"].(map[string]int); ok {
			m.metrics.Corrections.ByCode = byCode
		}

		if byType, ok := stats["by_type"].(map[interface{}]int); ok {
			typeStats := make(map[string]int)
			for k, v := range byType {
				if key, ok := k.(string); ok {
					typeStats[key] = v
				}
			}
			m.metrics.Corrections.ByType = typeStats
		}

		if byField, ok := stats["by_field"].(map[string]int); ok {
			m.metrics.Corrections.ByField = byField
		}

		m.metrics.Corrections.LastRefresh = time.Now()
	}
}

// handleMetrics 处理指标请求
func (m *Monitor) handleMetrics(w http.ResponseWriter, r *http.Request) {
	m.metricsMutex.RLock()
	metrics := *m.metrics
	m.metricsMutex.RUnlock()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(metrics)
}

// handleHealth 处理健康检查请求
func (m *Monitor) handleHealth(w http.ResponseWriter, r *http.Request) {
	health := m.getHealthStatus()

	status := http.StatusOK
	if !health["healthy"].(bool) {
		status = http.StatusServiceUnavailable
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(status)
	json.NewEncoder(w).Encode(health)
}

// getHealthStatus 获取健康状态
func (m *Monitor) getHealthStatus() map[string]interface{} {
	healthy := true
	checks := make(map[string]interface{})

	// 检查QOS连接
	qosHealthy := m.qosClient.IsConnected()
	checks["qos_connection"] = map[string]interface{}{
		"healthy": qosHealthy,
		"message": func() string {
			if qosHealthy {
				return "Connected"
			}
			return "Disconnected"
		}(),
	}
	if !qosHealthy {
		healthy = false
	}

	// 检查内存使用
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)
	memHealthy := memStats.Alloc < 1024*1024*1024 // 1GB限制
	checks["memory"] = map[string]interface{}{
		"healthy": memHealthy,
		"alloc":   memStats.Alloc,
		"message": func() string {
			if memHealthy {
				return "Normal"
			}
			return "High memory usage"
		}(),
	}
	if !memHealthy {
		healthy = false
	}

	// 检查协程数量
	numGoroutines := runtime.NumGoroutine()
	goroutineHealthy := numGoroutines < 10000 // 10000个协程限制
	checks["goroutines"] = map[string]interface{}{
		"healthy": goroutineHealthy,
		"count":   numGoroutines,
		"message": func() string {
			if goroutineHealthy {
				return "Normal"
			}
			return "Too many goroutines"
		}(),
	}
	if !goroutineHealthy {
		healthy = false
	}

	return map[string]interface{}{
		"healthy":   healthy,
		"timestamp": time.Now().Unix(),
		"checks":    checks,
	}
}

// RecordHTTPRequest 记录HTTP请求
func (m *Monitor) RecordHTTPRequest(success bool) {
	m.metricsMutex.Lock()
	defer m.metricsMutex.Unlock()

	m.metrics.HTTPRequests.Total++
	if success {
		m.metrics.HTTPRequests.Success++
	} else {
		m.metrics.HTTPRequests.Failed++
	}
}

// RecordCacheHit 记录缓存命中
func (m *Monitor) RecordCacheHit() {
	m.statsCollector.RecordCacheHit()
}

// RecordCacheMiss 记录缓存未命中
func (m *Monitor) RecordCacheMiss() {
	m.statsCollector.RecordCacheMiss()
}

// RecordQOSHTTPCall 记录QOS HTTP调用
func (m *Monitor) RecordQOSHTTPCall() {
	m.statsCollector.RecordQOSHTTPCall()
}

// RecordQOSAPILatency 记录QOS API调用延迟
func (m *Monitor) RecordQOSAPILatency(latency time.Duration) {
	m.statsCollector.RecordQOSAPILatency(latency)
}

// RecordCorrectionLatency 记录K线修复延迟
func (m *Monitor) RecordCorrectionLatency(latency time.Duration) {
	m.statsCollector.RecordCorrectionLatency(latency)
}

// RecordTotalLatency 记录总延迟
func (m *Monitor) RecordTotalLatency(latency time.Duration) {
	m.statsCollector.RecordTotalLatency(latency)
}

// RecordRecentKlineLatency 记录最近K线延迟
func (m *Monitor) RecordRecentKlineLatency(latency time.Duration) {
	m.statsCollector.RecordRecentKlineLatency(latency)
}

// RecordHistoryKlineLatency 记录历史K线延迟
func (m *Monitor) RecordHistoryKlineLatency(latency time.Duration) {
	m.statsCollector.RecordHistoryKlineLatency(latency)
}

// RecordCacheLatency 记录缓存延迟
func (m *Monitor) RecordCacheLatency(latency time.Duration) {
	m.statsCollector.RecordCacheLatency(latency)
}

// UpdateMetrics 手动更新指标（用于测试）
func (m *Monitor) UpdateMetrics() {
	m.updateMetrics()
}

// HandleMetrics 处理指标请求（用于测试）
func (m *Monitor) HandleMetrics(w http.ResponseWriter, r *http.Request) {
	m.handleMetrics(w, r)
}

// RecordWebSocketEvent 记录WebSocket事件
func (m *Monitor) RecordWebSocketEvent(success bool) {
	m.metricsMutex.Lock()
	defer m.metricsMutex.Unlock()

	m.metrics.WebSocketEvents.Total++
	if success {
		m.metrics.WebSocketEvents.Success++
	} else {
		m.metrics.WebSocketEvents.Failed++
	}
}

// RecordError 记录错误
func (m *Monitor) RecordError(errorType, message string) {
	m.metricsMutex.Lock()
	defer m.metricsMutex.Unlock()

	m.metrics.Errors.Total++
	m.metrics.Errors.LastError = message
	m.metrics.Errors.LastErrorAt = time.Now()
	m.metrics.Errors.ErrorsByType[errorType]++
}

// GetName 获取监控器名称（实现HealthChecker接口）
func (m *Monitor) GetName() string {
	return "monitor"
}

// IsHealthy 检查是否健康（实现HealthChecker接口）
func (m *Monitor) IsHealthy() bool {
	health := m.getHealthStatus()
	return health["healthy"].(bool)
}

// 确保Monitor实现了HealthChecker接口
var _ recovery.HealthChecker = (*Monitor)(nil)
