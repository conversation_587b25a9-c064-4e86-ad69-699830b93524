package stats

import (
	"testing"
	"time"
)

func TestTimeWindowCollector_Record(t *testing.T) {
	collector := NewTimeWindowCollector()
	
	// 记录一些事件
	collector.Record()
	collector.Record()
	collector.Record()
	
	stats := collector.GetStats()
	
	// 验证总数
	if stats.Total != 3 {
		t.<PERSON><PERSON><PERSON>("Expected total 3, got %d", stats.Total)
	}
	
	// 验证时间窗口统计
	if stats.LastMinute != 3 {
		t.<PERSON><PERSON><PERSON>("Expected last minute 3, got %d", stats.LastMinute)
	}
	
	if stats.LastHour != 3 {
		t.<PERSON><PERSON><PERSON>("Expected last hour 3, got %d", stats.LastHour)
	}
	
	if stats.Last24Hours != 3 {
		t.<PERSON><PERSON><PERSON>("Expected last 24 hours 3, got %d", stats.Last24Hours)
	}
}

func TestTimeWindowCollector_TimeWindows(t *testing.T) {
	collector := NewTimeWindowCollector()
	
	// 手动添加不同时间的记录来测试时间窗口
	now := time.Now()
	
	// 添加一个2分钟前的记录
	collector.mutex.Lock()
	collector.records = append(collector.records, now.Add(-2*time.Minute))
	collector.mutex.Unlock()
	
	// 添加一个2小时前的记录
	collector.mutex.Lock()
	collector.records = append(collector.records, now.Add(-2*time.Hour))
	collector.mutex.Unlock()
	
	// 添加一个当前记录
	collector.Record()
	
	stats := collector.GetStats()
	
	// 验证总数
	if stats.Total != 3 {
		t.Errorf("Expected total 3, got %d", stats.Total)
	}
	
	// 验证最近1分钟（只有当前记录）
	if stats.LastMinute != 1 {
		t.Errorf("Expected last minute 1, got %d", stats.LastMinute)
	}
	
	// 验证最近1小时（当前记录 + 2分钟前的记录）
	if stats.LastHour != 2 {
		t.Errorf("Expected last hour 2, got %d", stats.LastHour)
	}
	
	// 验证最近24小时（所有记录）
	if stats.Last24Hours != 3 {
		t.Errorf("Expected last 24 hours 3, got %d", stats.Last24Hours)
	}
}

func TestTimeWindowCollector_Cleanup(t *testing.T) {
	collector := NewTimeWindowCollector()
	
	// 添加一个超过24小时的记录
	oldTime := time.Now().Add(-25 * time.Hour)
	collector.mutex.Lock()
	collector.records = append(collector.records, oldTime)
	collector.mutex.Unlock()
	
	// 添加一个当前记录，这会触发清理
	collector.Record()
	
	stats := collector.GetStats()
	
	// 验证旧记录被清理
	if stats.Total != 1 {
		t.Errorf("Expected total 1 after cleanup, got %d", stats.Total)
	}
	
	if stats.Last24Hours != 1 {
		t.Errorf("Expected last 24 hours 1 after cleanup, got %d", stats.Last24Hours)
	}
}

func TestStatsCollector_CacheStats(t *testing.T) {
	collector := NewStatsCollector()
	
	// 记录一些缓存命中和未命中
	collector.RecordCacheHit()
	collector.RecordCacheHit()
	collector.RecordCacheMiss()
	
	stats := collector.GetCacheStats()
	
	// 验证命中统计
	if stats.Hits.Total != 2 {
		t.Errorf("Expected cache hits 2, got %d", stats.Hits.Total)
	}
	
	// 验证未命中统计
	if stats.Misses.Total != 1 {
		t.Errorf("Expected cache misses 1, got %d", stats.Misses.Total)
	}
	
	// 验证命中率
	expectedHitRate := float64(2) / float64(3) * 100
	if stats.HitRate != expectedHitRate {
		t.Errorf("Expected hit rate %.2f, got %.2f", expectedHitRate, stats.HitRate)
	}
}

func TestStatsCollector_QOSAPIStats(t *testing.T) {
	collector := NewStatsCollector()
	
	// 记录一些HTTP调用
	collector.RecordQOSHTTPCall()
	collector.RecordQOSHTTPCall()
	collector.RecordQOSHTTPCall()
	
	stats := collector.GetQOSAPIStats()
	
	// 验证HTTP调用统计
	if stats.HTTPCalls.Total != 3 {
		t.Errorf("Expected HTTP calls 3, got %d", stats.HTTPCalls.Total)
	}
	
	if stats.HTTPCalls.LastMinute != 3 {
		t.Errorf("Expected HTTP calls last minute 3, got %d", stats.HTTPCalls.LastMinute)
	}
}

func TestStatsCollector_ZeroHitRate(t *testing.T) {
	collector := NewStatsCollector()
	
	// 没有记录任何事件
	stats := collector.GetCacheStats()
	
	// 验证命中率为0
	if stats.HitRate != 0 {
		t.Errorf("Expected hit rate 0 for no records, got %.2f", stats.HitRate)
	}
}

func TestStatsCollector_OnlyMisses(t *testing.T) {
	collector := NewStatsCollector()
	
	// 只记录未命中
	collector.RecordCacheMiss()
	collector.RecordCacheMiss()
	
	stats := collector.GetCacheStats()
	
	// 验证命中率为0
	if stats.HitRate != 0 {
		t.Errorf("Expected hit rate 0 for only misses, got %.2f", stats.HitRate)
	}
	
	// 验证未命中统计
	if stats.Misses.Total != 2 {
		t.Errorf("Expected cache misses 2, got %d", stats.Misses.Total)
	}
}

func TestStatsCollector_OnlyHits(t *testing.T) {
	collector := NewStatsCollector()
	
	// 只记录命中
	collector.RecordCacheHit()
	collector.RecordCacheHit()
	
	stats := collector.GetCacheStats()
	
	// 验证命中率为100
	if stats.HitRate != 100.0 {
		t.Errorf("Expected hit rate 100 for only hits, got %.2f", stats.HitRate)
	}
	
	// 验证命中统计
	if stats.Hits.Total != 2 {
		t.Errorf("Expected cache hits 2, got %d", stats.Hits.Total)
	}
}

func BenchmarkTimeWindowCollector_Record(b *testing.B) {
	collector := NewTimeWindowCollector()
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		collector.Record()
	}
}

func BenchmarkTimeWindowCollector_GetStats(b *testing.B) {
	collector := NewTimeWindowCollector()
	
	// 预先添加一些记录
	for i := 0; i < 1000; i++ {
		collector.Record()
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		collector.GetStats()
	}
}
