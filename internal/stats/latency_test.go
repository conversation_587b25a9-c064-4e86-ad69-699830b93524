package stats

import (
	"testing"
	"time"
)

func TestLatencyCollector(t *testing.T) {
	collector := NewLatencyCollector()

	// 测试空收集器
	stats := collector.GetStats()
	if stats.Count.Total != 0 {
		t.<PERSON><PERSON>rf("Expected total count 0, got %d", stats.Count.Total)
	}
	if stats.Average.Total != 0 {
		t.<PERSON><PERSON><PERSON>("Expected average 0, got %d", stats.Average.Total)
	}
	if stats.Max.Total != 0 {
		t.<PERSON><PERSON>("Expected max 0, got %d", stats.Max.Total)
	}

	// 记录一些延迟
	latencies := []time.Duration{
		100 * time.Millisecond,
		200 * time.Millisecond,
		300 * time.Millisecond,
	}

	for _, latency := range latencies {
		collector.Record(latency)
	}

	// 获取统计数据
	stats = collector.GetStats()

	// 验证计数
	if stats.Count.Total != 3 {
		t.Errorf("Expected total count 3, got %d", stats.Count.Total)
	}
	if stats.Count.LastMinute != 3 {
		t.<PERSON><PERSON><PERSON>("Expected last minute count 3, got %d", stats.Count.LastMinute)
	}
	if stats.Count.LastHour != 3 {
		t.<PERSON><PERSON>rf("Expected last hour count 3, got %d", stats.Count.LastHour)
	}
	if stats.Count.Last24Hours != 3 {
		t.Errorf("Expected last 24 hours count 3, got %d", stats.Count.Last24Hours)
	}

	// 验证平均值 (100+200+300)/3 = 200ms
	expectedAverage := int64(200)
	if stats.Average.Total != expectedAverage {
		t.Errorf("Expected average %d, got %d", expectedAverage, stats.Average.Total)
	}

	// 验证最大值 300ms
	expectedMax := int64(300)
	if stats.Max.Total != expectedMax {
		t.Errorf("Expected max %d, got %d", expectedMax, stats.Max.Total)
	}
}

func TestLatencyCollectorTimeWindows(t *testing.T) {
	collector := NewLatencyCollector()

	// 手动添加不同时间的记录来测试时间窗口
	now := time.Now()

	// 添加一个2小时前的记录
	oldRecord := LatencyRecord{
		Timestamp: now.Add(-2 * time.Hour),
		Latency:   100 * time.Millisecond,
	}
	collector.records = append(collector.records, oldRecord)

	// 添加一个30分钟前的记录
	recentRecord := LatencyRecord{
		Timestamp: now.Add(-30 * time.Minute),
		Latency:   200 * time.Millisecond,
	}
	collector.records = append(collector.records, recentRecord)

	// 添加一个当前记录
	collector.Record(300 * time.Millisecond)

	stats := collector.GetStats()

	// 验证总计数
	if stats.Count.Total != 3 {
		t.Errorf("Expected total count 3, got %d", stats.Count.Total)
	}

	// 验证24小时内计数（应该包含所有3个）
	if stats.Count.Last24Hours != 3 {
		t.Errorf("Expected last 24 hours count 3, got %d", stats.Count.Last24Hours)
	}

	// 验证1小时内计数（应该包含2个：30分钟前和当前）
	if stats.Count.LastHour != 2 {
		t.Errorf("Expected last hour count 2, got %d", stats.Count.LastHour)
	}

	// 验证1分钟内计数（应该只有1个：当前）
	if stats.Count.LastMinute != 1 {
		t.Errorf("Expected last minute count 1, got %d", stats.Count.LastMinute)
	}
}

func TestStatsCollectorLatency(t *testing.T) {
	collector := NewStatsCollector()

	// 记录不同类型的延迟
	collector.RecordQOSAPILatency(100 * time.Millisecond)
	collector.RecordQOSAPILatency(200 * time.Millisecond)

	collector.RecordCorrectionLatency(50 * time.Millisecond)
	collector.RecordCorrectionLatency(75 * time.Millisecond)

	collector.RecordTotalLatency(300 * time.Millisecond)
	collector.RecordTotalLatency(400 * time.Millisecond)

	// 获取延迟统计
	latencyStats := collector.GetLatencyStats()

	// 验证QOS API延迟统计
	if latencyStats.QOSAPILatency.Count.Total != 2 {
		t.Errorf("Expected QOS API latency count 2, got %d", latencyStats.QOSAPILatency.Count.Total)
	}
	expectedQOSAvg := int64(150) // (100+200)/2
	if latencyStats.QOSAPILatency.Average.Total != expectedQOSAvg {
		t.Errorf("Expected QOS API average %d, got %d", expectedQOSAvg, latencyStats.QOSAPILatency.Average.Total)
	}

	// 验证修复延迟统计
	if latencyStats.CorrectionLatency.Count.Total != 2 {
		t.Errorf("Expected correction latency count 2, got %d", latencyStats.CorrectionLatency.Count.Total)
	}
	expectedCorrAvg := int64(62) // (50+75)/2 = 62.5, 向下取整为62
	if latencyStats.CorrectionLatency.Average.Total != expectedCorrAvg {
		t.Errorf("Expected correction average %d, got %d", expectedCorrAvg, latencyStats.CorrectionLatency.Average.Total)
	}

	// 验证总延迟统计
	if latencyStats.TotalLatency.Count.Total != 2 {
		t.Errorf("Expected total latency count 2, got %d", latencyStats.TotalLatency.Count.Total)
	}
	expectedTotalAvg := int64(350) // (300+400)/2
	if latencyStats.TotalLatency.Average.Total != expectedTotalAvg {
		t.Errorf("Expected total average %d, got %d", expectedTotalAvg, latencyStats.TotalLatency.Average.Total)
	}
}

func TestCalculateAverage(t *testing.T) {
	// 测试空切片
	avg := calculateAverage([]time.Duration{})
	if avg != 0 {
		t.Errorf("Expected average 0 for empty slice, got %d", avg)
	}

	// 测试单个值
	avg = calculateAverage([]time.Duration{100 * time.Millisecond})
	if avg != 100 {
		t.Errorf("Expected average 100, got %d", avg)
	}

	// 测试多个值
	latencies := []time.Duration{
		100 * time.Millisecond,
		200 * time.Millisecond,
		300 * time.Millisecond,
	}
	avg = calculateAverage(latencies)
	if avg != 200 {
		t.Errorf("Expected average 200, got %d", avg)
	}
}

func TestCalculateMax(t *testing.T) {
	// 测试空切片
	max := calculateMax([]time.Duration{})
	if max != 0 {
		t.Errorf("Expected max 0 for empty slice, got %d", max)
	}

	// 测试单个值
	max = calculateMax([]time.Duration{100 * time.Millisecond})
	if max != 100 {
		t.Errorf("Expected max 100, got %d", max)
	}

	// 测试多个值
	latencies := []time.Duration{
		200 * time.Millisecond,
		100 * time.Millisecond,
		300 * time.Millisecond,
		150 * time.Millisecond,
	}
	max = calculateMax(latencies)
	if max != 300 {
		t.Errorf("Expected max 300, got %d", max)
	}
}

func TestLatencyCollectorCleanup(t *testing.T) {
	collector := NewLatencyCollector()

	// 添加一个超过24小时的记录
	oldTime := time.Now().Add(-25 * time.Hour)
	oldRecord := LatencyRecord{
		Timestamp: oldTime,
		Latency:   100 * time.Millisecond,
	}
	collector.records = append(collector.records, oldRecord)

	// 添加一个当前记录，这会触发清理
	collector.Record(200 * time.Millisecond)

	// 验证旧记录被清理
	stats := collector.GetStats()
	if stats.Count.Total != 1 {
		t.Errorf("Expected total count 1 after cleanup, got %d", stats.Count.Total)
	}
	if stats.Average.Total != 200 {
		t.Errorf("Expected average 200 after cleanup, got %d", stats.Average.Total)
	}
}

func BenchmarkLatencyCollectorRecord(b *testing.B) {
	collector := NewLatencyCollector()
	latency := 100 * time.Millisecond

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		collector.Record(latency)
	}
}

func BenchmarkLatencyCollectorGetStats(b *testing.B) {
	collector := NewLatencyCollector()

	// 预填充一些数据
	for i := 0; i < 1000; i++ {
		collector.Record(time.Duration(i) * time.Millisecond)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		collector.GetStats()
	}
}
