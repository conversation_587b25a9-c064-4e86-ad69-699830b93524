package stats

import (
	"sync"
	"time"
)

// TimeWindowCollector 时间窗口统计收集器
type TimeWindowCollector struct {
	mutex   sync.RWMutex
	records []time.Time // 记录时间戳
}

// NewTimeWindowCollector 创建新的时间窗口收集器
func NewTimeWindowCollector() *TimeWindowCollector {
	return &TimeWindowCollector{
		records: make([]time.Time, 0),
	}
}

// Record 记录一次事件
func (c *TimeWindowCollector) Record() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	now := time.Now()
	c.records = append(c.records, now)

	// 清理超过24小时的记录
	c.cleanup(now)
}

// GetStats 获取统计数据
func (c *TimeWindowCollector) GetStats() TimeWindowStats {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	now := time.Now()

	// 确保数据是最新的
	c.cleanupUnsafe(now)

	stats := TimeWindowStats{
		Total: int64(len(c.records)),
	}

	// 计算各时间窗口的统计
	for _, record := range c.records {
		duration := now.Sub(record)

		if duration <= time.Minute {
			stats.LastMinute++
		}
		if duration <= time.Hour {
			stats.LastHour++
		}
		if duration <= 24*time.Hour {
			stats.Last24Hours++
		}
	}

	return stats
}

// cleanup 清理超过24小时的记录（需要锁）
func (c *TimeWindowCollector) cleanup(now time.Time) {
	cutoff := now.Add(-24 * time.Hour)

	// 找到第一个在24小时内的记录
	start := 0
	for i, record := range c.records {
		if record.After(cutoff) {
			start = i
			break
		}
	}

	// 如果所有记录都超过24小时，清空
	if start == 0 && len(c.records) > 0 && c.records[len(c.records)-1].Before(cutoff) {
		c.records = c.records[:0]
	} else if start > 0 {
		// 保留24小时内的记录
		c.records = c.records[start:]
	}
}

// cleanupUnsafe 清理超过24小时的记录（不加锁，用于已经持有读锁的情况）
func (c *TimeWindowCollector) cleanupUnsafe(now time.Time) {
	// 注意：这里我们不能修改切片，因为持有的是读锁
	// 这个方法主要用于统计时确保数据准确性
	// 实际清理在Record方法中进行
}

// TimeWindowStats 时间窗口统计
type TimeWindowStats struct {
	Total       int64 `json:"total"`         // 总次数
	Last24Hours int64 `json:"last_24_hours"` // 最近24小时
	LastHour    int64 `json:"last_hour"`     // 最近1小时
	LastMinute  int64 `json:"last_minute"`   // 最近1分钟
}

// LatencyRecord 延迟记录
type LatencyRecord struct {
	Timestamp time.Time     // 记录时间
	Latency   time.Duration // 延迟时间
}

// LatencyCollector 延迟统计收集器
type LatencyCollector struct {
	mutex   sync.RWMutex
	records []LatencyRecord // 延迟记录
}

// NewLatencyCollector 创建新的延迟收集器
func NewLatencyCollector() *LatencyCollector {
	return &LatencyCollector{
		records: make([]LatencyRecord, 0),
	}
}

// Record 记录一次延迟
func (c *LatencyCollector) Record(latency time.Duration) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	now := time.Now()
	c.records = append(c.records, LatencyRecord{
		Timestamp: now,
		Latency:   latency,
	})

	// 清理超过24小时的记录
	c.cleanup(now)
}

// GetStats 获取延迟统计数据
func (c *LatencyCollector) GetStats() LatencyStats {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	now := time.Now()

	// 确保数据是最新的
	c.cleanupUnsafe(now)

	stats := LatencyStats{
		Count: LatencyWindowStats{
			Total: int64(len(c.records)),
		},
	}

	if len(c.records) == 0 {
		return stats
	}

	// 分时间窗口统计
	var totalLatencies, last24hLatencies, lastHourLatencies, lastMinuteLatencies []time.Duration

	for _, record := range c.records {
		duration := now.Sub(record.Timestamp)
		totalLatencies = append(totalLatencies, record.Latency)

		if duration <= time.Minute {
			lastMinuteLatencies = append(lastMinuteLatencies, record.Latency)
			stats.Count.LastMinute++
		}
		if duration <= time.Hour {
			lastHourLatencies = append(lastHourLatencies, record.Latency)
			stats.Count.LastHour++
		}
		if duration <= 24*time.Hour {
			last24hLatencies = append(last24hLatencies, record.Latency)
			stats.Count.Last24Hours++
		}
	}

	// 计算平均值和最大值
	stats.Average.Total = calculateAverage(totalLatencies)
	stats.Average.Last24Hours = calculateAverage(last24hLatencies)
	stats.Average.LastHour = calculateAverage(lastHourLatencies)
	stats.Average.LastMinute = calculateAverage(lastMinuteLatencies)

	stats.Max.Total = calculateMax(totalLatencies)
	stats.Max.Last24Hours = calculateMax(last24hLatencies)
	stats.Max.LastHour = calculateMax(lastHourLatencies)
	stats.Max.LastMinute = calculateMax(lastMinuteLatencies)

	return stats
}

// cleanup 清理超过24小时的记录（需要锁）
func (c *LatencyCollector) cleanup(now time.Time) {
	cutoff := now.Add(-24 * time.Hour)

	// 找到第一个在24小时内的记录
	start := 0
	for i, record := range c.records {
		if record.Timestamp.After(cutoff) {
			start = i
			break
		}
	}

	// 如果所有记录都超过24小时，清空
	if start == 0 && len(c.records) > 0 && c.records[len(c.records)-1].Timestamp.Before(cutoff) {
		c.records = c.records[:0]
	} else if start > 0 {
		// 保留24小时内的记录
		c.records = c.records[start:]
	}
}

// cleanupUnsafe 清理超过24小时的记录（不加锁，用于已经持有读锁的情况）
func (c *LatencyCollector) cleanupUnsafe(now time.Time) {
	// 注意：这里我们不能修改切片，因为持有的是读锁
	// 这个方法主要用于统计时确保数据准确性
	// 实际清理在Record方法中进行
}

// calculateAverage 计算平均延迟
func calculateAverage(latencies []time.Duration) int64 {
	if len(latencies) == 0 {
		return 0
	}

	var total time.Duration
	for _, latency := range latencies {
		total += latency
	}

	return int64(total / time.Duration(len(latencies)) / time.Millisecond)
}

// calculateMax 计算最大延迟
func calculateMax(latencies []time.Duration) int64 {
	if len(latencies) == 0 {
		return 0
	}

	max := latencies[0]
	for _, latency := range latencies[1:] {
		if latency > max {
			max = latency
		}
	}

	return int64(max / time.Millisecond)
}

// LatencyWindowStats 延迟时间窗口统计
type LatencyWindowStats struct {
	Total       int64 `json:"total"`         // 总次数
	Last24Hours int64 `json:"last_24_hours"` // 最近24小时
	LastHour    int64 `json:"last_hour"`     // 最近1小时
	LastMinute  int64 `json:"last_minute"`   // 最近1分钟
}

// LatencyStats 延迟统计
type LatencyStats struct {
	Count   LatencyWindowStats `json:"count"`   // 请求次数统计
	Average LatencyWindowStats `json:"average"` // 平均延迟(毫秒)
	Max     LatencyWindowStats `json:"max"`     // 最大延迟(毫秒)
}

// StatsCollector 统计收集器
type StatsCollector struct {
	cacheHits    *TimeWindowCollector
	cacheMisses  *TimeWindowCollector
	qosHTTPCalls *TimeWindowCollector

	// 延迟统计收集器
	qosAPILatency       *LatencyCollector
	correctionLatency   *LatencyCollector
	totalLatency        *LatencyCollector
	recentKlineLatency  *LatencyCollector
	historyKlineLatency *LatencyCollector
	cacheLatency        *LatencyCollector
}

// NewStatsCollector 创建新的统计收集器
func NewStatsCollector() *StatsCollector {
	return &StatsCollector{
		cacheHits:    NewTimeWindowCollector(),
		cacheMisses:  NewTimeWindowCollector(),
		qosHTTPCalls: NewTimeWindowCollector(),

		// 延迟统计收集器
		qosAPILatency:       NewLatencyCollector(),
		correctionLatency:   NewLatencyCollector(),
		totalLatency:        NewLatencyCollector(),
		recentKlineLatency:  NewLatencyCollector(),
		historyKlineLatency: NewLatencyCollector(),
		cacheLatency:        NewLatencyCollector(),
	}
}

// RecordCacheHit 记录缓存命中
func (s *StatsCollector) RecordCacheHit() {
	s.cacheHits.Record()
}

// RecordCacheMiss 记录缓存未命中
func (s *StatsCollector) RecordCacheMiss() {
	s.cacheMisses.Record()
}

// RecordQOSHTTPCall 记录QOS HTTP调用
func (s *StatsCollector) RecordQOSHTTPCall() {
	s.qosHTTPCalls.Record()
}

// RecordQOSAPILatency 记录QOS API调用延迟
func (s *StatsCollector) RecordQOSAPILatency(latency time.Duration) {
	s.qosAPILatency.Record(latency)
}

// RecordCorrectionLatency 记录K线修复延迟
func (s *StatsCollector) RecordCorrectionLatency(latency time.Duration) {
	s.correctionLatency.Record(latency)
}

// RecordTotalLatency 记录总延迟
func (s *StatsCollector) RecordTotalLatency(latency time.Duration) {
	s.totalLatency.Record(latency)
}

// RecordRecentKlineLatency 记录最近K线延迟
func (s *StatsCollector) RecordRecentKlineLatency(latency time.Duration) {
	s.recentKlineLatency.Record(latency)
}

// RecordHistoryKlineLatency 记录历史K线延迟
func (s *StatsCollector) RecordHistoryKlineLatency(latency time.Duration) {
	s.historyKlineLatency.Record(latency)
}

// RecordCacheLatency 记录缓存延迟
func (s *StatsCollector) RecordCacheLatency(latency time.Duration) {
	s.cacheLatency.Record(latency)
}

// GetCacheStats 获取缓存统计
func (s *StatsCollector) GetCacheStats() CacheStats {
	hits := s.cacheHits.GetStats()
	misses := s.cacheMisses.GetStats()

	// 计算命中率
	total := hits.Total + misses.Total
	var hitRate float64
	if total > 0 {
		hitRate = float64(hits.Total) / float64(total) * 100
	}

	return CacheStats{
		Hits:    hits,
		Misses:  misses,
		HitRate: hitRate,
	}
}

// GetQOSAPIStats 获取QOS API统计
func (s *StatsCollector) GetQOSAPIStats() QOSAPIStats {
	return QOSAPIStats{
		HTTPCalls: s.qosHTTPCalls.GetStats(),
		Latency:   s.qosAPILatency.GetStats(),
	}
}

// GetLatencyStats 获取延迟统计
func (s *StatsCollector) GetLatencyStats() LatencyStatsCollection {
	return LatencyStatsCollection{
		QOSAPILatency:       s.qosAPILatency.GetStats(),
		CorrectionLatency:   s.correctionLatency.GetStats(),
		TotalLatency:        s.totalLatency.GetStats(),
		RecentKlineLatency:  s.recentKlineLatency.GetStats(),
		HistoryKlineLatency: s.historyKlineLatency.GetStats(),
		CacheLatency:        s.cacheLatency.GetStats(),
	}
}

// CacheStats 缓存统计
type CacheStats struct {
	Hits    TimeWindowStats `json:"hits"`     // 命中统计
	Misses  TimeWindowStats `json:"misses"`   // 未命中统计
	HitRate float64         `json:"hit_rate"` // 命中率
}

// QOSAPIStats QOS API调用统计
type QOSAPIStats struct {
	HTTPCalls TimeWindowStats `json:"http_calls"` // HTTP接口调用统计
	Latency   LatencyStats    `json:"latency"`    // 延迟统计
}

// LatencyStatsCollection 延迟统计集合
type LatencyStatsCollection struct {
	QOSAPILatency       LatencyStats `json:"qos_api_latency"`       // QOS API调用延迟
	CorrectionLatency   LatencyStats `json:"correction_latency"`    // K线修复延迟
	TotalLatency        LatencyStats `json:"total_latency"`         // 总延迟
	RecentKlineLatency  LatencyStats `json:"recent_kline_latency"`  // 最近K线延迟
	HistoryKlineLatency LatencyStats `json:"history_kline_latency"` // 历史K线延迟
	CacheLatency        LatencyStats `json:"cache_latency"`         // 缓存延迟
}
