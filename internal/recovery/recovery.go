package recovery

import (
	"context"
	"fmt"
	"os"
	"runtime"
	"sync"
	"time"

	"qos-market-api/internal/config"
	"qos-market-api/internal/logger"
)

// RecoveryManager 恢复管理器
type RecoveryManager struct {
	config     *config.RecoveryConfig
	logger     logger.Logger
	
	// 健康检查
	healthCheckers map[string]HealthChecker
	healthMutex    sync.RWMutex
	
	// 重启控制
	restartChan chan struct{}
	stopChan    chan struct{}
}

// HealthChecker 健康检查接口
type HealthChecker interface {
	IsHealthy() bool
	GetName() string
}

// NewRecoveryManager 创建新的恢复管理器
func NewRecoveryManager(cfg *config.RecoveryConfig, log logger.Logger) *RecoveryManager {
	return &RecoveryManager{
		config:         cfg,
		logger:         log,
		healthCheckers: make(map[string]HealthChecker),
		restartChan:    make(chan struct{}, 1),
		stopChan:       make(chan struct{}),
	}
}

// Start 启动恢复管理器
func (rm *RecoveryManager) Start(ctx context.Context) {
	rm.logger.Info("Starting recovery manager...")
	
	// 设置panic恢复
	if rm.config.RestartOnPanic {
		rm.setupPanicRecovery()
	}
	
	// 启动健康检查
	go rm.healthCheckLoop(ctx)
	
	// 启动重启监听
	go rm.restartListener(ctx)
}

// Stop 停止恢复管理器
func (rm *RecoveryManager) Stop() {
	rm.logger.Info("Stopping recovery manager...")
	close(rm.stopChan)
}

// RegisterHealthChecker 注册健康检查器
func (rm *RecoveryManager) RegisterHealthChecker(checker HealthChecker) {
	rm.healthMutex.Lock()
	rm.healthCheckers[checker.GetName()] = checker
	rm.healthMutex.Unlock()
	rm.logger.Infof("Registered health checker: %s", checker.GetName())
}

// UnregisterHealthChecker 取消注册健康检查器
func (rm *RecoveryManager) UnregisterHealthChecker(name string) {
	rm.healthMutex.Lock()
	delete(rm.healthCheckers, name)
	rm.healthMutex.Unlock()
	rm.logger.Infof("Unregistered health checker: %s", name)
}

// TriggerRestart 触发重启
func (rm *RecoveryManager) TriggerRestart() {
	select {
	case rm.restartChan <- struct{}{}:
		rm.logger.Warn("Restart triggered")
	default:
		rm.logger.Warn("Restart already pending")
	}
}

// setupPanicRecovery 设置panic恢复
func (rm *RecoveryManager) setupPanicRecovery() {
	// 这个函数需要在main函数中调用defer
	defer func() {
		if r := recover(); r != nil {
			rm.logger.Errorf("Panic recovered: %v", r)
			
			// 打印堆栈信息
			buf := make([]byte, 1024*64)
			n := runtime.Stack(buf, false)
			rm.logger.Errorf("Stack trace:\n%s", buf[:n])
			
			// 触发重启
			rm.TriggerRestart()
		}
	}()
}

// healthCheckLoop 健康检查循环
func (rm *RecoveryManager) healthCheckLoop(ctx context.Context) {
	ticker := time.NewTicker(rm.config.HealthCheckInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return
		case <-rm.stopChan:
			return
		case <-ticker.C:
			rm.performHealthCheck()
		}
	}
}

// performHealthCheck 执行健康检查
func (rm *RecoveryManager) performHealthCheck() {
	rm.healthMutex.RLock()
	checkers := make(map[string]HealthChecker)
	for name, checker := range rm.healthCheckers {
		checkers[name] = checker
	}
	rm.healthMutex.RUnlock()
	
	unhealthyServices := make([]string, 0)
	
	for name, checker := range checkers {
		if !checker.IsHealthy() {
			unhealthyServices = append(unhealthyServices, name)
			rm.logger.Warnf("Health check failed for service: %s", name)
		}
	}
	
	if len(unhealthyServices) > 0 {
		rm.logger.Errorf("Unhealthy services detected: %v", unhealthyServices)
		// 可以根据需要触发重启或其他恢复操作
		// rm.TriggerRestart()
	}
}

// restartListener 重启监听器
func (rm *RecoveryManager) restartListener(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		case <-rm.stopChan:
			return
		case <-rm.restartChan:
			rm.performRestart()
		}
	}
}

// performRestart 执行重启
func (rm *RecoveryManager) performRestart() {
	rm.logger.Warn("Performing application restart...")
	
	// 这里可以实现优雅重启逻辑
	// 例如：保存状态、关闭连接、重新启动服务等
	
	// 简单的重启实现：退出程序，由外部监控重启
	os.Exit(1)
}

// RetryWithBackoff 带退避的重试函数
func RetryWithBackoff(operation func() error, maxRetries int, initialDelay time.Duration, maxDelay time.Duration, logger logger.Logger) error {
	var err error
	delay := initialDelay
	
	for i := 0; i <= maxRetries; i++ {
		err = operation()
		if err == nil {
			return nil
		}
		
		if i == maxRetries {
			break
		}
		
		logger.Warnf("Operation failed (attempt %d/%d): %v, retrying in %v", i+1, maxRetries+1, err, delay)
		time.Sleep(delay)
		
		// 指数退避
		delay *= 2
		if delay > maxDelay {
			delay = maxDelay
		}
	}
	
	return fmt.Errorf("operation failed after %d retries: %w", maxRetries+1, err)
}

// CircuitBreaker 熔断器
type CircuitBreaker struct {
	maxFailures   int
	resetTimeout  time.Duration
	failures      int
	lastFailTime  time.Time
	state         CircuitState
	mutex         sync.RWMutex
}

// CircuitState 熔断器状态
type CircuitState int

const (
	CircuitClosed CircuitState = iota
	CircuitOpen
	CircuitHalfOpen
)

// NewCircuitBreaker 创建新的熔断器
func NewCircuitBreaker(maxFailures int, resetTimeout time.Duration) *CircuitBreaker {
	return &CircuitBreaker{
		maxFailures:  maxFailures,
		resetTimeout: resetTimeout,
		state:        CircuitClosed,
	}
}

// Execute 执行操作
func (cb *CircuitBreaker) Execute(operation func() error) error {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()
	
	// 检查是否可以执行
	if cb.state == CircuitOpen {
		if time.Since(cb.lastFailTime) > cb.resetTimeout {
			cb.state = CircuitHalfOpen
			cb.failures = 0
		} else {
			return fmt.Errorf("circuit breaker is open")
		}
	}
	
	// 执行操作
	err := operation()
	
	if err != nil {
		cb.failures++
		cb.lastFailTime = time.Now()
		
		if cb.failures >= cb.maxFailures {
			cb.state = CircuitOpen
		}
		
		return err
	}
	
	// 成功执行
	if cb.state == CircuitHalfOpen {
		cb.state = CircuitClosed
	}
	cb.failures = 0
	
	return nil
}

// GetState 获取熔断器状态
func (cb *CircuitBreaker) GetState() CircuitState {
	cb.mutex.RLock()
	defer cb.mutex.RUnlock()
	return cb.state
}

// SafeGoroutine 安全的协程执行
func SafeGoroutine(fn func(), logger logger.Logger) {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				logger.Errorf("Goroutine panic recovered: %v", r)
				
				// 打印堆栈信息
				buf := make([]byte, 1024*64)
				n := runtime.Stack(buf, false)
				logger.Errorf("Goroutine stack trace:\n%s", buf[:n])
			}
		}()
		
		fn()
	}()
}

// WithTimeout 带超时的操作执行
func WithTimeout(operation func() error, timeout time.Duration) error {
	done := make(chan error, 1)
	
	go func() {
		done <- operation()
	}()
	
	select {
	case err := <-done:
		return err
	case <-time.After(timeout):
		return fmt.Errorf("operation timeout after %v", timeout)
	}
}
