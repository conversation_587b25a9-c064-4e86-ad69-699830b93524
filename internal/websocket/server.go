package websocket

import (
	"context"
	"encoding/json"
	"net/http"
	"sync"
	"time"

	"github.com/gorilla/websocket"

	"qos-market-api/internal/cache"
	"qos-market-api/internal/client"
	"qos-market-api/internal/config"
	"qos-market-api/internal/logger"
	"qos-market-api/pkg/types"
)

// Client WebSocket客户端连接
type Client struct {
	conn          *websocket.Conn
	send          chan []byte
	server        *Server
	subscriptions map[string]bool
	mutex         sync.RWMutex
	id            string
}

// Server WebSocket服务器
type Server struct {
	clients      map[*Client]bool
	clientsMutex sync.RWMutex
	register     chan *Client
	unregister   chan *Client
	broadcast    chan []byte

	qosClient    *client.QOSClient
	cacheManager *cache.CacheManager
	config       *config.WebSocketConfig
	logger       logger.Logger

	upgrader websocket.Upgrader

	// 运行状态控制
	ctx    context.Context
	cancel context.CancelFunc
	done   chan struct{}
}

// NewServer 创建新的WebSocket服务器
func NewServer(qosClient *client.QOSClient, cacheManager *cache.CacheManager, cfg *config.WebSocketConfig, log logger.Logger) *Server {
	server := &Server{
		clients:      make(map[*Client]bool),
		register:     make(chan *Client),
		unregister:   make(chan *Client),
		broadcast:    make(chan []byte),
		qosClient:    qosClient,
		cacheManager: cacheManager,
		config:       cfg,
		logger:       log,
		done:         make(chan struct{}),
		upgrader: websocket.Upgrader{
			ReadBufferSize:  cfg.ReadBufferSize,
			WriteBufferSize: cfg.WriteBufferSize,
			CheckOrigin: func(r *http.Request) bool {
				return true // 允许所有来源，生产环境应该限制
			},
		},
	}

	// 注册QOS客户端消息处理器
	server.setupQOSMessageHandlers()

	return server
}

// Start 启动WebSocket服务器
func (s *Server) Start(ctx context.Context) {
	s.ctx, s.cancel = context.WithCancel(ctx)
	go s.run(s.ctx)
}

// Stop 停止WebSocket服务器
func (s *Server) Stop() {
	s.logger.Info("Stopping WebSocket server...")

	if s.cancel != nil {
		s.cancel()
	}

	// 等待服务器停止
	<-s.done

	// 关闭所有客户端连接
	s.clientsMutex.Lock()
	for client := range s.clients {
		client.conn.Close()
		close(client.send)
		delete(s.clients, client)
	}
	s.clientsMutex.Unlock()

	s.logger.Info("WebSocket server stopped")
}

// run 运行服务器主循环
func (s *Server) run(ctx context.Context) {
	defer close(s.done)

	for {
		select {
		case <-ctx.Done():
			s.logger.Info("WebSocket server context cancelled, stopping...")
			return
		case client := <-s.register:
			s.clientsMutex.Lock()
			s.clients[client] = true
			s.clientsMutex.Unlock()
			s.logger.Infof("WebSocket client connected: %s", client.id)

		case client := <-s.unregister:
			s.clientsMutex.Lock()
			if _, ok := s.clients[client]; ok {
				delete(s.clients, client)
				close(client.send)
			}
			s.clientsMutex.Unlock()
			s.logger.Infof("WebSocket client disconnected: %s", client.id)

		case message := <-s.broadcast:
			s.clientsMutex.RLock()
			for client := range s.clients {
				select {
				case client.send <- message:
				default:
					close(client.send)
					delete(s.clients, client)
				}
			}
			s.clientsMutex.RUnlock()
		}
	}
}

// HandleWebSocket 处理WebSocket连接
func (s *Server) HandleWebSocket(w http.ResponseWriter, r *http.Request) {
	conn, err := s.upgrader.Upgrade(w, r, nil)
	if err != nil {
		s.logger.Errorf("WebSocket upgrade failed: %v", err)
		return
	}

	client := &Client{
		conn:          conn,
		send:          make(chan []byte, 256),
		server:        s,
		subscriptions: make(map[string]bool),
		id:            generateClientID(),
	}

	client.server.register <- client

	// 启动客户端处理协程
	go client.writePump()
	go client.readPump()
}

// setupQOSMessageHandlers 设置QOS消息处理器
func (s *Server) setupQOSMessageHandlers() {
	// 实时快照处理器
	s.qosClient.RegisterMessageHandler("SS", func(data []byte) {
		s.broadcast <- data
	})

	// 盘口深度处理器
	s.qosClient.RegisterMessageHandler("SD", func(data []byte) {
		s.broadcast <- data
	})

	// K线数据处理器
	s.qosClient.RegisterMessageHandler("SK", func(data []byte) {
		s.broadcast <- data
	})

	// 成交明细处理器
	s.qosClient.RegisterMessageHandler("ST", func(data []byte) {
		s.broadcast <- data
	})
}

// generateClientID 生成客户端ID
func generateClientID() string {
	return time.Now().Format("20060102150405") + "_" + "client"
}

// readPump 读取客户端消息
func (c *Client) readPump() {
	defer func() {
		c.server.unregister <- c
		c.conn.Close()
	}()

	c.conn.SetReadLimit(c.server.config.MaxMessageSize)
	c.conn.SetReadDeadline(time.Now().Add(c.server.config.PongWait))
	c.conn.SetPongHandler(func(string) error {
		c.conn.SetReadDeadline(time.Now().Add(c.server.config.PongWait))
		return nil
	})

	for {
		_, message, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				c.server.logger.Errorf("WebSocket error: %v", err)
			}
			break
		}

		// 处理客户端消息
		c.handleMessage(message)
	}
}

// writePump 向客户端写入消息
func (c *Client) writePump() {
	ticker := time.NewTicker(c.server.config.PingPeriod)
	defer func() {
		ticker.Stop()
		c.conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.send:
			c.conn.SetWriteDeadline(time.Now().Add(c.server.config.WriteWait))
			if !ok {
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)

			// 批量发送队列中的消息
			n := len(c.send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-c.send)
			}

			if err := w.Close(); err != nil {
				return
			}

		case <-ticker.C:
			c.conn.SetWriteDeadline(time.Now().Add(c.server.config.WriteWait))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleMessage 处理客户端消息
func (c *Client) handleMessage(message []byte) {
	var msg types.WebSocketMessage
	if err := json.Unmarshal(message, &msg); err != nil {
		c.server.logger.Errorf("Failed to unmarshal client message: %v", err)
		return
	}

	switch msg.Type {
	case "SUBSCRIBE_SNAPSHOT":
		c.handleSubscribeSnapshot(msg)
	case "SUBSCRIBE_DEPTH":
		c.handleSubscribeDepth(msg)
	case "SUBSCRIBE_KLINE":
		c.handleSubscribeKline(msg)
	case "UNSUBSCRIBE":
		c.handleUnsubscribe(msg)
	case "PING":
		c.handlePing()
	default:
		c.server.logger.Warnf("Unknown message type: %s", msg.Type)
	}
}

// handleSubscribeSnapshot 处理订阅快照
func (c *Client) handleSubscribeSnapshot(msg types.WebSocketMessage) {
	var req types.SubscriptionRequest
	if data, err := json.Marshal(msg.Data); err == nil {
		json.Unmarshal(data, &req)
	}

	if len(req.Codes) == 0 {
		return
	}

	// 记录订阅
	c.mutex.Lock()
	for _, code := range req.Codes {
		c.subscriptions["snapshot:"+code] = true
	}
	c.mutex.Unlock()

	// 转发到QOS客户端
	if err := c.server.qosClient.SubscribeSnapshot(req.Codes); err != nil {
		c.server.logger.Errorf("Failed to subscribe snapshot: %v", err)
	}
}

// handleSubscribeDepth 处理订阅深度
func (c *Client) handleSubscribeDepth(msg types.WebSocketMessage) {
	var req types.SubscriptionRequest
	if data, err := json.Marshal(msg.Data); err == nil {
		json.Unmarshal(data, &req)
	}

	if len(req.Codes) == 0 {
		return
	}

	// 记录订阅
	c.mutex.Lock()
	for _, code := range req.Codes {
		c.subscriptions["depth:"+code] = true
	}
	c.mutex.Unlock()

	// 转发到QOS客户端
	if err := c.server.qosClient.SubscribeDepth(req.Codes); err != nil {
		c.server.logger.Errorf("Failed to subscribe depth: %v", err)
	}
}

// handleSubscribeKline 处理订阅K线
func (c *Client) handleSubscribeKline(msg types.WebSocketMessage) {
	// 这里需要解析K线类型等参数
	// 简化处理，实际应用中需要更详细的参数解析
	var req struct {
		Codes     []string        `json:"codes"`
		KlineType types.KlineType `json:"kline_type"`
	}

	if data, err := json.Marshal(msg.Data); err == nil {
		json.Unmarshal(data, &req)
	}

	if len(req.Codes) == 0 {
		return
	}

	// 记录订阅
	c.mutex.Lock()
	for _, code := range req.Codes {
		c.subscriptions["kline:"+code] = true
	}
	c.mutex.Unlock()

	// 转发到QOS客户端
	if err := c.server.qosClient.SubscribeKline(req.Codes, req.KlineType); err != nil {
		c.server.logger.Errorf("Failed to subscribe kline: %v", err)
	}
}

// handleUnsubscribe 处理取消订阅
func (c *Client) handleUnsubscribe(msg types.WebSocketMessage) {
	// 简化处理，清除所有订阅
	c.mutex.Lock()
	c.subscriptions = make(map[string]bool)
	c.mutex.Unlock()
}

// handlePing 处理ping消息
func (c *Client) handlePing() {
	response := types.WebSocketMessage{
		Type:    "PONG",
		Message: "OK",
	}

	if data, err := json.Marshal(response); err == nil {
		select {
		case c.send <- data:
		default:
			close(c.send)
		}
	}
}
