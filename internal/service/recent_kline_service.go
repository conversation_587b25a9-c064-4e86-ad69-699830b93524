package service

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"qos-market-api/internal/cache"
	"qos-market-api/internal/config"
	"qos-market-api/internal/correction"
	"qos-market-api/internal/logger"
	"qos-market-api/pkg/types"
)

// QOSClient QOS客户端接口
type QOSClient interface {
	GetKline(requests []types.KlineRequest) ([]types.KlineData, error)
	IsConnected() bool
}

// RecentKlineService 最近K线后台更新服务
type RecentKlineService struct {
	qosClient    QOSClient
	cacheManager *cache.CacheManager
	corrector    *correction.Corrector
	config       *config.KlineConfig
	symbols      *config.SymbolsConfig
	logger       logger.Logger

	// 运行状态
	ctx       context.Context
	cancel    context.CancelFunc
	wg        sync.WaitGroup
	isRunning bool
	mutex     sync.RWMutex

	// 统计信息
	lastUpdateTime time.Time
	updateCount    int64
	errorCount     int64

	// 延迟优化相关
	dataLoadTime    map[string]time.Time // 记录每个缓存键的数据加载时间
	isInitialized   bool                 // 是否已完成初始化加载
	integrityTicker *time.Ticker         // 完整性检查定时器
}

// NewRecentKlineService 创建最近K线服务
func NewRecentKlineService(
	qosClient QOSClient,
	cacheManager *cache.CacheManager,
	corrector *correction.Corrector,
	config *config.KlineConfig,
	symbols *config.SymbolsConfig,
	logger logger.Logger,
) *RecentKlineService {
	ctx, cancel := context.WithCancel(context.Background())

	return &RecentKlineService{
		qosClient:     qosClient,
		cacheManager:  cacheManager,
		corrector:     corrector,
		config:        config,
		symbols:       symbols,
		logger:        logger,
		ctx:           ctx,
		cancel:        cancel,
		dataLoadTime:  make(map[string]time.Time),
		isInitialized: false,
	}
}

// Start 启动服务
func (s *RecentKlineService) Start() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.isRunning {
		return nil
	}

	if !s.config.Recent.Enabled {
		s.logger.Info("Recent kline service is disabled")
		return nil
	}

	s.isRunning = true
	s.wg.Add(1)

	go s.updateLoop()

	s.logger.Infof("Recent kline service started with update interval: %v", s.config.Recent.UpdateInterval)
	return nil
}

// Stop 停止服务
func (s *RecentKlineService) Stop() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.isRunning {
		return nil
	}

	s.logger.Info("Stopping recent kline service...")
	s.cancel()

	// 停止完整性检查定时器
	if s.integrityTicker != nil {
		s.integrityTicker.Stop()
		s.integrityTicker = nil
	}

	// 使用带超时的等待
	done := make(chan struct{})
	go func() {
		s.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		s.logger.Info("Recent kline service stopped gracefully")
	case <-time.After(5 * time.Second):
		s.logger.Warn("Recent kline service stop timeout, forcing shutdown")
	}

	s.isRunning = false
	return nil
}

// IsRunning 检查服务是否运行中
func (s *RecentKlineService) IsRunning() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.isRunning
}

// IsInitialized 检查服务是否已初始化
func (s *RecentKlineService) IsInitialized() bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	return s.isInitialized
}

// GetStats 获取服务统计信息
func (s *RecentKlineService) GetStats() map[string]interface{} {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	// 获取支持的K线类型字符串列表
	supportedTypes := s.getSupportedKlineTypes()
	typeStrings := make([]string, len(supportedTypes))
	for i, klineType := range supportedTypes {
		typeStrings[i] = klineType.String()
	}

	return map[string]interface{}{
		"is_running":               s.isRunning,
		"last_update_time":         s.lastUpdateTime,
		"update_count":             s.updateCount,
		"error_count":              s.errorCount,
		"update_interval":          s.config.Recent.UpdateInterval.String(),
		"max_periods":              s.config.Recent.MaxPeriods,
		"supported_types":          typeStrings,
		"configured_types":         s.config.Recent.SupportedTypes,
		"is_initialized":           s.isInitialized,
		"initial_load_count":       s.config.Recent.InitialLoadCount,
		"refresh_count":            s.config.Recent.RefreshCount,
		"integrity_check_interval": s.config.Recent.IntegrityCheckInterval.String(),
		"max_data_age":             s.config.Recent.MaxDataAge.String(),
		"cached_items":             len(s.dataLoadTime),
	}
}

// waitForWebSocketConnection 等待WebSocket连接成功
func (s *RecentKlineService) waitForWebSocketConnection(timeout time.Duration) bool {
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	timeoutTimer := time.NewTimer(timeout)
	defer timeoutTimer.Stop()

	for {
		select {
		case <-s.ctx.Done():
			return false
		case <-timeoutTimer.C:
			return false
		case <-ticker.C:
			if s.qosClient.IsConnected() {
				return true
			}
		}
	}
}

// updateLoop 更新循环
func (s *RecentKlineService) updateLoop() {
	defer s.wg.Done()

	// 等待WebSocket连接成功，最多等待1分钟
	s.logger.Info("Waiting for WebSocket connection before starting recent kline updates...")
	if !s.waitForWebSocketConnection(120 * time.Second) {
		s.logger.Error("WebSocket connection timeout, recent kline service will not start")
		return
	}
	s.logger.Info("WebSocket connected, starting recent kline updates")

	// 执行初始化加载
	s.performInitialLoad()

	// 创建更新定时器
	ticker := time.NewTicker(s.config.Recent.UpdateInterval)
	defer ticker.Stop()

	// 创建完整性检查定时器
	s.startIntegrityChecker()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			s.updateRecentKlines()
		}
	}
}

// updateRecentKlines 更新最近K线数据
func (s *RecentKlineService) updateRecentKlines() {
	// 检查context是否已经取消
	select {
	case <-s.ctx.Done():
		s.logger.Debug("Context cancelled, skipping recent kline update")
		return
	default:
	}

	startTime := time.Now()

	// 检查QOS连接状态
	if !s.qosClient.IsConnected() {
		s.logger.Warn("QOS client is not connected, skipping recent kline update")
		s.incrementErrorCount()
		return
	}

	// 获取所有支持的交易品种
	allCodes := s.getAllSupportedCodes()
	if len(allCodes) == 0 {
		s.logger.Warn("No supported symbols found for recent kline update")
		return
	} else {
		s.logger.Infof("Updating recent klines for %d symbols: {%v}", len(allCodes), allCodes)
	}

	// 获取配置的K线类型
	klineTypes := s.getSupportedKlineTypes()

	// 并行更新不同K线类型
	successCount, errorCount := s.updateKlineTypesParallel(allCodes, klineTypes)

	// 更新统计信息
	s.mutex.Lock()
	s.lastUpdateTime = time.Now()
	s.updateCount++
	if errorCount > 0 {
		s.errorCount++
	}
	s.mutex.Unlock()

	duration := time.Since(startTime)
	s.logger.Infof("Recent kline update completed: %d success, %d errors, duration: %v",
		successCount, errorCount, duration)
}

// updateKlineTypesParallel 并行更新不同K线类型
func (s *RecentKlineService) updateKlineTypesParallel(codes []string, klineTypes []types.KlineType) (int, int) {
	if len(klineTypes) == 0 {
		return 0, 0
	}

	// 创建结果通道
	type updateResult struct {
		klineType types.KlineType
		err       error
		duration  time.Duration
	}
	resultChan := make(chan updateResult, len(klineTypes))

	// 启动并行更新goroutines
	for _, klineType := range klineTypes {
		go func(kt types.KlineType) {
			startTime := time.Now()
			err := s.updateKlineType(codes, kt)
			duration := time.Since(startTime)
			resultChan <- updateResult{
				klineType: kt,
				err:       err,
				duration:  duration,
			}
		}(klineType)
	}

	// 收集结果
	successCount := 0
	errorCount := 0
	for i := 0; i < len(klineTypes); i++ {
		result := <-resultChan
		if result.err != nil {
			s.logger.Errorf("Failed to update %s klines: %v (duration: %v)", result.klineType.String(), result.err, result.duration)
			errorCount++
		} else {
			s.logger.Infof("Successfully updated %s klines for %d symbols (duration: %v)", result.klineType.String(), len(codes), result.duration)
			successCount++
		}
	}

	return successCount, errorCount
}

// updateKlineType 更新指定类型的K线数据
func (s *RecentKlineService) updateKlineType(codes []string, klineType types.KlineType) error {
	// 检查context是否已经取消
	select {
	case <-s.ctx.Done():
		return s.ctx.Err()
	default:
	}

	// 检查是否已初始化，如果未初始化则跳过更新
	// 初始化应该通过 performInitialLoad 进行顺序加载
	s.mutex.RLock()
	initialized := s.isInitialized
	s.mutex.RUnlock()

	if !initialized {
		// 未初始化时跳过更新，避免绕过顺序初始化逻辑
		s.logger.Debugf("Service not initialized, skipping %s kline update", klineType.String())
		return nil
	}

	// 已初始化时使用增量更新
	return s.incrementalUpdateKlineType(codes, klineType)
}

// incrementalUpdateKlineType 增量更新指定类型的K线数据
func (s *RecentKlineService) incrementalUpdateKlineType(codes []string, klineType types.KlineType) error {
	// 获取刷新条数，如果未配置则使用默认值10
	refreshCount := s.config.Recent.RefreshCount
	if refreshCount <= 0 {
		refreshCount = 10
	}

	// 构建请求
	requests := make([]types.KlineRequest, 0, len(codes))
	for _, code := range codes {
		requests = append(requests, types.KlineRequest{
			Code:       code,
			Count:      refreshCount,
			AdjustType: 1, // 前复权
			KlineType:  klineType,
		})
	}

	// 批量获取K线数据
	klineDataList, err := s.qosClient.GetKline(requests)
	if err != nil {
		return fmt.Errorf("failed to get kline data from QOS API: %w", err)
	}

	// 检查返回的数据是否为空
	if len(klineDataList) == 0 {
		s.logger.Warnf("QOS API returned empty kline data list for %d requests", len(requests))
		return nil
	}

	// 处理每个品种的K线数据
	for _, klineData := range klineDataList {
		if err := s.processIncrementalKlineData(&klineData, klineType); err != nil {
			s.logger.Errorf("Failed to process incremental kline data for %s: %v", klineData.Code, err)
			continue
		}
	}

	return nil
}

// processIncrementalKlineData 处理增量K线数据（合并到现有缓存）
func (s *RecentKlineService) processIncrementalKlineData(klineData *types.KlineData, klineType types.KlineType) error {
	if klineData == nil || len(klineData.Klines) == 0 {
		return nil
	}

	// 应用K线修复
	correctedData := s.corrector.ApplyCorrections(klineData)

	// 获取缓存键
	cacheKey := types.GetCacheKey("recent_kline", correctedData.Code, klineType.String())

	// 获取现有缓存数据
	existingData, found := s.cacheManager.Get(cacheKey)
	if !found {
		// 如果缓存不存在，直接缓存新数据
		s.logger.Warnf("No cached data type for %s, reloading...", cacheKey)
		s.reloadSingleKlineData(correctedData.Code, klineType)
		return nil
		// return s.processInitialKlineData(correctedData, klineType)
	}

	// 转换现有数据
	existingKlineData, ok := existingData.(types.KlineData)
	if !ok {
		s.logger.Warnf("Invalid cached data type for %s, reloading...", cacheKey)
		s.reloadSingleKlineData(correctedData.Code, klineType)
		return nil
		// return s.processInitialKlineData(correctedData, klineType)
	}

	// 合并K线数据
	mergedData := s.mergeKlineData(&existingKlineData, correctedData)

	// 限制保留的最大周期数
	maxPeriods := s.config.Recent.MaxPeriods
	if len(mergedData.Klines) > maxPeriods {
		// 保留最新的数据
		mergedData.Klines = mergedData.Klines[len(mergedData.Klines)-maxPeriods:]
	}

	// 缓存合并后的数据
	dataType := "kline_" + klineType.String()
	if err := s.cacheManager.Set(cacheKey, *mergedData, dataType); err != nil {
		return err
	}

	// 更新数据加载时间
	s.mutex.Lock()
	s.dataLoadTime[cacheKey] = time.Now()
	s.mutex.Unlock()

	s.logger.Debugf("Updated incremental kline data for %s (%s): %d periods",
		mergedData.Code, klineType.String(), len(mergedData.Klines))

	return nil
}

// getAllSupportedCodes 获取所有支持的交易品种代码
func (s *RecentKlineService) getAllSupportedCodes() []string {
	var codes []string

	// 美股
	for _, symbol := range s.symbols.USStocks {
		codes = append(codes, types.FormatCode(types.MarketUS, symbol))
	}

	// 港股
	for _, symbol := range s.symbols.HKStocks {
		codes = append(codes, types.FormatCode(types.MarketHK, symbol))
	}

	// A股
	if len(s.symbols.AStocks.SH) > 0 {
		for _, symbol := range s.symbols.AStocks.SH {
			codes = append(codes, types.FormatCode(types.MarketSH, symbol))
		}
	}
	if len(s.symbols.AStocks.SZ) > 0 {
		for _, symbol := range s.symbols.AStocks.SZ {
			codes = append(codes, types.FormatCode(types.MarketSZ, symbol))
		}
	}

	// 数字货币
	for _, symbol := range s.symbols.Crypto {
		codes = append(codes, types.FormatCode(types.MarketCF, symbol))
	}

	// 外汇
	if len(s.symbols.Forex) > 0 {
		for _, symbol := range s.symbols.Forex {
			codes = append(codes, types.FormatCode(types.MarketFX, symbol))
		}
	}

	// 商品
	if len(s.symbols.Commodities) > 0 {
		for _, symbol := range s.symbols.Commodities {
			codes = append(codes, types.FormatCode(types.MarketCM, symbol))
		}
	}

	return codes
}

// getSupportedKlineTypes 获取支持的K线类型
func (s *RecentKlineService) getSupportedKlineTypes() []types.KlineType {
	// 默认支持的K线类型
	defaultTypes := []types.KlineType{
		types.KlineDay,
	}

	// 如果配置中没有指定支持的类型，使用默认值
	if len(s.config.Recent.SupportedTypes) == 0 {
		return defaultTypes
	}

	// 解析配置中的K线类型
	var supportedTypes []types.KlineType
	typeMap := map[string]types.KlineType{
		"1m":  types.Kline1Min,
		"5m":  types.Kline5Min,
		"15m": types.Kline15Min,
		"30m": types.Kline30Min,
		"1h":  types.Kline1Hour,
		"2h":  types.Kline2Hour,
		"4h":  types.Kline4Hour,
		"1d":  types.KlineDay,
		"1w":  types.KlineWeek,
		"1M":  types.KlineMonth,
		"1y":  types.KlineYear,
	}

	for _, typeStr := range s.config.Recent.SupportedTypes {
		if klineType, exists := typeMap[typeStr]; exists {
			supportedTypes = append(supportedTypes, klineType)
		} else {
			s.logger.Warnf("Unknown kline type in config: %s", typeStr)
		}
	}

	// 如果解析后没有有效的类型，使用默认值
	if len(supportedTypes) == 0 {
		s.logger.Warn("No valid kline types found in config, using defaults")
		return defaultTypes
	}

	return supportedTypes
}

// incrementErrorCount 增加错误计数
func (s *RecentKlineService) incrementErrorCount() {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	s.errorCount++
}

// getKlineTypeNames 获取K线类型名称列表
func (s *RecentKlineService) getKlineTypeNames(klineTypes []types.KlineType) []string {
	var names []string
	for _, klineType := range klineTypes {
		names = append(names, klineType.String())
	}
	return names
}

// getBatchSize 获取批处理大小
func (s *RecentKlineService) getBatchSize() int {
	batchSize := s.config.Recent.InitialLoadBatchSize
	if batchSize <= 0 {
		batchSize = 5 // 默认值
	}
	return batchSize
}

// performInitialLoad 执行初始化加载
func (s *RecentKlineService) performInitialLoad() {
	overallStartTime := time.Now()
	s.logger.Info("=== Starting initial kline data load ===")

	// 获取初始化加载条数，如果未配置则使用默认值500
	initialCount := s.config.Recent.InitialLoadCount
	if initialCount <= 0 {
		initialCount = 500
	}

	// 获取所有支持的交易品种
	allCodes := s.getAllSupportedCodes()
	if len(allCodes) == 0 {
		s.logger.Warn("No supported symbols found for initial load")
		return
	}

	// 获取配置的K线类型
	klineTypes := s.getSupportedKlineTypes()

	// 输出初始化概览信息
	s.logger.Infof("Initial load configuration:")
	s.logger.Infof("  - Total symbols: %d (%v)", len(allCodes), allCodes)
	s.logger.Infof("  - K-line types: %d (%v)", len(klineTypes), s.getKlineTypeNames(klineTypes))
	s.logger.Infof("  - Periods per symbol: %d", initialCount)
	s.logger.Infof("  - Batch size: %d", s.getBatchSize())
	s.logger.Infof("  - Expected total data points: %d", len(allCodes)*len(klineTypes)*initialCount)

	// 顺序加载不同K线类型的初始数据
	successCount, errorCount := s.loadInitialKlineTypesSequential(allCodes, klineTypes, initialCount)

	// 标记初始化完成
	s.mutex.Lock()
	s.isInitialized = true
	s.mutex.Unlock()

	overallDuration := time.Since(overallStartTime)
	s.logger.Infof("=== Initial kline data load completed ===")
	s.logger.Infof("  - Success: %d", successCount)
	s.logger.Infof("  - Errors: %d", errorCount)
	s.logger.Infof("  - Total duration: %v", overallDuration)
	s.logger.Infof("  - Average per symbol: %v", time.Duration(int64(overallDuration)/int64(len(allCodes))))
	if successCount > 0 {
		s.logger.Infof("  - Success rate: %.1f%%", float64(successCount)/float64(successCount+errorCount)*100)
	}
}

// loadInitialKlineTypesSequential 顺序加载不同K线类型的初始数据
func (s *RecentKlineService) loadInitialKlineTypesSequential(codes []string, klineTypes []types.KlineType, count int) (int, int) {
	if len(klineTypes) == 0 {
		return 0, 0
	}

	s.logger.Infof("Starting sequential load for %d K-line types...", len(klineTypes))

	totalSuccessCount := 0
	totalErrorCount := 0

	// 顺序处理每个K线类型
	for _, klineType := range klineTypes {
		startTime := time.Now()
		successCount, errorCount, err := s.loadInitialKlineTypeWithReturn(codes, klineType, count)
		duration := time.Since(startTime)

		totalSuccessCount += successCount
		totalErrorCount += errorCount

		if err != nil {
			s.logger.Errorf("Failed to load initial %s klines: %v (duration: %v)", klineType.String(), err, duration)
		} else {
			s.logger.Infof("✓ %s klines loaded: %d success, %d errors (duration: %v)", klineType.String(), successCount, errorCount, duration)
		}

		// // 在K线类型之间添加短暂延迟，避免请求过于频繁
		// if len(klineTypes) > 1 {
		// 	time.Sleep(500 * time.Millisecond)
		// }
	}

	s.logger.Infof("Sequential load completed: %d total success, %d total errors", totalSuccessCount, totalErrorCount)
	return totalSuccessCount, totalErrorCount
}

// loadInitialKlineTypeWithReturn 加载指定类型的初始K线数据并返回统计信息
func (s *RecentKlineService) loadInitialKlineTypeWithReturn(codes []string, klineType types.KlineType, count int) (int, int, error) {
	// 检查context是否已经取消
	select {
	case <-s.ctx.Done():
		return 0, 0, s.ctx.Err()
	default:
	}

	// 获取批处理大小配置，默认为5个品种一批
	batchSize := s.getBatchSize()

	// 分批处理品种
	totalCodes := len(codes)
	successCount := 0
	errorCount := 0

	s.logger.Infof("Loading initial %s kline data for %d symbols in batches of %d",
		klineType.String(), totalCodes, batchSize)

	// 分批顺序处理
	batchIndex := 0
	for i := 0; i < totalCodes; i += batchSize {
		end := i + batchSize
		if end > totalCodes {
			end = totalCodes
		}

		batchCodes := codes[i:end]

		batchSuccess, batchErrors, err := s.loadInitialKlineTypeBatch(batchCodes, klineType, count)
		successCount += batchSuccess
		errorCount += batchErrors

		if err != nil {
			s.logger.Errorf("Batch %d failed for %s kline: %v", batchIndex, klineType.String(), err)
		} else {
			s.logger.Debugf("Batch %d completed for %s kline: %d success, %d errors",
				batchIndex, klineType.String(), batchSuccess, batchErrors)
		}

		batchIndex++

		// // 在批次之间添加短暂延迟，避免请求过于频繁
		// if i+batchSize < totalCodes {
		// 	time.Sleep(200 * time.Millisecond)
		// }
	}

	s.logger.Infof("Initial %s kline load completed: %d success, %d errors out of %d symbols",
		klineType.String(), successCount, errorCount, totalCodes)

	return successCount, errorCount, nil
}

// loadInitialKlineTypeBatch 批量加载指定类型的初始K线数据
func (s *RecentKlineService) loadInitialKlineTypeBatch(codes []string, klineType types.KlineType, count int) (int, int, error) {
	// 检查context是否已经取消
	select {
	case <-s.ctx.Done():
		return 0, 0, s.ctx.Err()
	default:
	}

	if len(codes) == 0 {
		return 0, 0, nil
	}

	// 构建请求
	requests := make([]types.KlineRequest, 0, len(codes))
	for _, code := range codes {
		requests = append(requests, types.KlineRequest{
			Code:       code,
			Count:      count,
			AdjustType: 1, // 复权
			KlineType:  klineType,
		})
	}

	requestStartTime := time.Now()
	s.logger.Debugf("Requesting initial %s kline data for %d symbols: %v",
		klineType.String(), len(codes), codes)

	// 批量获取K线数据，带重试机制
	var klineDataList []types.KlineData
	var err error
	maxRetries := 3

	for retry := 0; retry < maxRetries; retry++ {
		apiCallStart := time.Now()
		klineDataList, err = s.qosClient.GetKline(requests)
		apiCallDuration := time.Since(apiCallStart)

		if err == nil {
			s.logger.Debugf("QOS API call successful for %s kline batch (duration: %v)",
				klineType.String(), apiCallDuration)
			break
		}

		// 检查是否是因为数据过大导致的错误
		if strings.Contains(err.Error(), "response data truncated") ||
			strings.Contains(err.Error(), "unexpected end of JSON input") {
			s.logger.Warnf("Batch size too large for %s kline, reducing batch size and retrying (attempt %d/%d)",
				klineType.String(), retry+1, maxRetries)

			// 如果批次大小大于1，尝试分成更小的批次
			if len(codes) > 1 {
				return s.loadInitialKlineTypeBatchRecursive(codes, klineType, count)
			}
		}

		if retry < maxRetries-1 {
			s.logger.Warnf("Failed to get initial kline data batch for %s (attempt %d/%d, duration: %v): %v, retrying...",
				klineType.String(), retry+1, maxRetries, apiCallDuration, err)
			time.Sleep(time.Duration(retry+1) * time.Second) // 递增延迟
		}
	}

	if err != nil {
		s.logger.Errorf("Failed to get initial kline data batch for %s after %d attempts: %v",
			klineType.String(), maxRetries, err)
		return 0, len(codes), fmt.Errorf("failed to get initial kline data from QOS API after %d attempts: %w", maxRetries, err)
	}

	requestDuration := time.Since(requestStartTime)

	// 检查返回的数据是否为空
	if len(klineDataList) == 0 {
		s.logger.Warnf("QOS API returned empty kline data list for initial load batch of %d requests (%s, duration: %v)",
			len(requests), klineType.String(), requestDuration)
		return 0, 0, nil
	}

	// 处理每个品种的K线数据
	processStartTime := time.Now()
	successCount := 0
	errorCount := 0
	totalDataPoints := 0

	for _, klineData := range klineDataList {
		if err := s.processInitialKlineData(&klineData, klineType); err != nil {
			s.logger.Errorf("Failed to process initial kline data for %s (%s): %v",
				klineData.Code, klineType.String(), err)
			errorCount++
			continue
		}
		successCount++
		totalDataPoints += len(klineData.Klines)

		// 记录每个品种的数据范围
		if len(klineData.Klines) > 0 {
			firstKline := klineData.Klines[0]
			lastKline := klineData.Klines[len(klineData.Klines)-1]
			s.logger.Debugf("Loaded %s %s klines: %d periods from %s to %s",
				klineData.Code, klineType.String(), len(klineData.Klines),
				time.Unix(firstKline.Timestamp, 0).Format("2006-01-02 15:04:05"),
				time.Unix(lastKline.Timestamp, 0).Format("2006-01-02 15:04:05"))
		}
	}

	// 检查是否有品种没有返回数据
	returnedCodes := make(map[string]bool)
	for _, klineData := range klineDataList {
		returnedCodes[klineData.Code] = true
	}

	for _, code := range codes {
		if !returnedCodes[code] {
			s.logger.Warnf("No kline data returned for symbol %s (%s)", code, klineType.String())
			errorCount++
		}
	}

	processDuration := time.Since(processStartTime)
	s.logger.Debugf("Batch completed for %s kline: %d success, %d errors, %d data points (request: %v, process: %v, total: %v)",
		klineType.String(), successCount, errorCount, totalDataPoints, requestDuration, processDuration, requestDuration+processDuration)

	return successCount, errorCount, nil
}

// loadInitialKlineTypeBatchRecursive 递归处理过大的批次，将其分成更小的批次
func (s *RecentKlineService) loadInitialKlineTypeBatchRecursive(codes []string, klineType types.KlineType, count int) (int, int, error) {
	if len(codes) <= 1 {
		// 如果只有一个品种还是失败，直接返回错误
		return 0, 1, fmt.Errorf("single symbol request failed for %s", codes[0])
	}

	// 将批次分成两半
	mid := len(codes) / 2
	firstHalf := codes[:mid]
	secondHalf := codes[mid:]

	s.logger.Infof("Splitting batch for %s kline: %d symbols -> %d + %d symbols",
		klineType.String(), len(codes), len(firstHalf), len(secondHalf))

	// 顺序处理两个子批次
	totalSuccess := 0
	totalErrors := 0
	var lastErr error

	// 处理第一半
	success1, errors1, err1 := s.loadInitialKlineTypeBatch(firstHalf, klineType, count)
	totalSuccess += success1
	totalErrors += errors1
	if err1 != nil {
		lastErr = err1
	}

	// 在两个子批次之间添加延迟
	time.Sleep(300 * time.Millisecond)

	// 处理第二半
	success2, errors2, err2 := s.loadInitialKlineTypeBatch(secondHalf, klineType, count)
	totalSuccess += success2
	totalErrors += errors2
	if err2 != nil {
		lastErr = err2
	}

	s.logger.Infof("Recursive batch completed for %s kline: %d success, %d errors",
		klineType.String(), totalSuccess, totalErrors)

	return totalSuccess, totalErrors, lastErr
}

// processInitialKlineData 处理初始K线数据（应用修复并缓存）
func (s *RecentKlineService) processInitialKlineData(klineData *types.KlineData, klineType types.KlineType) error {
	if klineData == nil || len(klineData.Klines) == 0 {
		return nil
	}

	// 应用K线修复
	correctedData := s.corrector.ApplyCorrections(klineData)

	// 缓存数据（初始化时不限制数量）
	cacheKey := types.GetCacheKey("recent_kline", correctedData.Code, klineType.String())
	dataType := "kline_" + klineType.String()

	if err := s.cacheManager.Set(cacheKey, *correctedData, dataType); err != nil {
		return err
	}

	// 记录数据加载时间
	s.mutex.Lock()
	s.dataLoadTime[cacheKey] = time.Now()
	s.mutex.Unlock()

	s.logger.Debugf("Cached initial kline data for %s (%s): %d periods",
		correctedData.Code, klineType.String(), len(correctedData.Klines))

	return nil
}

// startIntegrityChecker 启动完整性检查定时器
func (s *RecentKlineService) startIntegrityChecker() {
	// 获取完整性检查间隔，如果未配置则使用默认值5分钟
	checkInterval := s.config.Recent.IntegrityCheckInterval
	if checkInterval <= 0 {
		checkInterval = 5 * time.Minute
	}

	s.mutex.Lock()
	s.integrityTicker = time.NewTicker(checkInterval)
	s.mutex.Unlock()

	// 启动完整性检查协程
	s.wg.Add(1)
	go s.integrityCheckLoop()

	s.logger.Infof("Integrity checker started with interval: %v", checkInterval)
}

// integrityCheckLoop 完整性检查循环
func (s *RecentKlineService) integrityCheckLoop() {
	defer s.wg.Done()

	for {
		select {
		case <-s.ctx.Done():
			return
		case <-s.integrityTicker.C:
			s.performIntegrityCheck()
		}
	}
}

// performIntegrityCheck 执行完整性检查
func (s *RecentKlineService) performIntegrityCheck() {
	s.logger.Debug("Starting integrity check...")
	startTime := time.Now()

	// 获取数据最大存活时间，如果未配置则使用默认值30分钟
	maxDataAge := s.config.Recent.MaxDataAge
	if maxDataAge <= 0 {
		maxDataAge = 30 * time.Minute
	}

	// 检查是否已初始化
	s.mutex.RLock()
	initialized := s.isInitialized
	s.mutex.RUnlock()

	if !initialized {
		s.logger.Debug("Service not initialized yet, skipping integrity check")
		return
	}

	// 获取所有支持的交易品种和K线类型
	allCodes := s.getAllSupportedCodes()
	klineTypes := s.getSupportedKlineTypes()

	reloadCount := 0
	checkCount := 0

	// 检查每个缓存键的数据完整性和新鲜度
	for _, code := range allCodes {
		for _, klineType := range klineTypes {
			cacheKey := types.GetCacheKey("recent_kline", code, klineType.String())
			checkCount++

			// 检查缓存是否存在
			if _, found := s.cacheManager.Get(cacheKey); !found {
				s.logger.Warnf("Missing cache data for %s, reloading...", cacheKey)
				s.reloadSingleKlineData(code, klineType)
				reloadCount++
				continue
			}

			// 检查数据年龄
			s.mutex.RLock()
			loadTime, exists := s.dataLoadTime[cacheKey]
			s.mutex.RUnlock()

			if !exists || time.Since(loadTime) > maxDataAge {
				s.logger.Infof("Data for %s is too old (age: %v), reloading...", cacheKey, time.Since(loadTime))
				s.reloadSingleKlineData(code, klineType)
				reloadCount++
			}
		}
	}

	duration := time.Since(startTime)
	s.logger.Infof("Integrity check completed: checked %d items, reloaded %d items, duration: %v",
		checkCount, reloadCount, duration)
}

// reloadSingleKlineData 重新加载单个K线数据
func (s *RecentKlineService) reloadSingleKlineData(code string, klineType types.KlineType) {
	// 获取初始化加载条数
	initialCount := s.config.Recent.InitialLoadCount
	if initialCount <= 0 {
		initialCount = 500
	}

	// 构建请求
	request := types.KlineRequest{
		Code:       code,
		Count:      initialCount,
		AdjustType: 1, // 前复权
		KlineType:  klineType,
	}

	// 获取K线数据
	klineDataList, err := s.qosClient.GetKline([]types.KlineRequest{request})
	if err != nil {
		s.logger.Errorf("Failed to reload kline data for %s (%s): %v", code, klineType.String(), err)
		return
	}

	// 检查返回的数据是否为空
	if len(klineDataList) == 0 {
		s.logger.Warnf("QOS API returned empty kline data for reload request: %s (%s)", code, klineType.String())
		return
	}

	// 处理数据
	if err := s.processInitialKlineData(&klineDataList[0], klineType); err != nil {
		s.logger.Errorf("Failed to process reloaded kline data for %s (%s): %v", code, klineType.String(), err)
	} else {
		s.logger.Infof("Successfully reloaded kline data for %s (%s)", code, klineType.String())
	}
}

// mergeKlineData 合并K线数据
func (s *RecentKlineService) mergeKlineData(existing *types.KlineData, newData *types.KlineData) *types.KlineData {
	if existing == nil || len(existing.Klines) == 0 {
		return newData
	}
	if newData == nil || len(newData.Klines) == 0 {
		return existing
	}

	// 创建时间戳到K线的映射，用于去重和更新
	klineMap := make(map[int64]*types.Kline)

	// 先添加现有数据
	for i := range existing.Klines {
		kline := &existing.Klines[i]
		klineMap[kline.Timestamp] = kline
	}

	// 添加或更新新数据
	for i := range newData.Klines {
		kline := &newData.Klines[i]
		klineMap[kline.Timestamp] = kline
	}

	// 将映射转换回切片并按时间戳排序
	var timestamps []int64
	for timestamp := range klineMap {
		timestamps = append(timestamps, timestamp)
	}

	// 排序时间戳
	for i := 0; i < len(timestamps)-1; i++ {
		for j := i + 1; j < len(timestamps); j++ {
			if timestamps[i] > timestamps[j] {
				timestamps[i], timestamps[j] = timestamps[j], timestamps[i]
			}
		}
	}

	// 构建合并后的K线数据
	mergedKlines := make([]types.Kline, 0, len(timestamps))
	for _, timestamp := range timestamps {
		mergedKlines = append(mergedKlines, *klineMap[timestamp])
	}

	return &types.KlineData{
		Code:   existing.Code,
		Klines: mergedKlines,
	}
}
