package service

import (
	"os"
	"sync"
	"testing"
	"time"

	"qos-market-api/internal/cache"
	"qos-market-api/internal/config"
	"qos-market-api/internal/correction"
	"qos-market-api/internal/logger"
	"qos-market-api/pkg/types"
)

// MockQOSClient 模拟QOS客户端
type MockQOSClient struct {
	connected bool
	delay     time.Duration
	callCount int
	mutex     sync.Mutex
}

func (m *MockQOSClient) GetKline(requests []types.KlineRequest) ([]types.KlineData, error) {
	m.mutex.Lock()
	m.callCount++
	m.mutex.Unlock()

	// 模拟网络延迟
	if m.delay > 0 {
		time.Sleep(m.delay)
	}

	// 返回模拟数据
	var results []types.KlineData
	for _, req := range requests {
		klines := make([]types.Kline, req.Count)
		for i := 0; i < req.Count; i++ {
			klines[i] = types.Kline{
				Code:       req.Code,
				OpenPrice:  "100.0",
				ClosePrice: "101.0",
				HighPrice:  "102.0",
				LowPrice:   "99.0",
				Volume:     "1000",
				Timestamp:  time.Now().Unix() - int64(i*60),
				KlineType:  req.KlineType,
			}
		}
		results = append(results, types.KlineData{
			Code:   req.Code,
			Klines: klines,
		})
	}
	return results, nil
}

func (m *MockQOSClient) IsConnected() bool {
	return m.connected
}

func (m *MockQOSClient) GetCallCount() int {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	return m.callCount
}

func (m *MockQOSClient) ResetCallCount() {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.callCount = 0
}

// TestParallelKlineUpdate 测试并行K线更新
func TestParallelKlineUpdate(t *testing.T) {
	// 创建模拟客户端，设置一定的延迟来模拟网络请求
	mockClient := &MockQOSClient{
		connected: true,
		delay:     100 * time.Millisecond, // 100ms延迟
	}

	// 创建日志器
	log, _ := logger.New(&config.LoggingConfig{
		Level:  "debug",
		Format: "text",
		Output: "stdout",
	})

	// 创建K线配置
	klineConfig := &config.KlineConfig{
		Recent: config.RecentKlineConfig{
			Enabled:        true,
			UpdateInterval: 30 * time.Second,
			MaxPeriods:     10,
			SupportedTypes: []string{"1m", "5m", "15m", "1h", "1d"}, // 5种类型
		},
	}

	// 创建缓存管理器
	cacheConfig := &config.CacheConfig{
		Memory: config.MemoryCacheConfig{
			MaxSize:         1000,
			TTL:             5 * time.Minute,
			CleanupInterval: time.Minute,
		},
	}
	cacheManager := cache.NewCacheManager(cacheConfig, klineConfig, log)

	// 创建修复器（测试用，创建临时数据库）
	tmpDB := "/tmp/test_corrections.db"
	os.Remove(tmpDB) // 清理可能存在的文件
	defer os.Remove(tmpDB)

	database, err := correction.NewDatabase(tmpDB, log)
	if err != nil {
		t.Fatalf("Failed to create test database: %v", err)
	}
	defer database.Close()

	cache := correction.NewCorrectionCache(database, time.Minute, log)
	defer cache.Stop()
	corrector := correction.NewCorrector(cache, log)

	// 创建交易品种配置
	symbolsConfig := &config.SymbolsConfig{
		USStocks: []string{"AAPL", "GOOGL"},
		HKStocks: []string{"00700"},
	}

	// 创建服务
	service := NewRecentKlineService(
		mockClient,
		cacheManager,
		corrector,
		klineConfig,
		symbolsConfig,
		log,
	)

	// 测试并行更新
	t.Run("ParallelUpdate", func(t *testing.T) {
		mockClient.ResetCallCount()

		// 获取所有支持的代码和K线类型
		allCodes := service.getAllSupportedCodes()
		klineTypes := service.getSupportedKlineTypes()

		t.Logf("Testing with %d codes and %d kline types", len(allCodes), len(klineTypes))

		// 记录开始时间
		startTime := time.Now()

		// 执行并行更新
		successCount, errorCount := service.updateKlineTypesParallel(allCodes, klineTypes)

		// 记录结束时间
		duration := time.Since(startTime)

		t.Logf("Parallel update completed: %d success, %d errors, duration: %v",
			successCount, errorCount, duration)

		// 验证结果
		if errorCount > 0 {
			t.Errorf("Expected no errors, got %d", errorCount)
		}

		if successCount != len(klineTypes) {
			t.Errorf("Expected %d successful updates, got %d", len(klineTypes), successCount)
		}

		// 验证API调用次数（应该等于K线类型数量，因为每种类型调用一次）
		callCount := mockClient.GetCallCount()
		if callCount != len(klineTypes) {
			t.Errorf("Expected %d API calls, got %d", len(klineTypes), callCount)
		}

		// 验证并行执行的效果：总时间应该接近单次调用时间，而不是所有调用时间的总和
		expectedSequentialTime := time.Duration(len(klineTypes)) * mockClient.delay
		if duration >= expectedSequentialTime {
			t.Errorf("Parallel execution took too long. Expected < %v, got %v",
				expectedSequentialTime, duration)
		}

		t.Logf("Performance improvement: Sequential would take ~%v, parallel took %v",
			expectedSequentialTime, duration)
	})

	// 测试空K线类型列表
	t.Run("EmptyKlineTypes", func(t *testing.T) {
		allCodes := service.getAllSupportedCodes()
		emptyTypes := []types.KlineType{}

		successCount, errorCount := service.updateKlineTypesParallel(allCodes, emptyTypes)

		if successCount != 0 || errorCount != 0 {
			t.Errorf("Expected 0 success and 0 errors for empty types, got %d success, %d errors",
				successCount, errorCount)
		}
	})
}

// TestSequentialVsParallelPerformance 性能对比测试
func TestSequentialVsParallelPerformance(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping performance test in short mode")
	}

	// 创建模拟客户端，设置较长的延迟来突出并行的优势
	mockClient := &MockQOSClient{
		connected: true,
		delay:     200 * time.Millisecond, // 200ms延迟
	}

	// 创建日志器
	log, _ := logger.New(&config.LoggingConfig{
		Level:  "error",
		Format: "text",
		Output: "stdout",
	})

	// 创建K线配置
	klineConfig := &config.KlineConfig{
		Recent: config.RecentKlineConfig{
			Enabled:        true,
			MaxPeriods:     10,
			SupportedTypes: []string{"1m", "5m", "15m", "30m", "1h", "1d"}, // 6种类型
		},
	}

	// 创建服务（简化配置）
	cacheManager := cache.NewCacheManager(&config.CacheConfig{
		Memory: config.MemoryCacheConfig{
			MaxSize:         1000,
			TTL:             5 * time.Minute,
			CleanupInterval: time.Minute,
		},
	}, klineConfig, log)

	// 创建修复器（测试用，创建临时数据库）
	tmpDB := "/tmp/test_corrections_perf.db"
	os.Remove(tmpDB) // 清理可能存在的文件
	defer os.Remove(tmpDB)

	database, err := correction.NewDatabase(tmpDB, log)
	if err != nil {
		t.Fatalf("Failed to create test database: %v", err)
	}
	defer database.Close()

	cache := correction.NewCorrectionCache(database, time.Minute, log)
	defer cache.Stop()
	corrector := correction.NewCorrector(cache, log)

	service := NewRecentKlineService(
		mockClient,
		cacheManager,
		corrector,
		klineConfig,
		&config.SymbolsConfig{
			USStocks: []string{"AAPL", "GOOGL", "MSFT"},
		},
		log,
	)

	allCodes := service.getAllSupportedCodes()
	klineTypes := service.getSupportedKlineTypes()

	// 测试并行更新
	mockClient.ResetCallCount()
	startTime := time.Now()
	successCount, errorCount := service.updateKlineTypesParallel(allCodes, klineTypes)
	parallelDuration := time.Since(startTime)

	t.Logf("Parallel update: %d success, %d errors, duration: %v",
		successCount, errorCount, parallelDuration)

	// 模拟顺序更新的时间（实际不执行，只是计算预期时间）
	expectedSequentialTime := time.Duration(len(klineTypes)) * mockClient.delay

	// 计算性能提升
	if parallelDuration > 0 {
		speedup := float64(expectedSequentialTime) / float64(parallelDuration)
		t.Logf("Performance improvement: %.2fx speedup (expected sequential: %v, parallel: %v)",
			speedup, expectedSequentialTime, parallelDuration)

		// 验证并行确实比顺序快
		if parallelDuration >= expectedSequentialTime {
			t.Errorf("Parallel execution should be faster than sequential")
		}
	}
}

// TestSequentialInitialization 测试初始化是否为顺序执行
func TestSequentialInitialization(t *testing.T) {
	// 创建测试配置
	cfg := &config.KlineConfig{
		Recent: config.RecentKlineConfig{
			Enabled:                true,
			UpdateInterval:         time.Minute,
			MaxPeriods:             100,
			InitialLoadCount:       10,
			RefreshCount:           5,
			InitialLoadBatchSize:   2,
			IntegrityCheckInterval: 5 * time.Minute,
			MaxDataAge:             30 * time.Minute,
			SupportedTypes:         []string{"1m", "5m", "1h"},
		},
	}

	symbols := &config.SymbolsConfig{
		USStocks: []string{"AAPL", "GOOGL"},
	}

	// 创建带延迟的模拟客户端来验证顺序执行
	mockClient := &MockQOSClient{
		connected: true,
		delay:     100 * time.Millisecond, // 每个请求延迟100ms
	}

	// 创建日志器
	log, _ := logger.New(&config.LoggingConfig{
		Level:  "info",
		Format: "text",
		Output: "stdout",
	})

	// 创建缓存管理器
	cacheConfig := &config.CacheConfig{
		Memory: config.MemoryCacheConfig{
			MaxSize:         1000,
			TTL:             5 * time.Minute,
			CleanupInterval: time.Minute,
		},
	}
	cacheManager := cache.NewCacheManager(cacheConfig, cfg, log)

	// 创建修复器（测试用，创建临时数据库）
	tmpDB := "/tmp/test_sequential_corrections.db"
	os.Remove(tmpDB) // 清理可能存在的文件
	defer os.Remove(tmpDB)

	database, err := correction.NewDatabase(tmpDB, log)
	if err != nil {
		t.Fatalf("Failed to create test database: %v", err)
	}
	defer database.Close()

	cache := correction.NewCorrectionCache(database, time.Minute, log)
	corrector := correction.NewCorrector(cache, log)

	// 创建服务
	service := NewRecentKlineService(
		mockClient,
		cacheManager,
		corrector,
		cfg,
		symbols,
		log,
	)

	// 记录开始时间
	startTime := time.Now()

	// 执行初始化加载
	service.performInitialLoad()

	// 记录结束时间
	duration := time.Since(startTime)

	// 验证是否已初始化
	if !service.IsInitialized() {
		t.Error("Service should be initialized after performInitialLoad")
	}

	// 验证调用次数（3个K线类型，每个类型一次批量调用 = 3次调用）
	expectedCalls := 3
	actualCalls := mockClient.GetCallCount()
	if actualCalls != expectedCalls {
		t.Errorf("Expected %d API calls, got %d", expectedCalls, actualCalls)
	}

	// 验证执行时间（顺序执行应该比并行执行慢）
	// 3个K线类型 * 100ms延迟 + 2个K线类型间的500ms延迟 = 至少1300ms
	expectedMinDuration := 3*100*time.Millisecond + 2*500*time.Millisecond
	if duration < expectedMinDuration {
		t.Errorf("Duration too short for sequential execution: expected at least %v, got %v", expectedMinDuration, duration)
	}

	t.Logf("Sequential initialization completed in %v (expected minimum: %v)", duration, expectedMinDuration)
}
