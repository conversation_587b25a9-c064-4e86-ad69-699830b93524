package types

// MarketType 市场类型
type MarketType string

const (
	MarketUS MarketType = "US" // 美股
	MarketHK MarketType = "HK" // 港股
	MarketSH MarketType = "SH" // 上海A股
	MarketSZ MarketType = "SZ" // 深圳A股
	MarketCF MarketType = "CF" // 数字货币
	MarketFX MarketType = "FX" // 外汇
	MarketIX MarketType = "IX" // 指数
	MarketCM MarketType = "CM" // 商品
)

// KlineType K线类型
type KlineType int

const (
	KlineAll   KlineType = 0    // 所有K线类型（用于修复）
	Kline1Min  KlineType = 1    // 1分钟
	Kline5Min  KlineType = 5    // 5分钟
	Kline15Min KlineType = 15   // 15分钟
	Kline30Min KlineType = 30   // 30分钟
	Kline1Hour KlineType = 60   // 1小时
	Kline2Hour KlineType = 120  // 2小时
	Kline4Hour KlineType = 240  // 4小时
	KlineDay   KlineType = 1001 // 日线
	KlineWeek  KlineType = 1007 // 周线
	KlineMonth KlineType = 1030 // 月线
	KlineYear  KlineType = 2001 // 年线
)

// TradeDirection 交易方向
type TradeDirection int

const (
	TradeDirectionUnknown TradeDirection = 0 // 未知
	TradeDirectionBuy     TradeDirection = 1 // 买入
	TradeDirectionSell    TradeDirection = 2 // 卖出
)

// USSessionType 美股交易时段类型
type USSessionType int

const (
	USSessionUnknown   USSessionType = 0 // 未知
	USSessionNight     USSessionType = 1 // 夜盘
	USSessionPremarket USSessionType = 2 // 盘前
	USSessionIntraday  USSessionType = 3 // 盘中
	USSessionAfterHour USSessionType = 4 // 盘后
)

// Symbol 交易品种
type Symbol struct {
	Code   string     `json:"code"`   // 完整代码，如 "US:AAPL"
	Market MarketType `json:"market"` // 市场类型
	Name   string     `json:"name"`   // 品种名称
}

// InstrumentInfo 交易品种基础信息
type InstrumentInfo struct {
	Code              string `json:"c"`  // 股票代码
	Exchange          string `json:"e"`  // 交易所
	TradeCurrency     string `json:"tc"` // 交易币种
	ChineseName       string `json:"nc"` // 中文名称
	EnglishName       string `json:"ne"` // 英文名称
	LotSize           int    `json:"ls"` // 最小交易单位
	TotalShares       int64  `json:"ts"` // 总股本
	OutstandingShares int64  `json:"os"` // 流通股本
	EarningsPerShare  string `json:"ep"` // 每股盈利
	NetAssetPerShare  string `json:"na"` // 每股净资产
	DividendYield     string `json:"dy"` // 股息率
}

// Snapshot 实时行情快照
type Snapshot struct {
	Code           string `json:"c"`  // 股票代码
	LastPrice      string `json:"lp"` // 当前价格
	YesterdayPrice string `json:"yp"` // 昨日收盘价
	OpenPrice      string `json:"o"`  // 开盘价
	HighPrice      string `json:"h"`  // 最高价
	LowPrice       string `json:"l"`  // 最低价
	Timestamp      int64  `json:"ts"` // 时间戳-单位秒
	Volume         string `json:"v"`  // 成交量
	Turnover       string `json:"t"`  // 成交金额
	Suspended      int    `json:"s"`  // 是否停牌（0表示未停牌，1表示停牌）

	// 美股特有字段
	PremarketQuote *SessionQuote `json:"pq,omitempty"` // 盘前快照数据
	AfterHourQuote *SessionQuote `json:"aq,omitempty"` // 盘后快照数据
	NightQuote     *SessionQuote `json:"nq,omitempty"` // 夜盘快照数据
	TradingType    USSessionType `json:"tt,omitempty"` // 美股交易时段类型
}

// SessionQuote 美股交易时段行情
type SessionQuote struct {
	LastPrice      string `json:"lp"` // 当前价格
	YesterdayPrice string `json:"yp"` // 上次收盘价
	HighPrice      string `json:"h"`  // 最高价
	LowPrice       string `json:"l"`  // 最低价
	Timestamp      int64  `json:"ts"` // 时间戳-单位秒
	Volume         string `json:"v"`  // 成交量
	Turnover       string `json:"t"`  // 成交金额
}

// DepthItem 盘口深度项
type DepthItem struct {
	Price  string `json:"p"` // 价格
	Volume string `json:"v"` // 数量
}

// Depth 盘口深度
type Depth struct {
	Code      string      `json:"c"`  // 股票代码
	Bids      []DepthItem `json:"b"`  // 买单数组
	Asks      []DepthItem `json:"a"`  // 卖单数组
	Timestamp int64       `json:"ts"` // 时间戳-单位秒
}

// Trade 成交明细
type Trade struct {
	Code      string         `json:"c"`  // 股票代码
	Price     string         `json:"p"`  // 成交价格
	Volume    string         `json:"v"`  // 成交数量
	Direction TradeDirection `json:"d"`  // 交易方向
	Timestamp int64          `json:"ts"` // 时间戳-单位秒
}

// Kline K线数据
type Kline struct {
	Code       string    `json:"c"`  // 股票代码
	OpenPrice  string    `json:"o"`  // 开盘价
	ClosePrice string    `json:"cl"` // 收盘价
	HighPrice  string    `json:"h"`  // 最高价
	LowPrice   string    `json:"l"`  // 最低价
	Volume     string    `json:"v"`  // 成交量
	Timestamp  int64     `json:"ts"` // 时间戳-单位秒
	KlineType  KlineType `json:"kt"` // K线类型
}

// KlineData K线数据集合
type KlineData struct {
	Code   string  `json:"c"` // 股票代码
	Klines []Kline `json:"k"` // K线数组
}

// APIResponse API响应基础结构
type APIResponse struct {
	Message string      `json:"msg"`  // 响应消息
	Data    interface{} `json:"data"` // 响应数据
}

// WebSocketMessage WebSocket消息结构
type WebSocketMessage struct {
	Type    string      `json:"type"`            // 消息类型
	Message string      `json:"msg,omitempty"`   // 消息内容
	ReqID   int         `json:"reqid,omitempty"` // 请求ID
	Data    interface{} `json:"data,omitempty"`  // 数据内容
}

// SubscriptionRequest 订阅请求
type SubscriptionRequest struct {
	Type  string   `json:"type"`            // 订阅类型
	Codes []string `json:"codes"`           // 代码列表
	ReqID int      `json:"reqid,omitempty"` // 请求ID
}

// KlineRequest K线请求
type KlineRequest struct {
	Code       string    `json:"c"`           // 股票代码
	Count      int       `json:"co"`          // 请求数量
	AdjustType int       `json:"a"`           // 复权类型 0:不复权 1:前复权
	KlineType  KlineType `json:"kt"`          // K线类型
	EndTime    int64     `json:"e,omitempty"` // 结束时间戳
}

// KlineRequestMessage K线请求消息
type KlineRequestMessage struct {
	Type      string         `json:"type"`            // 请求类型
	KlineReqs []KlineRequest `json:"kline_reqs"`      // K线请求数组
	ReqID     int            `json:"reqid,omitempty"` // 请求ID
}

// GetMarketFromCode 从代码中提取市场类型
func GetMarketFromCode(code string) MarketType {
	if len(code) < 3 {
		return ""
	}

	marketStr := code[:2]
	switch marketStr {
	case "US":
		return MarketUS
	case "HK":
		return MarketHK
	case "SH":
		return MarketSH
	case "SZ":
		return MarketSZ
	case "CF":
		return MarketCF
	case "FX":
		return MarketFX
	case "IX":
		return MarketIX
	case "CM":
		return MarketCM
	default:
		return ""
	}
}

// GetSymbolFromCode 从代码中提取品种名称
func GetSymbolFromCode(code string) string {
	if len(code) < 4 {
		return code
	}
	return code[3:] // 跳过 "XX:" 前缀
}

// FormatCode 格式化代码
func FormatCode(market MarketType, symbol string) string {
	return string(market) + ":" + symbol
}

// GetCacheKey 获取缓存键
func GetCacheKey(dataType string, code string, params ...string) string {
	key := dataType + ":" + code
	for _, param := range params {
		key += ":" + param
	}
	return key
}

// KlineCorrection K线修复数据
type KlineCorrection struct {
	ID        int64  `json:"id" db:"id"`                 // 主键ID
	Code      string `json:"code" db:"code"`             // 股票代码
	Timestamp int64  `json:"timestamp" db:"timestamp"`   // 时间戳-单位秒
	Field     string `json:"field" db:"field"`           // 修复字段(o,cl,h,l,v)
	OrigValue string `json:"orig_value" db:"orig_value"` // 原始值
	CorrValue string `json:"corr_value" db:"corr_value"` // 修复值
	Reason    string `json:"reason" db:"reason"`         // 修复原因
	CreatedAt int64  `json:"created_at" db:"created_at"` // 创建时间
	UpdatedAt int64  `json:"updated_at" db:"updated_at"` // 更新时间
	IsActive  bool   `json:"is_active" db:"is_active"`   // 是否启用
}

// KlineCorrectionRequest 修复数据请求
type KlineCorrectionRequest struct {
	Code      string `json:"code" binding:"required"`       // 股票代码
	Timestamp int64  `json:"timestamp" binding:"required"`  // 时间戳
	Field     string `json:"field" binding:"required"`      // 修复字段
	CorrValue string `json:"corr_value" binding:"required"` // 修复值
	Reason    string `json:"reason"`                        // 修复原因
}

// GetKlineTypeString 获取K线类型字符串
func (kt KlineType) String() string {
	switch kt {
	case KlineAll:
		return "all"
	case Kline1Min:
		return "1m"
	case Kline5Min:
		return "5m"
	case Kline15Min:
		return "15m"
	case Kline30Min:
		return "30m"
	case Kline1Hour:
		return "1h"
	case Kline2Hour:
		return "2h"
	case Kline4Hour:
		return "4h"
	case KlineDay:
		return "1d"
	case KlineWeek:
		return "1w"
	case KlineMonth:
		return "1M"
	case KlineYear:
		return "1y"
	default:
		return "unknown"
	}
}
