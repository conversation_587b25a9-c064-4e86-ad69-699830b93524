version: '3.8'

services:
  qos-market-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: qos-market-api
    ports:
      - "8080:8080"  # HTTP API端口
      - "9090:9090"  # 监控端口
    volumes:
      - ./configs:/app/configs:ro
      - ./logs:/app/logs
    environment:
      - TZ=Asia/Shanghai
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - qos-network

  # 可选：添加Redis缓存（如果需要）
  # redis:
  #   image: redis:7-alpine
  #   container_name: qos-redis
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis_data:/data
  #   restart: unless-stopped
  #   networks:
  #     - qos-network

  # 可选：添加Prometheus监控
  # prometheus:
  #   image: prom/prometheus:latest
  #   container_name: qos-prometheus
  #   ports:
  #     - "9091:9090"
  #   volumes:
  #     - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
  #     - prometheus_data:/prometheus
  #   command:
  #     - '--config.file=/etc/prometheus/prometheus.yml'
  #     - '--storage.tsdb.path=/prometheus'
  #     - '--web.console.libraries=/etc/prometheus/console_libraries'
  #     - '--web.console.templates=/etc/prometheus/consoles'
  #     - '--storage.tsdb.retention.time=200h'
  #     - '--web.enable-lifecycle'
  #   restart: unless-stopped
  #   networks:
  #     - qos-network

  # 可选：添加Grafana仪表板
  # grafana:
  #   image: grafana/grafana:latest
  #   container_name: qos-grafana
  #   ports:
  #     - "3000:3000"
  #   volumes:
  #     - grafana_data:/var/lib/grafana
  #     - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
  #   environment:
  #     - GF_SECURITY_ADMIN_USER=admin
  #     - GF_SECURITY_ADMIN_PASSWORD=admin
  #     - GF_USERS_ALLOW_SIGN_UP=false
  #   restart: unless-stopped
  #   networks:
  #     - qos-network

networks:
  qos-network:
    driver: bridge

volumes:
  # redis_data:
  # prometheus_data:
  # grafana_data:
