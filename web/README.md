# QOS Market API Web 测试工具

这个目录包含了用于测试 QOS Market API 的 Web 界面工具。

## 可用工具

### 1. K线测试工具 (`kline_test.html`)

**访问地址**: `http://localhost:8080/kline-test`

**功能特性**:
- 🔍 支持多市场品种查询（美股、港股、A股、数字货币、外汇等）
- 📊 多种K线周期选择（1分钟到年线）
- 🕯️ 专业蜡烛图显示，多线图模拟蜡烛图效果
- 📋 详细数据表格，包含涨跌幅计算
- 🎨 美观的响应式界面设计
- ⚡ 快速品种代码选择
- 🔄 双视图切换（蜡烛图/数据表格）
- 💡 智能颜色区分涨跌趋势

**支持的交易品种**:
- **美股**: `US:AAPL` (苹果), `US:TSLA` (特斯拉), `US:MSFT` (微软)
- **港股**: `HK:700` (腾讯), `HK:9988` (阿里巴巴), `HK:1810` (小米)
- **A股**: `SH:600519` (贵州茅台), `SZ:000001` (平安银行)
- **数字货币**: `CF:BTCUSDT` (比特币), `CF:ETHUSDT` (以太坊)
- **外汇**: `FX:EURUSD` (欧元/美元)

**K线周期**:
- 分钟级: 1分钟, 5分钟, 15分钟, 30分钟
- 小时级: 1小时, 4小时
- 日级: 日线, 周线, 月线, 年线

**使用方法**:
1. 启动 QOS Market API 服务
2. 在浏览器中访问 `http://localhost:8080/kline-test`
3. 输入交易品种代码或点击示例代码
4. 选择K线周期和数据条数
5. 点击"获取K线数据"按钮
6. 查看K线数据：
   - **🕯️ 蜡烛图**: 专业的OHLC蜡烛图表，红绿颜色区分涨跌
   - **📈 线形图**: 收盘价、最高价、最低价的线形趋势图
   - **📋 数据表格**: 详细的数值数据，包含涨跌幅计算

### 2. 数据修复管理工具 (`corrections.html`)

**访问地址**: `http://localhost:8080/corrections`

**功能特性**:
- 📝 添加、编辑、删除K线数据修复记录
- 🔄 启用/禁用修复数据
- 📊 查看修复统计信息
- 🗂️ 修复数据列表管理

## 技术特点

### 前端技术
- 纯HTML/CSS/JavaScript实现，无需额外依赖
- 响应式设计，支持移动端访问
- 现代化UI设计，用户体验友好
- 实时数据展示和错误处理

### 后端集成
- 直接调用 QOS Market API 的 HTTP 接口
- 支持所有API参数配置
- 自动处理数据格式化和展示
- 完整的错误处理和用户反馈

### 数据展示

#### 🕯️ 蜡烛图表
- 专业的金融蜡烛图显示
- 红绿颜色自动区分涨跌
- 完整的OHLC（开高低收）数据展示
- 时间轴自适应格式化
- 交互式图表，支持缩放和悬停查看详情

#### 📈 线形图表
- 收盘价主线，显示价格趋势
- 最高价和最低价辅助线（虚线）
- 区域填充，直观显示价格波动
- 多条线图例说明

#### 📋 数据表格
- 详细的数值数据展示：
  - 时间戳（格式化显示）
  - 开盘价、收盘价、最高价、最低价
  - 成交量（智能单位转换K/M）
  - 涨跌额和涨跌幅（颜色区分）
- 实时计算涨跌幅和涨跌额
- 表格行悬停高亮效果

## 开发和扩展

### 添加新的测试工具
1. 在 `web/` 目录下创建新的 HTML 文件
2. 在 `internal/handler/http_handler.go` 中添加路由
3. 重新构建和启动服务

### 自定义样式
所有样式都内嵌在HTML文件中，可以直接修改CSS部分来自定义界面外观。

### API集成
测试工具直接调用以下API接口：
- `/api/v1/kline` - 获取K线数据
- `/api/v1/snapshot` - 获取实时快照
- `/api/v1/depth` - 获取盘口深度
- `/api/v1/instrument` - 获取品种信息

## 故障排除

### 常见问题

1. **无法访问测试页面**
   - 确保 QOS Market API 服务正在运行
   - 检查端口 8080 是否被占用
   - 确认 `web/` 目录下的文件存在

2. **无法获取数据**
   - 检查交易品种代码格式是否正确
   - 确认 QOS.HK API 密钥配置正确
   - 查看服务器日志了解详细错误信息

3. **数据显示异常**
   - 检查浏览器控制台是否有JavaScript错误
   - 确认API返回的数据格式正确
   - 尝试刷新页面重新获取数据

### 日志查看
服务器日志会显示所有API请求和响应信息，可以帮助诊断问题：
```bash
# 查看实时日志
tail -f logs/qos-market-api.log
```

## 更新历史

- **v1.0.0** - 初始版本，包含K线测试工具
- 支持多市场品种查询
- 响应式界面设计
- 完整的数据展示功能
