<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QOS Market API - K线测试工具</title>
    <script src="https://unpkg.com/lightweight-charts@4.1.3/dist/lightweight-charts.standalone.production.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .control-panel {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-row {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .form-col {
            flex: 1;
            min-width: 200px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        input, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        input:focus, select:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .results {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .results-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .results-content {
            padding: 20px;
        }

        .chart-container {
            position: relative;
            height: 500px;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        #klineChart {
            width: 100%;
            height: 100%;
        }

        .chart-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .chart-tab {
            padding: 8px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            transition: all 0.3s;
        }

        .chart-tab.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .chart-tab:hover {
            border-color: #667eea;
        }

        .kline-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .kline-table th,
        .kline-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #e9ecef;
        }

        .kline-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #555;
        }

        .kline-table tr:hover {
            background: #f8f9fa;
        }

        .positive {
            color: #28a745;
        }

        .negative {
            color: #dc3545;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }

        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }

        .symbol-examples {
            margin-top: 10px;
            font-size: 12px;
            color: #666;
        }

        .symbol-examples span {
            display: inline-block;
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
            margin: 2px;
            cursor: pointer;
        }

        .symbol-examples span:hover {
            background: #667eea;
            color: white;
        }

        .help-section {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 6px 6px 0;
        }

        .help-section h4 {
            margin: 0 0 10px 0;
            color: #667eea;
        }

        .help-section p {
            margin: 5px 0;
            font-size: 14px;
            color: #666;
        }

        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📈 QOS Market API</h1>
            <p>K线数据测试工具 - 支持多市场实时行情查询</p>
        </div>

        <div class="control-panel">
            <div class="form-group">
                <div class="form-row">
                    <div class="form-col">
                        <label for="symbol">交易品种代码</label>
                        <select id="symbol">
                            <option value="">请选择品种</option>
                        </select>
                        <div class="symbol-examples">
                            <span onclick="setSymbol('US:AAPL')">US:AAPL</span>
                            <span onclick="setSymbol('HK:700')">HK:700</span>
                            <span onclick="setSymbol('SH:600519')">SH:600519</span>
                            <span onclick="setSymbol('CF:BTCUSDT')">CF:BTCUSDT</span>
                            <span onclick="setSymbol('FX:EURUSD')">FX:EURUSD</span>
                        </div>
                    </div>
                    <div class="form-col">
                        <label for="klineType">K线周期</label>
                        <select id="klineType">
                            <option value="1">1分钟</option>
                            <option value="5">5分钟</option>
                            <option value="15">15分钟</option>
                            <option value="30">30分钟</option>
                            <option value="60">1小时</option>
                            <option value="1001" selected>日线</option>
                            <option value="1007">周线</option>
                            <option value="1030">月线</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="form-row">
                    <div class="form-col">
                        <label for="count">数据条数</label>
                        <input type="number" id="count" min="1" max="1000" value="20">
                    </div>
                    <div class="form-col">
                        <label for="adjust">复权类型</label>
                        <select id="adjust">
                            <option value="0">不复权</option>
                            <option value="1" selected>前复权</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <button class="btn" onclick="fetchKlineData()" id="fetchBtn">
                    📊 获取K线数据
                </button>
            </div>

            <div class="help-section">
                <h4>💡 使用提示</h4>
                <p><strong>🕯️ 专业蜡烛图</strong>：使用 TradingView 的 lightweight-charts 库，绿色表示上涨，红色表示下跌</p>
                <p><strong>📊 成交量显示</strong>：图表底部显示成交量柱状图，颜色与蜡烛图对应</p>
                <p><strong>📋 数据表格</strong>：详细数值数据，可查看具体的OHLC和成交量</p>
                <p><strong>⚡ 快速选择</strong>：点击品种代码示例可快速填入输入框</p>
                <p><strong>🔄 视图切换</strong>：在蜡烛图和数据表格之间切换查看</p>
                <p><strong>🎯 交互功能</strong>：支持缩放、平移、十字线等专业交易图表功能</p>
            </div>
        </div>

        <div class="results" id="results" style="display: none;">
            <div class="results-header">
                <h3 id="resultsTitle">K线数据</h3>
                <p id="resultsInfo"></p>
            </div>
            <div class="results-content" id="resultsContent">
                <!-- 图表选项卡 -->
                <div class="chart-tabs">
                    <div class="chart-tab active" onclick="switchTab('chart')">🕯️ 蜡烛图</div>
                    <div class="chart-tab" onclick="switchTab('table')">📋 数据表格</div>
                </div>

                <!-- 蜡烛图表 -->
                <div class="chart-container" id="chartContainer">
                    <div id="klineChart"></div>
                </div>

                <!-- 数据表格 -->
                <div id="tableContainer" style="display: none;">
                    <!-- 表格内容将在这里显示 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = window.location.origin;
        let klineChart = null;
        let candlestickSeries = null;
        let volumeSeries = null;
        let currentTab = 'chart';

        function setSymbol(symbol) {
            document.getElementById('symbol').value = symbol;
        }

        function switchTab(tab) {
            currentTab = tab;

            // 更新选项卡样式
            document.querySelectorAll('.chart-tab').forEach(t => t.classList.remove('active'));

            // 找到被点击的标签并添加active类
            const tabs = document.querySelectorAll('.chart-tab');
            tabs.forEach((t, index) => {
                if ((tab === 'chart' && index === 0) ||
                    (tab === 'table' && index === 1)) {
                    t.classList.add('active');
                }
            });

            // 显示/隐藏内容
            const chartContainer = document.getElementById('chartContainer');
            const tableContainer = document.getElementById('tableContainer');

            // 隐藏所有容器
            if (chartContainer) chartContainer.style.display = 'none';
            if (tableContainer) tableContainer.style.display = 'none';

            // 显示选中的容器
            if (tab === 'chart' && chartContainer) {
                chartContainer.style.display = 'block';
            } else if (tab === 'table' && tableContainer) {
                tableContainer.style.display = 'block';
            }
        }

        function showMessage(message, type = 'info') {
            const resultsDiv = document.getElementById('results');

            if (type === 'error') {
                // 对于错误消息，直接显示在结果区域
                resultsDiv.style.display = 'block';
                const contentDiv = document.getElementById('resultsContent');
                if (contentDiv) {
                    contentDiv.innerHTML = `<div class="${type}">${message}</div>`;
                }
            } else {
                // 对于成功消息，显示在信息区域
                const infoDiv = document.getElementById('resultsInfo');
                if (infoDiv) {
                    infoDiv.innerHTML = `<span class="${type}">${message}</span>`;

                    // 成功消息5秒后自动消失
                    if (type === 'success') {
                        setTimeout(() => {
                            const currentContent = infoDiv.innerHTML;
                            // 只有当前内容还是这条成功消息时才清除
                            if (currentContent.includes(message)) {
                                infoDiv.innerHTML = '';
                            }
                        }, 5000);
                    }
                }
            }
        }

        function formatNumber(num) {
            if (num === null || num === undefined || num === '') return '-';
            return parseFloat(num).toFixed(4);
        }

        function formatVolume(vol) {
            if (vol === null || vol === undefined || vol === '') return '-';
            const num = parseInt(vol);
            if (num >= 1000000) {
                return (num / 1000000).toFixed(2) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(2) + 'K';
            }
            return num.toString();
        }

        function formatTimestamp(ts) {
            if (!ts) return '-';
            const date = new Date(ts * 1000);
            return date.toLocaleString('zh-CN');
        }

        function calculateChange(open, close) {
            if (!open || !close) return { change: 0, changePercent: 0 };
            const change = parseFloat(close) - parseFloat(open);
            const changePercent = (change / parseFloat(open)) * 100;
            return { change, changePercent };
        }

        async function fetchKlineData() {
            const symbol = document.getElementById('symbol').value.trim();
            const klineType = document.getElementById('klineType').value;
            const count = document.getElementById('count').value;
            const adjust = document.getElementById('adjust').value;
            const fetchBtn = document.getElementById('fetchBtn');

            if (!symbol) {
                showMessage('请输入交易品种代码', 'error');
                return;
            }

            fetchBtn.disabled = true;
            fetchBtn.textContent = '🔄 获取中...';
            
            showMessage('正在获取K线数据，请稍候...', 'info');

            try {
                const url = `${API_BASE}/api/v1/kline?codes=${encodeURIComponent(symbol)}&kline_type=${klineType}&count=${count}&adjust=${adjust}`;
                console.log('Fetching:', url);
                
                const response = await fetch(url);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${data.msg || '请求失败'}`);
                }

                displayKlineData(data, symbol, klineType);
                
            } catch (error) {
                console.error('Error:', error);
                showMessage(`获取数据失败: ${error.message}`, 'error');
            } finally {
                fetchBtn.disabled = false;
                fetchBtn.textContent = '📊 获取K线数据';
            }
        }

        function displayKlineData(data, symbol, klineType) {
            const resultsDiv = document.getElementById('results');
            const titleDiv = document.getElementById('resultsTitle');
            const infoDiv = document.getElementById('resultsInfo');

            if (data.msg !== 'OK' || !data.data || data.data.length === 0) {
                showMessage('未获取到数据，请检查交易品种代码是否正确', 'error');
                return;
            }

            const klineData = data.data[0];
            if (!klineData.k || klineData.k.length === 0) {
                showMessage('该品种暂无K线数据', 'error');
                return;
            }

            const klineTypeNames = {
                '1': '1分钟', '5': '5分钟', '15': '15分钟', '30': '30分钟',
                '60': '1小时', '240': '4小时', '1001': '日线', '1007': '周线', '1030': '月线'
            };

            // 先显示结果容器
            resultsDiv.style.display = 'block';

            titleDiv.textContent = `${symbol} - ${klineTypeNames[klineType] || klineType}`;
            infoDiv.textContent = `共 ${klineData.k.length} 条数据`;

            // 等待DOM更新后再创建图表
            setTimeout(() => {
                try {
                    // 创建数据表格
                    createDataTable(klineData.k);

                    // 创建K线图表
                    createKlineChart(klineData.k, symbol, klineTypeNames[klineType] || klineType);

                    showMessage(`成功获取 ${klineData.k.length} 条K线数据`, 'success');
                } catch (error) {
                    console.error('创建图表时出错:', error);
                    // 显示错误但不覆盖已有内容
                    const infoDiv = document.getElementById('resultsInfo');
                    if (infoDiv) {
                        infoDiv.innerHTML = `<span class="error">图表创建失败: ${error.message}</span>`;
                    }
                }
            }, 100);
        }

        function createKlineChart(klineData, symbol, typeName) {
            const chartContainer = document.getElementById('klineChart');
            if (!chartContainer) {
                console.error('找不到K线图容器元素');
                return;
            }

            // 销毁现有图表
            if (klineChart) {
                klineChart.remove();
                klineChart = null;
                candlestickSeries = null;
                volumeSeries = null;
            }

            // 清空容器
            chartContainer.innerHTML = '';

            // 准备蜡烛图数据，确保时间排序
            const candlestickData = klineData
                .map(item => ({
                    time: item.ts, // lightweight-charts 使用时间戳（秒）
                    open: parseFloat(item.o),
                    high: parseFloat(item.h),
                    low: parseFloat(item.l),
                    close: parseFloat(item.cl)
                }))
                .sort((a, b) => a.time - b.time); // 按时间排序

            // 准备成交量数据
            const volumeData = klineData
                .map(item => ({
                    time: item.ts,
                    value: parseFloat(item.v || 0),
                    color: parseFloat(item.cl) >= parseFloat(item.o) ?
                        'rgba(38, 166, 154, 0.8)' : 'rgba(239, 83, 80, 0.8)'
                }))
                .sort((a, b) => a.time - b.time);

            // 使用 TradingView lightweight-charts 创建图表
            createLightweightChart(chartContainer, candlestickData, volumeData, symbol, typeName);
        }

        function createLightweightChart(container, candlestickData, volumeData, symbol, typeName) {
            try {
                // 检查 LightweightCharts 是否可用
                if (typeof LightweightCharts === 'undefined') {
                    throw new Error('TradingView lightweight-charts 库未加载');
                }

                console.log('开始创建图表，数据条数:', candlestickData.length);
                console.log('样本数据:', candlestickData.slice(0, 2));

                // 创建图表
                klineChart = LightweightCharts.createChart(container, {
                    width: container.clientWidth,
                    height: container.clientHeight,
                    layout: {
                        backgroundColor: '#ffffff',
                        textColor: '#333',
                        fontSize: 12,
                        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                    },
                    grid: {
                        vertLines: {
                            color: '#e1e5e9',
                        },
                        horzLines: {
                            color: '#e1e5e9',
                        },
                    },
                    crosshair: {
                        mode: LightweightCharts.CrosshairMode.Normal,
                    },
                    rightPriceScale: {
                        borderColor: '#e1e5e9',
                        scaleMargins: {
                            top: 0.1,
                            bottom: 0.3,
                        },
                    },
                    timeScale: {
                        borderColor: '#e1e5e9',
                        timeVisible: true,
                        secondsVisible: false,
                    },
                    watermark: {
                        visible: true,
                        fontSize: 24,
                        horzAlign: 'center',
                        vertAlign: 'center',
                        color: 'rgba(171, 71, 188, 0.3)',
                        text: `${symbol} - ${typeName}`,
                    },
                });

                console.log('图表容器创建成功');

                // 检查 addCandlestickSeries 方法是否存在
                if (typeof klineChart.addCandlestickSeries !== 'function') {
                    console.error('addCandlestickSeries 方法不存在，可用方法:', Object.getOwnPropertyNames(klineChart));
                    throw new Error('addCandlestickSeries 方法不可用，可能是库版本问题');
                }

                // 添加蜡烛图系列
                candlestickSeries = klineChart.addCandlestickSeries({
                    upColor: '#26a69a',      // 上涨蜡烛颜色（绿色）
                    downColor: '#ef5350',    // 下跌蜡烛颜色（红色）
                    borderUpColor: '#26a69a',
                    borderDownColor: '#ef5350',
                    wickUpColor: '#26a69a',
                    wickDownColor: '#ef5350',
                    priceFormat: {
                        type: 'price',
                        precision: 4,
                        minMove: 0.0001,
                    },
                });

                console.log('蜡烛图系列创建成功');

                // 设置蜡烛图数据
                candlestickSeries.setData(candlestickData);
                console.log('蜡烛图数据设置成功');

                // 添加成交量系列（如果有成交量数据）
                if (volumeData && volumeData.length > 0 && volumeData.some(d => d.value > 0)) {
                    console.log('开始添加成交量数据');
                    volumeSeries = klineChart.addHistogramSeries({
                        color: '#26a69a',
                        priceFormat: {
                            type: 'volume',
                        },
                        priceScaleId: 'volume',
                        scaleMargins: {
                            top: 0.7,
                            bottom: 0,
                        },
                    });

                    // 设置成交量数据
                    volumeSeries.setData(volumeData);
                    console.log('成交量数据设置成功');

                    // 创建成交量价格刻度
                    klineChart.priceScale('volume').applyOptions({
                        scaleMargins: {
                            top: 0.7,
                            bottom: 0,
                        },
                    });
                }

                // 自适应内容
                klineChart.timeScale().fitContent();

                // 响应式处理
                if (typeof ResizeObserver !== 'undefined') {
                    const resizeObserver = new ResizeObserver(entries => {
                        if (klineChart && entries.length > 0) {
                            const { width, height } = entries[0].contentRect;
                            klineChart.applyOptions({ width, height });
                        }
                    });
                    resizeObserver.observe(container);
                }

                console.log('TradingView lightweight-charts 图表创建成功');

            } catch (error) {
                console.error('TradingView 图表创建失败:', error);
                console.error('错误堆栈:', error.stack);

                // 显示错误信息
                container.innerHTML = `
                    <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #dc3545; text-align: center;">
                        <div>
                            <h4>图表加载失败</h4>
                            <p>错误信息: ${error.message}</p>
                            <p>请检查网络连接并刷新页面重试</p>
                            <small>如果问题持续存在，请检查浏览器控制台获取详细错误信息</small>
                        </div>
                    </div>
                `;
            }
        }





        function createDataTable(klineData) {
            const tableContainer = document.getElementById('tableContainer');
            if (!tableContainer) {
                console.error('找不到tableContainer元素');
                return;
            }

            let tableHTML = `
                <table class="kline-table">
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>开盘价</th>
                            <th>收盘价</th>
                            <th>最高价</th>
                            <th>最低价</th>
                            <th>成交量</th>
                            <th>涨跌额</th>
                            <th>涨跌幅</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            klineData.forEach(item => {
                const { change, changePercent } = calculateChange(item.o, item.cl);
                const changeClass = change >= 0 ? 'positive' : 'negative';

                tableHTML += `
                    <tr>
                        <td>${formatTimestamp(item.ts)}</td>
                        <td>${formatNumber(item.o)}</td>
                        <td class="${changeClass}">${formatNumber(item.cl)}</td>
                        <td>${formatNumber(item.h)}</td>
                        <td>${formatNumber(item.l)}</td>
                        <td>${formatVolume(item.v)}</td>
                        <td class="${changeClass}">${change >= 0 ? '+' : ''}${formatNumber(change)}</td>
                        <td class="${changeClass}">${changePercent >= 0 ? '+' : ''}${changePercent.toFixed(2)}%</td>
                    </tr>
                `;
            });

            tableHTML += '</tbody></table>';
            tableContainer.innerHTML = tableHTML;
        }

        // 加载支持的品种列表
        async function loadSupportedSymbols() {
            try {
                const response = await fetch('/api/v1/symbols');
                const result = await response.json();

                if (response.ok && result.msg === 'OK') {
                    const symbolSelect = document.getElementById('symbol');
                    symbolSelect.innerHTML = '<option value="">请选择品种</option>';

                    result.data.forEach(symbol => {
                        const option = document.createElement('option');
                        option.value = symbol;
                        option.textContent = symbol;
                        symbolSelect.appendChild(option);
                    });

                    // 设置默认选中值
                    symbolSelect.value = 'US:AAPL';
                } else {
                    console.error('Failed to load supported symbols:', result);
                }
            } catch (error) {
                console.error('Error loading supported symbols:', error);
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('K线测试工具已加载');

            // 加载支持的品种列表
            loadSupportedSymbols();

            // 检查 TradingView lightweight-charts 是否正确加载
            if (typeof LightweightCharts !== 'undefined') {
                console.log('✅ TradingView lightweight-charts 已加载');
                console.log('LightweightCharts 对象:', LightweightCharts);
                console.log('版本:', LightweightCharts.version || 'unknown');

                // 检查关键方法
                if (typeof LightweightCharts.createChart === 'function') {
                    console.log('✅ createChart 方法可用');
                } else {
                    console.log('❌ createChart 方法不可用');
                }

                // 检查常量
                if (LightweightCharts.CrosshairMode) {
                    console.log('✅ CrosshairMode 常量可用');
                } else {
                    console.log('❌ CrosshairMode 常量不可用');
                }

            } else {
                console.log('❌ TradingView lightweight-charts 未加载');
                console.log('请检查网络连接或CDN是否可访问');
            }
        });

        // 支持回车键触发查询
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                fetchKlineData();
            }
        });
    </script>
</body>
</html>
