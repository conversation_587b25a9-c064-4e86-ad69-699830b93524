<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>K线数据修复管理</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-section {
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 60px;
            resize: vertical;
        }
        .form-row {
            display: flex;
            gap: 15px;
        }
        .form-row .form-group {
            flex: 1;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .btn-danger {
            background-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-success:hover {
            background-color: #218838;
        }
        .table-section {
            margin-top: 30px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .status-active {
            color: #28a745;
            font-weight: bold;
        }
        .status-inactive {
            color: #dc3545;
            font-weight: bold;
        }
        .actions {
            display: flex;
            gap: 5px;
        }
        .actions button {
            padding: 5px 10px;
            font-size: 12px;
        }
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .message.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .message.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .stat-card {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
            flex: 1;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>K线数据修复管理</h1>
        
        <!-- 统计信息 -->
        <div class="stats" id="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalCorrections">0</div>
                <div class="stat-label">总修复数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeCorrections">0</div>
                <div class="stat-label">活跃修复</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="uniqueCodes">0</div>
                <div class="stat-label">涉及品种</div>
            </div>
        </div>

        <!-- 添加修复数据表单 -->
        <div class="form-section">
            <h2>添加修复数据</h2>
            <form id="correctionForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="code">品种代码:</label>
                        <select id="code" name="code" required>
                            <option value="">请选择品种</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="field">修复字段:</label>
                        <select id="field" name="field" required>
                            <option value="">请选择</option>
                            <option value="o">开盘价(o)</option>
                            <option value="cl">收盘价(cl)</option>
                            <option value="h">最高价(h)</option>
                            <option value="l">最低价(l)</option>
                            <option value="v">成交量(v)</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="datetime">修复时间:</label>
                        <input type="datetime-local" id="datetime" name="datetime" step="1" required>
                        <input type="hidden" id="timestamp" name="timestamp">
                        <small style="color: #666; font-size: 12px;">选择需要修复的精确时间点（精确到秒）</small>
                    </div>
                    <div class="form-group">
                        <label for="corrValue">修复值:</label>
                        <input type="text" id="corrValue" name="corr_value" placeholder="修复后的值" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group" style="grid-column: 1 / -1;">
                        <label for="reason">修复原因:</label>
                        <textarea id="reason" name="reason" placeholder="说明修复原因(可选)"></textarea>
                    </div>
                </div>

                <!-- 修复范围说明 -->
                <div class="form-group">
                    <div style="padding: 15px; background-color: #e3f2fd; border-left: 4px solid #2196f3; border-radius: 4px; font-size: 14px;">
                        <strong>📋 修复范围说明：</strong><br>
                        修复数据将自动应用到所有时间范围覆盖此修复时间戳的K线周期。<br>
                        例如：修复时间为 2024-01-15 14:30:25，则该修复将影响：
                        <ul style="margin: 8px 0; padding-left: 20px;">
                            <li>1分钟K线：14:30:00 - 14:30:59</li>
                            <li>1小时K线：14:00:00 - 14:59:59</li>
                            <li>日线K线：00:00:00 - 23:59:59</li>
                            <li>以及其他包含此时间点的K线周期</li>
                        </ul>
                    </div>
                </div>

                <button type="submit">添加修复数据</button>
            </form>
        </div>

        <!-- 消息显示区域 -->
        <div id="message"></div>

        <!-- 修复数据列表 -->
        <div class="table-section">
            <h2>修复数据列表</h2>
            <div style="margin-bottom: 15px;">
                <button onclick="loadCorrections()" class="btn-success">刷新列表</button>
                <button onclick="refreshCache()" style="margin-left: 10px;">刷新缓存</button>
            </div>
            <table id="correctionsTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>品种代码</th>
                        <th>修复时间</th>
                        <th>字段</th>
                        <th>修复值</th>
                        <th>原因</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="correctionsBody">
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // API基础URL
        const API_BASE = '/api/v1/corrections';
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadSupportedSymbols();
            loadCorrections();
            loadStats();

            // 绑定表单提交事件
            document.getElementById('correctionForm').addEventListener('submit', handleSubmit);

            // 绑定日期时间变化事件
            document.getElementById('datetime').addEventListener('change', function() {
                const datetimeValue = this.value;
                if (datetimeValue) {
                    // 将本地时间转换为UTC时间戳（秒）
                    const timestamp = Math.floor(new Date(datetimeValue).getTime() / 1000);
                    document.getElementById('timestamp').value = timestamp;
                }
            });

            // 移除影响范围计算相关的事件绑定
        });

        // 加载支持的品种列表
        async function loadSupportedSymbols() {
            try {
                const response = await fetch('/api/v1/symbols');
                const result = await response.json();

                if (response.ok && result.msg === 'OK') {
                    const codeSelect = document.getElementById('code');
                    codeSelect.innerHTML = '<option value="">请选择品种</option>';

                    result.data.forEach(symbol => {
                        const option = document.createElement('option');
                        option.value = symbol;
                        option.textContent = symbol;
                        codeSelect.appendChild(option);
                    });
                } else {
                    console.error('Failed to load supported symbols:', result);
                }
            } catch (error) {
                console.error('Error loading supported symbols:', error);
            }
        }

        // 处理表单提交
        async function handleSubmit(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const data = {
                code: formData.get('code'),
                timestamp: parseInt(formData.get('timestamp')),
                field: formData.get('field'),
                corr_value: formData.get('corr_value'),
                reason: formData.get('reason') || ''
            };

            try {
                const response = await fetch(API_BASE, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                
                if (response.ok) {
                    showMessage('修复数据添加成功！', 'success');
                    e.target.reset();
                    loadCorrections();
                    loadStats();
                } else {
                    showMessage('添加失败: ' + (result.error || '未知错误'), 'error');
                }
            } catch (error) {
                showMessage('网络错误: ' + error.message, 'error');
            }
        }

        // 加载修复数据列表
        async function loadCorrections() {
            try {
                const response = await fetch(API_BASE + '?limit=100');
                const result = await response.json();
                
                if (response.ok) {
                    displayCorrections(result.data || []);
                } else {
                    showMessage('加载数据失败: ' + (result.error || '未知错误'), 'error');
                }
            } catch (error) {
                showMessage('网络错误: ' + error.message, 'error');
            }
        }

        // 显示修复数据
        function displayCorrections(corrections) {
            const tbody = document.getElementById('correctionsBody');
            tbody.innerHTML = '';

            corrections.forEach(correction => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${correction.id}</td>
                    <td>${correction.code}</td>
                    <td>${formatTimestamp(correction.timestamp)}</td>
                    <td>${getFieldText(correction.field)}</td>
                    <td>${correction.corr_value}</td>
                    <td>${correction.reason || '-'}</td>
                    <td class="${correction.is_active ? 'status-active' : 'status-inactive'}">
                        ${correction.is_active ? '启用' : '禁用'}
                    </td>
                    <td>${formatTimestamp(correction.created_at)}</td>
                    <td class="actions">
                        <button onclick="toggleCorrection(${correction.id}, ${!correction.is_active})"
                                class="${correction.is_active ? 'btn-danger' : 'btn-success'}">
                            ${correction.is_active ? '禁用' : '启用'}
                        </button>
                        <button onclick="deleteCorrection(${correction.id})" class="btn-danger">删除</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 切换修复数据状态
        async function toggleCorrection(id, isActive) {
            try {
                const response = await fetch(`${API_BASE}/${id}/toggle`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ is_active: isActive })
                });

                const result = await response.json();
                
                if (response.ok) {
                    showMessage('状态更新成功！', 'success');
                    loadCorrections();
                    loadStats();
                } else {
                    showMessage('状态更新失败: ' + (result.error || '未知错误'), 'error');
                }
            } catch (error) {
                showMessage('网络错误: ' + error.message, 'error');
            }
        }

        // 删除修复数据
        async function deleteCorrection(id) {
            if (!confirm('确定要删除这条修复数据吗？')) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/${id}`, {
                    method: 'DELETE'
                });

                const result = await response.json();
                
                if (response.ok) {
                    showMessage('删除成功！', 'success');
                    loadCorrections();
                    loadStats();
                } else {
                    showMessage('删除失败: ' + (result.error || '未知错误'), 'error');
                }
            } catch (error) {
                showMessage('网络错误: ' + error.message, 'error');
            }
        }

        // 刷新缓存
        async function refreshCache() {
            try {
                const response = await fetch('/api/v1/corrections/stats');
                if (response.ok) {
                    showMessage('缓存刷新成功！', 'success');
                    loadStats();
                } else {
                    showMessage('缓存刷新失败', 'error');
                }
            } catch (error) {
                showMessage('网络错误: ' + error.message, 'error');
            }
        }

        // 加载统计信息
        async function loadStats() {
            try {
                const response = await fetch('/api/v1/corrections/stats');
                const result = await response.json();
                
                if (response.ok && result.data) {
                    const stats = result.data;
                    document.getElementById('totalCorrections').textContent = stats.total_corrections || 0;
                    
                    // 计算活跃修复数
                    let activeCount = 0;
                    if (stats.by_code) {
                        activeCount = stats.total_corrections;
                    }
                    document.getElementById('activeCorrections').textContent = activeCount;
                    
                    // 计算涉及品种数
                    const uniqueCodes = stats.by_code ? Object.keys(stats.by_code).length : 0;
                    document.getElementById('uniqueCodes').textContent = uniqueCodes;
                }
            } catch (error) {
                console.error('Failed to load stats:', error);
            }
        }

        // 显示消息
        function showMessage(text, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = `<div class="message ${type}">${text}</div>`;
            setTimeout(() => {
                messageDiv.innerHTML = '';
            }, 5000);
        }



        // 获取字段文本
        function getFieldText(field) {
            const fields = {
                'o': '开盘价', 'cl': '收盘价', 'h': '最高价', 'l': '最低价', 'v': '成交量'
            };
            return fields[field] || field;
        }

        // 格式化时间戳
        function formatTimestamp(timestamp) {
            return new Date(timestamp * 1000).toLocaleString('zh-CN');
        }




    </script>
</body>
</html>
