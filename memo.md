使用Go语言写一个可以稳定运行的行情接口程序，主要功能包括：
1.从https://qos.hk/的Websocket接口和HTTP接口获取K线数据
2.提供HTTP和Websocket接口供其他服务访问调用
3.具有缓存机制，如果用户访问的数据在缓存中则直接提供，否则调用QOS.HK接口获取后提供
4.支持美股、港股和数字货币
5.支持对接入的交易品种、数据类型进行配置
6.具有日志、异常处理、自动重启功能


Key: 7874b6353908ede80711c99155b7a0bb
Expires At: 2025-07-17T23:32:24.974048909+08:00

项目完成总结
🎉 QOS Market API 项目已成功完成！

📋 已完成的功能
✅ 核心功能

从 QOS.HK 获取实时行情数据（HTTP + WebSocket）
提供 HTTP REST API 接口
提供 WebSocket 实时数据推送
智能缓存机制，优先使用缓存数据
支持美股、港股、A股、数字货币、外汇、商品等多市场
✅ 系统特性

完整的配置管理系统
结构化日志记录（支持文件轮转）
异常处理和自动重连机制
健康检查和性能监控
自动重启和故障恢复
缓存过期和清理机制
✅ 开发和部署

完整的项目结构和代码组织
Makefile 构建系统
Docker 容器化支持
单元测试和集成测试
详细的 API 文档
部署指南和运维文档
🏗️ 项目架构
qos-market-api/
├── cmd/server/          # 主程序入口
├── internal/            # 内部包
│   ├── cache/          # 缓存系统
│   ├── client/         # QOS.HK 客户端
│   ├── config/         # 配置管理
│   ├── handler/        # HTTP 处理器
│   ├── logger/         # 日志系统
│   ├── monitoring/     # 监控系统
│   ├── recovery/       # 恢复管理

🚀 快速开始
设置 API 密钥：
cp configs/config.example.yaml configs/config.yaml
# 编辑 configs/config.yaml，设置您的 QOS.HK API 密钥
启动服务：
测试 API：
📡 API 接口
HTTP API: http://localhost:8080
/health - 健康检查
/api/v1/snapshot - 实时行情快照
/api/v1/kline - K线数据
/api/v1/depth - 盘口深度
/api/v1/instrument - 交易品种信息
WebSocket: ws://localhost:8080/ws
支持实时数据订阅和推送
监控接口: http://localhost:9090
/metrics - 应用指标
/health - 监控健康检查
🔧 主要特性
高可用性
自动重连机制
故障恢复和重启
健康检查监控
高性能
内存缓存系统
并发处理支持
连接池管理
易于运维
详细的日志记录
指标监控
Docker 容器化
系统服务支持
开发友好
完整的文档
测试覆盖
代码规范
易于扩展
📚 文档
API 文档 - 详细的接口说明
部署指南 - 生产环境部署
README.md - 项目概述和快速开始
这个项目提供了一个完整、稳定、可扩展的行情数据服务解决方案，满足了您提出的所有需求！🎯


对行情接口程序进行优化：
1.支持K线周期包括1分钟、5分钟、15分钟、30分钟、60分钟、日K线、周K线、月K线、年K线
2.对照https://qos.hk/api.html接口文档，检查qos_client.go，主要问题是snapshot、instrument、depth不支持逗号分隔，需要转换为接口文档中array格式
3.基于配置中的品种对请求进行过滤，并在开始运行打印支持的品种，在健康状态中返回支持的品种。


新增功能：对调用情况进行统计，并在/metrics中显示
1.缓存命中次数，包括总次数、最近24小时、1小时、1分钟命中次数
2.调用QOS HTTP接口次数，包括总次数、最近24小时、1小时、1分钟命中次数

为行情接口程序新增功能：
1.支持对指定错误K线数据进行修改，用户可以指定品种、时间和要修改的属性
2.提供一个简单的Web界面来录入需要修改的错误数据
3.使用sqlite或其他轻量级数据库进行修复数据的持久化
4.为了降低延迟，程序应该定期从数据库中读取修复数据并加载到内存中
5.程序在数据返回给用户前，应该检查是否有修复数据，如果有，则应用修复数据，这个检查过程应该是低延时、高效的

编写一个简单的网页来对行情接口进行测试，可以指定品种、周期查看K线。
使用chartjs-chart-financial来绘制蜡烛图

修复以下问题：1.成功获取K线的提示消息5秒后自动消失；2.蜡烛图中不同时间的蜡烛堆叠覆盖了

使用https://github.com/tradingview/lightweight-charts来实现测试页面中的蜡烛图


对行情接口程序进行优化：
1.优化K线数据修复管理页面：交易品种代码根据配置文件支持的品种提供一个下拉选项
2.优化K线数据修复管理页面：时间戳提供一个日期选择器
3.优化K线测试页面：交易品种代码根据配置文件支持的品种提供一个下拉选项
4.优化K线数据修复逻辑，不需要指定K线类型，而是根据修复的时间戳自动对所有类型的K线进行修复


K线修复功能优化：录入的K线修复值可能会影响不同周期的K线，例如录入数据是当天最高价，则会影响日线。


对K线修复逻辑进行简化和优化。修复逻辑如下：
1.录入的K线修复值精确到秒，但不需要区分周期
2.所有周期的K线，如果覆盖到修复时间戳，就应用修复值

新增延迟追踪功能，分阶段记录延迟并统计：
1.追踪获取QOS行情接口的延迟
2.追踪K线修复的延迟
3.向用户返回最终结果的延迟
4.在metrics中输出最近1分钟、1小时、24小时的延迟平均值和最大值
5.在日志中输出用户请求K线的数据，并输出总延迟


提高K线修复的效率，降低修复延迟，同时应该应用起止时间内的所有修复数据而不是仅第一个。
提高K线修复的效率，降低修复延迟。不要按照时间戳逐个搜索，而是应该通过起止时间从修复缓存中搜索所有覆盖的数据，然后再按照从老到新的数据应用修复。

区分最近K线和历史K线进行功能优化，最近K线仍然使用目前的GetKline，历史K线使用新的方法和处理。
* 最近K线（使用QOS的/kline接口）
1. 后台定期向QOS批量查询
2. 在缓存中仅保留最新数据
3. 用户请求时从缓存中获取（若有）
4. 最近K线的更新频率可配置

* 历史K线（使用QOS的/history接口）
1. 用户请求时再向QOS查询
2. 如果行情数据不涉及最新K线则缓存
3. 用户请求时从缓存中获取（若有）
4. 缓存有效期可配置，默认设置为24小时

注意，不论历史K线还是最近K线均需要在返回给用户前进行K线修复。QOS接口可以从https://qos.hk/api.html查询。

1. 将用户的请求在日志中输出，包括请求的品种、K线类型、数量、结束时间、延迟等信息，方便后续分析和监控。
2. 根据最新的代码，重新整理系统的API文档、CORRECTION文档等。


最近K线数据延迟优化：
1. 初始化：启动时读取最近1000条K线数据到内存中，这样用户请求时可以快速返回，初始化读取的条数可配置
2. 实时刷新：之后每隔一段时间更新最近的K线数据到内存中，此时仅需请求最新几条K线数据进行替换或新增，间隔时间可配置，内存中保留的每个品种的最大K线条数和刷新的K线数量可配置
3. 定期检查：定时检查内存中最近K线的完整性和新鲜度，如果不完整或更新时间超过一定时间，则对有问题的K线重新读取最近1000条数据

修复在K线数据初始化时的问题：failed to unmarshal response: unexpected end of JSON input
可能的问题是同时请求的品种和K线条数太多，请按品种并行初始化。

在初始化时输出日志，包括品种、K线类型、K线数量和起止时间


添加功能：提供一个并行初始化K线数据的方法，并在配置中添加相应选项可以指定按品种批次并行初始化或顺序初始化，注意：1.并行初始化只需要将品种批次并行，不需要对K线类型并行。2.将K线类型的初始化顺序调整为从大周期到小周期，例如年、月、周、日、小时、分钟。