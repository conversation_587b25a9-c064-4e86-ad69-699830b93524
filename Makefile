# QOS Market API Makefile

# 变量定义
APP_NAME = qos-market-api
VERSION = 1.0.0
BUILD_TIME = $(shell date +%Y-%m-%d_%H:%M:%S)
GIT_COMMIT = $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
LDFLAGS = -X main.version=$(VERSION) -X main.buildTime=$(BUILD_TIME) -X main.gitCommit=$(GIT_COMMIT)

# Go相关变量
GOCMD = go
GOBUILD = $(GOCMD) build
GOCLEAN = $(GOCMD) clean
GOTEST = $(GOCMD) test
GOGET = $(GOCMD) get
GOMOD = $(GOCMD) mod

# 目录
BUILD_DIR = bin
MAIN_PATH = cmd/server/main.go

# 默认目标
.PHONY: all
all: clean deps build

# 安装依赖
.PHONY: deps
deps:
	@echo "Installing dependencies..."
	$(GOMOD) download
	$(GOMOD) tidy

# 构建
.PHONY: build
build:
	@echo "Building $(APP_NAME)..."
	@mkdir -p $(BUILD_DIR)
	$(GOBUILD) -trimpath -ldflags "$(LDFLAGS) -w -s" -o $(BUILD_DIR)/$(APP_NAME) $(MAIN_PATH)

# 构建Linux版本
.PHONY: build-linux
build-linux:
	@echo "Building $(APP_NAME) for Linux..."
	@mkdir -p $(BUILD_DIR)
	GOOS=linux GOARCH=amd64 $(GOBUILD) -trimpath -ldflags "$(LDFLAGS) -w -s" -o $(BUILD_DIR)/$(APP_NAME)-linux $(MAIN_PATH)

# 构建Windows版本
.PHONY: build-windows
build-windows:
	@echo "Building $(APP_NAME) for Windows..."
	@mkdir -p $(BUILD_DIR)
	GOOS=windows GOARCH=amd64 $(GOBUILD) -ldflags "$(LDFLAGS)" -o $(BUILD_DIR)/$(APP_NAME)-windows.exe $(MAIN_PATH)

# 构建所有平台
.PHONY: build-all
build-all: build build-linux build-windows

# 运行
.PHONY: run
run: build
	@echo "Running $(APP_NAME)..."
	./$(BUILD_DIR)/$(APP_NAME) -config=configs/config.yaml

# 运行开发模式
.PHONY: dev
dev:
	@echo "Running $(APP_NAME) in development mode..."
	$(GOCMD) run $(MAIN_PATH) -config=configs/config.yaml

# 测试
.PHONY: test
test:
	@echo "Running tests..."
	$(GOTEST) -v ./...

# 基准测试
.PHONY: bench
bench:
	@echo "Running benchmarks..."
	$(GOTEST) -bench=. -benchmem ./...

# 测试覆盖率
.PHONY: test-coverage
test-coverage:
	@echo "Running tests with coverage..."
	$(GOTEST) -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# 代码检查
.PHONY: lint
lint:
	@echo "Running linter..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "golangci-lint not installed. Install it with: go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest"; \
	fi

# 格式化代码
.PHONY: fmt
fmt:
	@echo "Formatting code..."
	$(GOCMD) fmt ./...

# 清理
.PHONY: clean
clean:
	@echo "Cleaning..."
	$(GOCLEAN)
	rm -rf $(BUILD_DIR)
	rm -f coverage.out coverage.html

# 创建配置文件
.PHONY: config
config:
	@if [ ! -f configs/config.yaml ]; then \
		echo "Creating config file from example..."; \
		cp configs/config.example.yaml configs/config.yaml; \
		echo "Please edit configs/config.yaml and set your API key"; \
	else \
		echo "Config file already exists"; \
	fi

# 创建日志目录
.PHONY: setup-dirs
setup-dirs:
	@echo "Creating directories..."
	@mkdir -p logs
	@mkdir -p $(BUILD_DIR)

# 安装工具
.PHONY: install-tools
install-tools:
	@echo "Installing development tools..."
	$(GOGET) github.com/golangci/golangci-lint/cmd/golangci-lint@latest

# Docker构建
.PHONY: docker-build
docker-build:
	@echo "Building Docker image..."
	docker build -t $(APP_NAME):$(VERSION) .

# Docker运行
.PHONY: docker-run
docker-run:
	@echo "Running Docker container..."
	docker run -p 8080:8080 -p 9090:9090 $(APP_NAME):$(VERSION)

# 显示帮助
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  all          - Clean, install deps and build"
	@echo "  deps         - Install dependencies"
	@echo "  build        - Build the application"
	@echo "  build-linux  - Build for Linux"
	@echo "  build-windows- Build for Windows"
	@echo "  build-all    - Build for all platforms"
	@echo "  run          - Build and run the application"
	@echo "  dev          - Run in development mode"
	@echo "  test         - Run tests"
	@echo "  bench        - Run benchmarks"
	@echo "  test-coverage- Run tests with coverage"
	@echo "  lint         - Run linter"
	@echo "  fmt          - Format code"
	@echo "  clean        - Clean build artifacts"
	@echo "  config       - Create config file from example"
	@echo "  setup-dirs   - Create necessary directories"
	@echo "  install-tools- Install development tools"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run   - Run Docker container"
	@echo "  help         - Show this help"

# 初始化项目
.PHONY: init
init: setup-dirs config deps
	@echo "Project initialized successfully!"
	@echo "Next steps:"
	@echo "1. Edit configs/config.yaml and set your QOS.HK API key"
	@echo "2. Run 'make build' to build the application"
	@echo "3. Run 'make run' to start the server"
