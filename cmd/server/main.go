package main

import (
	"context"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"qos-market-api/internal/cache"
	"qos-market-api/internal/client"
	"qos-market-api/internal/config"
	"qos-market-api/internal/correction"
	"qos-market-api/internal/handler"
	"qos-market-api/internal/logger"
	"qos-market-api/internal/monitoring"
	"qos-market-api/internal/recovery"
	"qos-market-api/internal/service"
	"qos-market-api/internal/websocket"
)

var (
	configPath = flag.String("config", "configs/config.yaml", "配置文件路径")
	version    = "1.0.0"
	buildTime  = "unknown"
)

func main() {
	flag.Parse()

	// 打印版本信息
	fmt.Printf("QOS Market API Server\n")
	fmt.Printf("Version: %s\n", version)
	fmt.Printf("Build Time: %s\n", buildTime)
	fmt.Printf("Config: %s\n", *configPath)
	fmt.Println()

	// 加载配置
	cfg, err := config.Load(*configPath)
	if err != nil {
		fmt.Printf("Failed to load config: %v\n", err)
		os.Exit(1)
	}

	// 初始化日志
	if err := logger.Init(&cfg.Logging); err != nil {
		fmt.Printf("Failed to initialize logger: %v\n", err)
		os.Exit(1)
	}

	log := logger.GetLogger()
	log.Info("Starting QOS Market API Server...")

	// 打印支持的品种
	supportedSymbols := cfg.GetSupportedSymbols()
	log.Infof("Supported symbols (%d): %v", len(supportedSymbols), supportedSymbols)

	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 设置panic恢复
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("Main panic recovered: %v", r)
			os.Exit(1)
		}
	}()

	// 初始化组件
	if err := initializeAndRun(ctx, cfg, log); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}

func initializeAndRun(ctx context.Context, cfg *config.Config, log logger.Logger) error {
	// 创建缓存管理器
	cacheManager := cache.NewCacheManager(&cfg.Cache, &cfg.Kline, log)
	log.Info("Cache manager initialized")

	// 创建QOS客户端
	qosClient := client.NewQOSClient(&cfg.QOS, log)
	log.Info("QOS client created")

	// 创建恢复管理器
	recoveryManager := recovery.NewRecoveryManager(&cfg.Recovery, log)
	log.Info("Recovery manager created")

	// 创建监控器
	monitor := monitoring.NewMonitor(&cfg.Monitoring, qosClient, cacheManager, log)
	log.Info("Monitor created")

	// 设置缓存统计记录器
	cacheManager.SetStatsRecorder(monitor)
	log.Info("Cache stats recorder set")

	// 设置QOS客户端HTTP统计记录器
	qosClient.SetHTTPStatsRecorder(monitor)
	log.Info("QOS HTTP stats recorder set")

	// 注册健康检查器
	recoveryManager.RegisterHealthChecker(monitor)

	// 创建HTTP处理器
	httpHandler := handler.NewHTTPHandler(qosClient, cacheManager, cfg, log)
	httpHandler.SetLatencyRecorder(monitor)
	log.Info("HTTP handler created")

	// 初始化修复功能（如果启用）
	var correctionSystem *CorrectionSystem
	if cfg.Correction.Enabled {
		correctionSystem = initCorrectionSystem(cfg, httpHandler, monitor, log)
	}

	// 初始化最近K线服务（如果启用）
	var recentKlineService interface{}
	if cfg.Kline.Recent.Enabled {
		var corrector *correction.Corrector
		if correctionSystem != nil {
			corrector = correctionSystem.Corrector
		}
		recentKlineService = initRecentKlineService(cfg, qosClient, cacheManager, corrector, log)
		// 设置最近K线服务到HTTP处理器
		httpHandler.SetRecentKlineService(recentKlineService)
	}

	// 创建WebSocket服务器
	wsServer := websocket.NewServer(qosClient, cacheManager, &cfg.WebSocket, log)
	log.Info("WebSocket server created")

	// 启动组件
	if err := startComponents(ctx, qosClient, recoveryManager, monitor, wsServer, recentKlineService); err != nil {
		return fmt.Errorf("failed to start components: %w", err)
	}

	// 设置HTTP路由
	var mux *http.ServeMux
	if correctionSystem != nil && correctionSystem.Handler != nil {
		mux = httpHandler.SetupRoutesWithCorrection(correctionSystem.Handler)
	} else {
		mux = httpHandler.SetupRoutes()
	}
	mux.HandleFunc("/ws", wsServer.HandleWebSocket)

	// 创建HTTP服务器
	httpServer := &http.Server{
		Addr:         cfg.GetAddress(),
		Handler:      mux,
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
		IdleTimeout:  cfg.Server.IdleTimeout,
	}

	// 启动HTTP服务器
	go func() {
		log.Infof("HTTP server starting on %s", cfg.GetAddress())
		if err := httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Errorf("HTTP server error: %v", err)
		}
	}()

	log.Info("QOS Market API Server started successfully")

	// 等待信号
	return waitForShutdown(ctx, httpServer, cacheManager, qosClient, recoveryManager, monitor, recentKlineService, wsServer, correctionSystem, log)
}

// CorrectionSystem 修复系统结构
type CorrectionSystem struct {
	Handler   *correction.Handler
	Corrector *correction.Corrector
}

// initCorrectionSystem 初始化修复系统
func initCorrectionSystem(cfg *config.Config, httpHandler *handler.HTTPHandler, monitor *monitoring.Monitor, log logger.Logger) *CorrectionSystem {
	// 初始化修复系统
	correctionHandler, corrector, err := correction.InitCorrectionSystem(cfg, log)
	if err != nil {
		log.Errorf("Failed to initialize correction system: %v", err)
		return nil
	}

	if corrector != nil {
		// 设置修复器到HTTP处理器
		httpHandler.SetCorrector(corrector)
		// 设置修复器到监控器
		monitor.SetCorrector(corrector)
		log.Info("Correction system initialized and attached to HTTP handler and monitor")
	}

	return &CorrectionSystem{
		Handler:   correctionHandler,
		Corrector: corrector,
	}
}

func startComponents(ctx context.Context, qosClient *client.QOSClient, recoveryManager *recovery.RecoveryManager, monitor *monitoring.Monitor, wsServer *websocket.Server, recentKlineService interface{}) error {
	// 启动QOS客户端
	if err := qosClient.Start(ctx); err != nil {
		return fmt.Errorf("failed to start QOS client: %w", err)
	}

	// 启动恢复管理器
	recoveryManager.Start(ctx)

	// 启动监控器
	if err := monitor.Start(ctx); err != nil {
		return fmt.Errorf("failed to start monitor: %w", err)
	}

	// 启动最近K线服务（如果启用）
	if recentKlineService != nil {
		if service, ok := recentKlineService.(*service.RecentKlineService); ok {
			if err := service.Start(); err != nil {
				return fmt.Errorf("failed to start recent kline service: %w", err)
			}
		}
	}

	// 启动WebSocket服务器
	wsServer.Start(ctx)

	return nil
}

func waitForShutdown(ctx context.Context, httpServer *http.Server, cacheManager *cache.CacheManager, qosClient *client.QOSClient, recoveryManager *recovery.RecoveryManager, monitor *monitoring.Monitor, recentKlineService interface{}, wsServer *websocket.Server, correctionSystem *CorrectionSystem, log logger.Logger) error {
	// 创建信号通道
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 等待信号
	select {
	case sig := <-sigChan:
		log.Infof("Received signal: %v", sig)
	case <-ctx.Done():
		log.Info("Context cancelled")
	}

	log.Info("Shutting down server...")

	// 创建关闭上下文
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	// 优雅关闭HTTP服务器
	if err := httpServer.Shutdown(shutdownCtx); err != nil {
		log.Errorf("HTTP server shutdown error: %v", err)
	} else {
		log.Info("HTTP server stopped")
	}

	// 停止组件
	stopComponents(qosClient, recoveryManager, monitor, cacheManager, recentKlineService, wsServer, correctionSystem, log)

	log.Info("Server shutdown completed")
	return nil
}

func stopComponents(qosClient *client.QOSClient, recoveryManager *recovery.RecoveryManager, monitor *monitoring.Monitor, cacheManager *cache.CacheManager, recentKlineService interface{}, wsServer *websocket.Server, correctionSystem *CorrectionSystem, log logger.Logger) {
	// 停止WebSocket服务器
	wsServer.Stop()

	// 停止修复系统
	if correctionSystem != nil {
		correction.CloseCorrectionSystem(correctionSystem.Handler, correctionSystem.Corrector)
		log.Info("Correction system stopped")
	}

	// 停止最近K线服务
	if recentKlineService != nil {
		if service, ok := recentKlineService.(*service.RecentKlineService); ok {
			if err := service.Stop(); err != nil {
				log.Errorf("Failed to stop recent kline service: %v", err)
			} else {
				log.Info("Recent kline service stopped")
			}
		}
	}

	// 停止QOS客户端
	if err := qosClient.Stop(); err != nil {
		log.Errorf("Failed to stop QOS client: %v", err)
	} else {
		log.Info("QOS client stopped")
	}

	// 停止恢复管理器
	recoveryManager.Stop()
	log.Info("Recovery manager stopped")

	// 停止监控器
	if err := monitor.Stop(); err != nil {
		log.Errorf("Failed to stop monitor: %v", err)
	} else {
		log.Info("Monitor stopped")
	}

	// 停止缓存管理器
	cacheManager.Stop()
	log.Info("Cache manager stopped")
}

// initRecentKlineService 初始化最近K线服务
func initRecentKlineService(cfg *config.Config, qosClient *client.QOSClient, cacheManager *cache.CacheManager, corrector *correction.Corrector, log logger.Logger) *service.RecentKlineService {
	// 创建最近K线服务
	recentKlineService := service.NewRecentKlineService(
		qosClient,
		cacheManager,
		corrector,
		&cfg.Kline,
		&cfg.Symbols,
		log,
	)

	log.Info("Recent kline service initialized")
	return recentKlineService
}

// 健康检查实现
type QOSHealthChecker struct {
	client *client.QOSClient
}

func (h *QOSHealthChecker) IsHealthy() bool {
	return h.client.IsConnected()
}

func (h *QOSHealthChecker) GetName() string {
	return "qos_client"
}

// 缓存健康检查实现
type CacheHealthChecker struct {
	cache *cache.CacheManager
}

func (h *CacheHealthChecker) IsHealthy() bool {
	// 简单检查缓存是否可用
	return h.cache.Size() >= 0
}

func (h *CacheHealthChecker) GetName() string {
	return "cache_manager"
}
