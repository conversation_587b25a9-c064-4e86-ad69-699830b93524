package main

import (
	"fmt"
	"log"
	"sync"
	"time"
)

// 模拟K线类型
type KlineType string

const (
	Kline1Min  KlineType = "1m"
	Kline5Min  KlineType = "5m"
	Kline15Min KlineType = "15m"
	Kline30Min KlineType = "30m"
	Kline1Hour KlineType = "1h"
	KlineDay   KlineType = "1d"
)

// 模拟QOS API调用
func fetchKlineData(klineType KlineType, symbols []string) error {
	// 模拟网络延迟
	time.Sleep(200 * time.Millisecond)

	fmt.Printf("✓ Fetched %s kline data for %d symbols\n", klineType, len(symbols))
	return nil
}

// 顺序更新实现
func sequentialUpdate(klineTypes []KlineType, symbols []string) time.Duration {
	fmt.Println("\n=== 顺序更新 ===")
	start := time.Now()

	successCount := 0
	for _, klineType := range klineTypes {
		if err := fetchKlineData(klineType, symbols); err != nil {
			log.Printf("Failed to update %s: %v", klineType, err)
		} else {
			successCount++
		}
	}

	duration := time.Since(start)
	fmt.Printf("顺序更新完成: %d 成功, 耗时: %v\n", successCount, duration)
	return duration
}

// 并行更新实现
func parallelUpdate(klineTypes []KlineType, symbols []string) time.Duration {
	fmt.Println("\n=== 并行更新 ===")
	start := time.Now()

	// 结果结构
	type updateResult struct {
		klineType KlineType
		err       error
	}

	// 创建结果通道
	resultChan := make(chan updateResult, len(klineTypes))

	// 启动并行更新goroutines
	for _, klineType := range klineTypes {
		go func(kt KlineType) {
			err := fetchKlineData(kt, symbols)
			resultChan <- updateResult{
				klineType: kt,
				err:       err,
			}
		}(klineType)
	}

	// 收集结果
	successCount := 0
	errorCount := 0
	for i := 0; i < len(klineTypes); i++ {
		result := <-resultChan
		if result.err != nil {
			log.Printf("Failed to update %s: %v", result.klineType, result.err)
			errorCount++
		} else {
			successCount++
		}
	}

	duration := time.Since(start)
	fmt.Printf("并行更新完成: %d 成功, %d 失败, 耗时: %v\n", successCount, errorCount, duration)
	return duration
}

// 高级并行更新（带工作池）
func parallelUpdateWithWorkerPool(klineTypes []KlineType, symbols []string, maxWorkers int) time.Duration {
	fmt.Printf("\n=== 并行更新（工作池：%d workers）===\n", maxWorkers)
	start := time.Now()

	// 工作任务
	type task struct {
		klineType KlineType
		symbols   []string
	}

	// 结果
	type result struct {
		klineType KlineType
		err       error
	}

	// 创建通道
	taskChan := make(chan task, len(klineTypes))
	resultChan := make(chan result, len(klineTypes))

	// 启动工作池
	var wg sync.WaitGroup
	for i := 0; i < maxWorkers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			for t := range taskChan {
				fmt.Printf("Worker %d processing %s\n", workerID, t.klineType)
				err := fetchKlineData(t.klineType, t.symbols)
				resultChan <- result{
					klineType: t.klineType,
					err:       err,
				}
			}
		}(i)
	}

	// 发送任务
	go func() {
		for _, klineType := range klineTypes {
			taskChan <- task{
				klineType: klineType,
				symbols:   symbols,
			}
		}
		close(taskChan)
	}()

	// 等待所有worker完成
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// 收集结果
	successCount := 0
	errorCount := 0
	for result := range resultChan {
		if result.err != nil {
			log.Printf("Failed to update %s: %v", result.klineType, result.err)
			errorCount++
		} else {
			successCount++
		}
	}

	duration := time.Since(start)
	fmt.Printf("工作池更新完成: %d 成功, %d 失败, 耗时: %v\n", successCount, errorCount, duration)
	return duration
}

func parallelUpdateDemo() {
	fmt.Println("QOS Market API - K线并行更新性能演示")
	fmt.Println("=====================================")

	// 测试数据
	klineTypes := []KlineType{
		Kline1Min,
		Kline5Min,
		Kline15Min,
		Kline30Min,
		Kline1Hour,
		KlineDay,
	}

	symbols := []string{"AAPL", "GOOGL", "MSFT", "TSLA", "AMZN"}

	fmt.Printf("测试配置:\n")
	fmt.Printf("- K线类型数量: %d\n", len(klineTypes))
	fmt.Printf("- 交易品种数量: %d\n", len(symbols))
	fmt.Printf("- 模拟网络延迟: 200ms\n")
	fmt.Printf("- 预期顺序时间: %v\n", time.Duration(len(klineTypes))*200*time.Millisecond)

	// 1. 顺序更新
	sequentialDuration := sequentialUpdate(klineTypes, symbols)

	// 2. 并行更新
	parallelDuration := parallelUpdate(klineTypes, symbols)

	// 3. 工作池并行更新
	workerPoolDuration := parallelUpdateWithWorkerPool(klineTypes, symbols, 3)

	// 性能对比
	fmt.Println("\n=== 性能对比 ===")
	fmt.Printf("顺序更新耗时: %v\n", sequentialDuration)
	fmt.Printf("并行更新耗时: %v\n", parallelDuration)
	fmt.Printf("工作池更新耗时: %v\n", workerPoolDuration)

	if parallelDuration > 0 {
		speedup := float64(sequentialDuration) / float64(parallelDuration)
		fmt.Printf("并行加速比: %.2fx\n", speedup)
	}

	if workerPoolDuration > 0 {
		speedup := float64(sequentialDuration) / float64(workerPoolDuration)
		fmt.Printf("工作池加速比: %.2fx\n", speedup)
	}

	// 资源使用分析
	fmt.Println("\n=== 资源使用分析 ===")
	fmt.Printf("并行更新:\n")
	fmt.Printf("- Goroutine数量: %d (每种K线类型一个)\n", len(klineTypes))
	fmt.Printf("- 内存开销: ~%dKB (每个goroutine约2KB)\n", len(klineTypes)*2)
	fmt.Printf("- 并发API调用: %d\n", len(klineTypes))

	fmt.Printf("\n工作池更新:\n")
	fmt.Printf("- Worker数量: 3 (固定)\n")
	fmt.Printf("- 内存开销: ~6KB (3个worker)\n")
	fmt.Printf("- 并发API调用: 最多3个\n")

	fmt.Println("\n=== 建议 ===")
	fmt.Println("1. 对于K线类型较少(≤10)的场景，推荐使用简单并行更新")
	fmt.Println("2. 对于K线类型较多(>10)的场景，推荐使用工作池限制并发数")
	fmt.Println("3. 根据QOS API的并发限制调整worker数量")
	fmt.Println("4. 监控API响应时间，避免过度并发导致的性能下降")
}
