package main

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

// KlineOptimizationDemo 演示K线数据获取优化功能
func main() {
	klineOptimizationDemo()
	fmt.Println()
	parallelUpdateDemo()
	fmt.Println()
	statsDemo()
}

func klineOptimizationDemo() {
	fmt.Println("QOS Market API - K线数据获取优化功能演示")
	fmt.Println(strings.Repeat("=", 50))

	// 等待服务器启动
	fmt.Println("请先启动QOS Market API服务器...")
	fmt.Println("运行命令: ./qos-server")
	fmt.Println()

	baseURL := "http://localhost:8080"

	// 演示1: 最近K线数据获取（智能路由到最近K线缓存）
	fmt.Println("1. 演示最近K线数据获取（小数量请求，使用最近K线缓存）")
	testRecentKline(baseURL)
	fmt.Println()

	// 演示2: 历史K线数据获取（智能路由到历史K线接口）
	fmt.Println("2. 演示历史K线数据获取（大数量请求，使用历史K线接口）")
	testHistoryKlineByCount(baseURL)
	fmt.Println()

	// 演示3: 指定时间的历史K线数据获取
	fmt.Println("3. 演示指定时间的历史K线数据获取")
	testHistoryKlineByTime(baseURL)
	fmt.Println()

	// 演示4: 专门的历史K线接口
	fmt.Println("4. 演示专门的历史K线接口")
	testHistoryAPI(baseURL)
	fmt.Println()

	// 演示5: 监控指标查看
	fmt.Println("5. 查看延迟监控指标")
	testMetrics(baseURL)
	fmt.Println()

	fmt.Println("演示完成！")
}

// testRecentKline 测试最近K线数据获取
func testRecentKline(baseURL string) {
	url := fmt.Sprintf("%s/api/v1/kline?codes=US:AAPL&kline_type=1001&count=10", baseURL)

	start := time.Now()
	resp, err := http.Get(url)
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return
	}
	defer resp.Body.Close()

	duration := time.Since(start)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("读取响应失败: %v\n", err)
		return
	}

	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		fmt.Printf("解析JSON失败: %v\n", err)
		return
	}

	fmt.Printf("请求URL: %s\n", url)
	fmt.Printf("响应时间: %v\n", duration)
	fmt.Printf("状态码: %d\n", resp.StatusCode)
	fmt.Printf("响应消息: %v\n", result["msg"])

	if data, ok := result["data"].([]interface{}); ok {
		fmt.Printf("返回数据条数: %d\n", len(data))
	}

	fmt.Println("✓ 小数量请求应该使用最近K线缓存，响应速度较快")
}

// testHistoryKlineByCount 测试大数量历史K线数据获取
func testHistoryKlineByCount(baseURL string) {
	url := fmt.Sprintf("%s/api/v1/kline?codes=US:AAPL&kline_type=1001&count=200", baseURL)

	start := time.Now()
	resp, err := http.Get(url)
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return
	}
	defer resp.Body.Close()

	duration := time.Since(start)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("读取响应失败: %v\n", err)
		return
	}

	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		fmt.Printf("解析JSON失败: %v\n", err)
		return
	}

	fmt.Printf("请求URL: %s\n", url)
	fmt.Printf("响应时间: %v\n", duration)
	fmt.Printf("状态码: %d\n", resp.StatusCode)
	fmt.Printf("响应消息: %v\n", result["msg"])

	if data, ok := result["data"].([]interface{}); ok {
		fmt.Printf("返回数据条数: %d\n", len(data))
	}

	fmt.Println("✓ 大数量请求应该使用历史K线接口")
}

// testHistoryKlineByTime 测试指定时间的历史K线数据获取
func testHistoryKlineByTime(baseURL string) {
	// 获取24小时前的时间戳
	endTime := time.Now().Add(-24 * time.Hour).Unix()
	url := fmt.Sprintf("%s/api/v1/kline?codes=US:AAPL&kline_type=1001&count=50&end_time=%d", baseURL, endTime)

	start := time.Now()
	resp, err := http.Get(url)
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return
	}
	defer resp.Body.Close()

	duration := time.Since(start)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("读取响应失败: %v\n", err)
		return
	}

	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		fmt.Printf("解析JSON失败: %v\n", err)
		return
	}

	fmt.Printf("请求URL: %s\n", url)
	fmt.Printf("响应时间: %v\n", duration)
	fmt.Printf("状态码: %d\n", resp.StatusCode)
	fmt.Printf("响应消息: %v\n", result["msg"])

	if data, ok := result["data"].([]interface{}); ok {
		fmt.Printf("返回数据条数: %d\n", len(data))
	}

	fmt.Println("✓ 指定结束时间的请求应该使用历史K线接口")
}

// testHistoryAPI 测试专门的历史K线接口
func testHistoryAPI(baseURL string) {
	url := fmt.Sprintf("%s/api/v1/history?codes=US:AAPL&kline_type=1001&count=100", baseURL)

	start := time.Now()
	resp, err := http.Get(url)
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return
	}
	defer resp.Body.Close()

	duration := time.Since(start)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("读取响应失败: %v\n", err)
		return
	}

	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		fmt.Printf("解析JSON失败: %v\n", err)
		return
	}

	fmt.Printf("请求URL: %s\n", url)
	fmt.Printf("响应时间: %v\n", duration)
	fmt.Printf("状态码: %d\n", resp.StatusCode)
	fmt.Printf("响应消息: %v\n", result["msg"])

	if data, ok := result["data"].([]interface{}); ok {
		fmt.Printf("返回数据条数: %d\n", len(data))
	}

	fmt.Println("✓ 专门的历史K线接口，强制使用历史数据源")
}

// testMetrics 测试监控指标
func testMetrics(baseURL string) {
	url := fmt.Sprintf("%s/metrics", baseURL)

	resp, err := http.Get(url)
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("读取响应失败: %v\n", err)
		return
	}

	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		fmt.Printf("解析JSON失败: %v\n", err)
		return
	}

	fmt.Printf("请求URL: %s\n", url)
	fmt.Printf("状态码: %d\n", resp.StatusCode)

	// 显示延迟统计
	if latency, ok := result["latency"].(map[string]interface{}); ok {
		fmt.Println("延迟统计:")

		if recentKline, ok := latency["recent_kline_latency"].(map[string]interface{}); ok {
			if count, ok := recentKline["count"].(map[string]interface{}); ok {
				fmt.Printf("  最近K线请求次数: %v\n", count["total"])
			}
		}

		if historyKline, ok := latency["history_kline_latency"].(map[string]interface{}); ok {
			if count, ok := historyKline["count"].(map[string]interface{}); ok {
				fmt.Printf("  历史K线请求次数: %v\n", count["total"])
			}
		}

		if cache, ok := latency["cache_latency"].(map[string]interface{}); ok {
			if count, ok := cache["count"].(map[string]interface{}); ok {
				fmt.Printf("  缓存访问次数: %v\n", count["total"])
			}
		}
	}

	fmt.Println("✓ 可以通过/metrics端点查看详细的性能指标")
}
