package main

import (
	"fmt"
	"time"

	"qos-market-api/internal/cache"
	"qos-market-api/internal/config"
	"qos-market-api/internal/logger"
	"qos-market-api/internal/stats"
)

// MockLogger 模拟日志器
type MockLogger struct{}

func (m *<PERSON>ckLogger) Debug(args ...interface{})                              {}
func (m *<PERSON>ckLogger) Debugf(format string, args ...interface{})              {}
func (m *MockLogger) Info(args ...interface{})                               {}
func (m *MockLogger) Infof(format string, args ...interface{})               {}
func (m *MockLogger) Warn(args ...interface{})                               {}
func (m *MockLogger) Warnf(format string, args ...interface{})               {}
func (m *MockLogger) Error(args ...interface{})                              {}
func (m *<PERSON>ckLogger) Errorf(format string, args ...interface{})              {}
func (m *<PERSON><PERSON><PERSON>ogger) Fatal(args ...interface{})                              {}
func (m *<PERSON>ckLogger) Fatalf(format string, args ...interface{})              {}
func (m *MockLogger) WithField(key string, value interface{}) logger.Logger  { return m }
func (m *MockLogger) WithFields(fields map[string]interface{}) logger.Logger { return m }

// MockQOSClient 模拟QOS客户端
type MockQOSClient struct{}

func (m *MockQOSClient) IsConnected() bool { return true }

func statsDemo() {
	fmt.Println("=== QOS Market API 统计功能演示 ===")
	fmt.Println()

	// 1. 演示统计收集器的基本功能
	fmt.Println("1. 统计收集器基本功能演示:")
	collector := stats.NewStatsCollector()

	// 模拟一些缓存操作
	fmt.Println("   模拟缓存操作...")
	collector.RecordCacheHit()
	collector.RecordCacheHit()
	collector.RecordCacheMiss()
	collector.RecordCacheHit()

	// 模拟一些HTTP调用
	fmt.Println("   模拟QOS HTTP调用...")
	collector.RecordQOSHTTPCall()
	collector.RecordQOSHTTPCall()
	collector.RecordQOSHTTPCall()

	// 获取统计数据
	cacheStats := collector.GetCacheStats()
	apiStats := collector.GetQOSAPIStats()

	fmt.Printf("   缓存命中: %d 次\n", cacheStats.Hits.Total)
	fmt.Printf("   缓存未命中: %d 次\n", cacheStats.Misses.Total)
	fmt.Printf("   缓存命中率: %.1f%%\n", cacheStats.HitRate)
	fmt.Printf("   QOS HTTP调用: %d 次\n", apiStats.HTTPCalls.Total)
	fmt.Println()

	// 2. 演示时间窗口统计
	fmt.Println("2. 时间窗口统计演示:")
	timeCollector := stats.NewTimeWindowCollector()

	// 记录一些事件
	fmt.Println("   记录事件...")
	for i := 0; i < 5; i++ {
		timeCollector.Record()
		time.Sleep(100 * time.Millisecond) // 短暂延迟
	}

	windowStats := timeCollector.GetStats()
	fmt.Printf("   总次数: %d\n", windowStats.Total)
	fmt.Printf("   最近1分钟: %d\n", windowStats.LastMinute)
	fmt.Printf("   最近1小时: %d\n", windowStats.LastHour)
	fmt.Printf("   最近24小时: %d\n", windowStats.Last24Hours)
	fmt.Println()

	// 3. 演示缓存统计集成
	fmt.Println("3. 缓存统计集成演示:")

	// 创建配置
	cfg := &config.CacheConfig{
		Memory: config.MemoryCacheConfig{
			MaxSize:         100,
			TTL:             5 * time.Minute,
			CleanupInterval: time.Minute,
		},
	}

	// 创建组件
	log := &MockLogger{}
	klineConfig := &config.KlineConfig{}
	cacheManager := cache.NewCacheManager(cfg, klineConfig, log)

	// 创建统计收集器
	statsCollector := stats.NewStatsCollector()

	// 设置统计记录器
	cacheManager.SetStatsRecorder(statsCollector)

	fmt.Println("   模拟实际使用场景...")

	// 模拟实际使用场景
	symbols := []string{"US:AAPL", "US:GOOGL", "US:MSFT", "HK:700", "HK:9988"}

	for i := 0; i < 10; i++ {
		symbol := symbols[i%len(symbols)]

		// 模拟缓存查询
		cacheKey := fmt.Sprintf("snapshot_%s", symbol)
		if _, found := cacheManager.Get(cacheKey); !found {
			// 缓存未命中，设置新数据
			cacheManager.Set(cacheKey, fmt.Sprintf("data_%s_%d", symbol, i), "snapshot")
			// 模拟QOS HTTP调用
			statsCollector.RecordQOSHTTPCall()
		}

		// 再次查询（应该命中缓存）
		cacheManager.Get(cacheKey)

		time.Sleep(50 * time.Millisecond)
	}

	// 获取最终统计结果
	finalCacheStats := statsCollector.GetCacheStats()
	finalAPIStats := statsCollector.GetQOSAPIStats()

	// 显示统计结果
	fmt.Println("\n   === 最终统计结果 ===")
	fmt.Printf("   缓存命中总数: %d\n", finalCacheStats.Hits.Total)
	fmt.Printf("   缓存未命中总数: %d\n", finalCacheStats.Misses.Total)
	fmt.Printf("   缓存命中率: %.1f%%\n", finalCacheStats.HitRate)
	fmt.Printf("   QOS HTTP调用总数: %d\n", finalAPIStats.HTTPCalls.Total)
	fmt.Printf("   最近1分钟调用: %d\n", finalAPIStats.HTTPCalls.LastMinute)
	fmt.Printf("   最近1小时调用: %d\n", finalAPIStats.HTTPCalls.LastHour)
	fmt.Printf("   最近24小时调用: %d\n", finalAPIStats.HTTPCalls.Last24Hours)

	fmt.Println("\n=== 演示完成 ===")
}
